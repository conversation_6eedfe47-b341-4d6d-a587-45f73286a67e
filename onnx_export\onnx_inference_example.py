#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型推理示例
演示如何使用转换后的ONNX模型进行雷达干扰决策
"""

import os
import sys
import numpy as np
import json
from typing import Dict, List, Tuple

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("错误: ONNXRuntime未安装，请运行: pip install onnxruntime")
    sys.exit(1)


class ONNXJammingPredictor:
    """ONNX雷达干扰决策预测器"""
    
    def __init__(self, model_path: str):
        """
        初始化预测器
        
        Args:
            model_path: ONNX模型文件路径
        """
        self.model_path = model_path
        self.session = None
        self.input_name = None
        self.output_names = []
        
        self._load_model()
    
    def _load_model(self):
        """加载ONNX模型"""
        print(f"加载ONNX模型: {self.model_path}")
        
        try:
            self.session = ort.InferenceSession(self.model_path)
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            
            print(f"✅ 模型加载成功")
            print(f"   输入: {self.input_name}")
            print(f"   输出: {len(self.output_names)} 个")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def preprocess_radar_state(self, radar_state: Dict) -> np.ndarray:
        """
        预处理雷达状态数据
        
        Args:
            radar_state: 雷达状态字典
            
        Returns:
            归一化后的状态向量
        """
        # 提取并归一化雷达参数
        state_vector = np.array([
            radar_state.get('frequency', 10e9) / 1e10,      # 频率归一化 (0-20GHz)
            radar_state.get('pulse_width', 1e-6) * 1e6,     # 脉宽转换为微秒
            radar_state.get('prt', 100e-6) / 1000,          # PRT转换为毫秒
            radar_state.get('power', 1e6) / 1e7,            # 功率归一化 (0-10MW)
            radar_state.get('distance', 50) / 100,          # 距离归一化 (0-100km)
            radar_state.get('speed', 300) / 500,            # 速度归一化 (0-500m/s)
            radar_state.get('direction', 90) / 180,         # 方向归一化 (0-180度)
            radar_state.get('work_mode', 2) / 4,            # 工作模式归一化 (0-4)
            radar_state.get('threat_level', 3) / 5,         # 威胁等级归一化 (1-5)
            radar_state.get('threat_value', 0.5),           # 威胁值 (0-1)
            radar_state.get('threat_confidence', 0.7),      # 威胁置信度 (0-1)
            radar_state.get('threat_urgency', 0.6),         # 威胁紧急度 (0-1)
        ], dtype=np.float32)
        
        return state_vector.reshape(1, -1)  # 添加batch维度
    
    def predict(self, radar_state: Dict) -> Dict:
        """
        预测干扰决策
        
        Args:
            radar_state: 雷达状态字典
            
        Returns:
            干扰决策结果
        """
        # 预处理输入
        input_data = self.preprocess_radar_state(radar_state)
        
        # 运行推理
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        
        # 解析输出
        result = self._parse_outputs(outputs)
        
        return result
    
    def _parse_outputs(self, outputs: List[np.ndarray]) -> Dict:
        """
        解析模型输出
        
        Args:
            outputs: 模型输出列表
            
        Returns:
            解析后的结果字典
        """
        result = {}
        
        # 输出名称映射
        output_mapping = {
            'jamming_type_probs': '干扰类型概率分布',
            'combination_scores': '组合干扰评估概率分布',
            'comb_params': '梳状谱干扰参数',
            'isrj_params': '间歇采样转发参数',
            'broadband_params': '宽带阻塞噪声参数',
            'smart_noise_params': '灵巧噪声参数',
            'deception_params': '拖引干扰参数'
        }
        
        # 干扰类型名称
        jamming_types = ['无干扰', '间歇采样', '宽带噪声', '灵巧噪声', '拖引']
        
        for i, (name, output) in enumerate(zip(self.output_names, outputs)):
            output_flat = output.flatten()
            
            if name == 'jamming_type_probs':
                # 干扰类型概率分布
                probs = output_flat
                recommended_type = np.argmax(probs)
                
                result['jamming_decision'] = {
                    'recommended_type': jamming_types[recommended_type],
                    'recommended_type_id': int(recommended_type),
                    'confidence': float(probs[recommended_type]),
                    'all_probabilities': {
                        jamming_types[j]: float(probs[j]) for j in range(len(jamming_types))
                    }
                }
                
            elif name == 'combination_scores':
                # 组合干扰评估
                scores = output_flat
                best_combination = np.argmax(scores)
                
                result['combination_assessment'] = {
                    'best_combination_id': int(best_combination),
                    'best_score': float(scores[best_combination]),
                    'all_scores': scores.tolist()
                }
                
            else:
                # 各种干扰参数
                result[name] = {
                    'description': output_mapping.get(name, '未知参数'),
                    'values': output_flat.tolist(),
                    'shape': output.shape,
                    'range': [float(output_flat.min()), float(output_flat.max())]
                }
        
        return result
    
    def predict_with_interpretation(self, radar_state: Dict) -> Dict:
        """
        预测并解释结果
        
        Args:
            radar_state: 雷达状态字典
            
        Returns:
            包含解释的预测结果
        """
        # 基本预测
        result = self.predict(radar_state)
        
        # 添加解释
        interpretation = self._interpret_results(result, radar_state)
        result['interpretation'] = interpretation
        
        return result
    
    def _interpret_results(self, result: Dict, radar_state: Dict) -> Dict:
        """
        解释预测结果
        
        Args:
            result: 预测结果
            radar_state: 原始雷达状态
            
        Returns:
            解释信息
        """
        interpretation = {}
        
        # 解释干扰决策
        if 'jamming_decision' in result:
            decision = result['jamming_decision']
            recommended_type = decision['recommended_type']
            confidence = decision['confidence']
            
            if recommended_type == '无干扰':
                interpretation['action'] = "建议不进行干扰"
                interpretation['reason'] = f"模型认为当前威胁等级较低 (置信度: {confidence:.2f})"
            else:
                interpretation['action'] = f"建议使用{recommended_type}干扰"
                interpretation['reason'] = f"基于当前雷达特征，{recommended_type}最有效 (置信度: {confidence:.2f})"
        
        # 解释威胁评估
        threat_level = radar_state.get('threat_level', 3)
        if threat_level >= 4:
            interpretation['threat_assessment'] = "高威胁目标，建议立即干扰"
        elif threat_level >= 3:
            interpretation['threat_assessment'] = "中等威胁目标，建议准备干扰"
        else:
            interpretation['threat_assessment'] = "低威胁目标，可选择性干扰"
        
        # 解释雷达特征
        frequency = radar_state.get('frequency', 10e9) / 1e9
        if frequency > 15:
            interpretation['frequency_band'] = "Ku波段雷达，建议使用宽带噪声干扰"
        elif frequency > 8:
            interpretation['frequency_band'] = "X波段雷达，建议使用灵巧噪声干扰"
        else:
            interpretation['frequency_band'] = "S波段雷达，建议使用间歇采样干扰"
        
        return interpretation


def create_example_scenarios() -> List[Dict]:
    """创建示例场景"""
    scenarios = [
        {
            'name': '高威胁制导雷达',
            'radar_state': {
                'frequency': 10e9,      # 10 GHz
                'pulse_width': 1e-6,    # 1 μs
                'prt': 100e-6,          # 100 μs
                'power': 5e6,           # 5 MW
                'distance': 30,         # 30 km
                'speed': 400,           # 400 m/s
                'direction': 45,        # 45°
                'work_mode': 4,         # 制导模式
                'threat_level': 5,      # 最高威胁
                'threat_value': 0.9,
                'threat_confidence': 0.8,
                'threat_urgency': 0.9
            }
        },
        {
            'name': '中威胁搜索雷达',
            'radar_state': {
                'frequency': 3e9,       # 3 GHz
                'pulse_width': 2e-6,    # 2 μs
                'prt': 1000e-6,         # 1000 μs
                'power': 1e6,           # 1 MW
                'distance': 80,         # 80 km
                'speed': 200,           # 200 m/s
                'direction': 90,        # 90°
                'work_mode': 1,         # 搜索模式
                'threat_level': 3,      # 中等威胁
                'threat_value': 0.6,
                'threat_confidence': 0.7,
                'threat_urgency': 0.5
            }
        },
        {
            'name': '低威胁导航雷达',
            'radar_state': {
                'frequency': 9.4e9,     # 9.4 GHz
                'pulse_width': 0.5e-6,  # 0.5 μs
                'prt': 2000e-6,         # 2000 μs
                'power': 0.1e6,         # 0.1 MW
                'distance': 15,         # 15 km
                'speed': 100,           # 100 m/s
                'direction': 120,       # 120°
                'work_mode': 0,         # 导航模式
                'threat_level': 2,      # 低威胁
                'threat_value': 0.3,
                'threat_confidence': 0.6,
                'threat_urgency': 0.2
            }
        }
    ]
    
    return scenarios


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ONNX模型推理示例')
    parser.add_argument('--model', '-m', type=str,
                        default='models/onnx/jamming_model_ppo.onnx',
                        help='ONNX模型文件路径')
    parser.add_argument('--scenario', '-s', type=int, default=-1,
                        help='运行指定场景 (0-2, -1=运行所有场景)')
    parser.add_argument('--custom', '-c', action='store_true',
                        help='使用自定义输入')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        print("请先运行模型转换脚本生成ONNX模型")
        return 1
    
    try:
        # 创建预测器
        predictor = ONNXJammingPredictor(args.model)
        
        if args.custom:
            # 自定义输入
            print("\n请输入雷达参数 (按回车使用默认值):")
            
            radar_state = {}
            radar_state['frequency'] = float(input("频率 (Hz, 默认10e9): ") or "10e9")
            radar_state['pulse_width'] = float(input("脉宽 (s, 默认1e-6): ") or "1e-6")
            radar_state['prt'] = float(input("PRT (s, 默认100e-6): ") or "100e-6")
            radar_state['power'] = float(input("功率 (W, 默认1e6): ") or "1e6")
            radar_state['distance'] = float(input("距离 (km, 默认50): ") or "50")
            radar_state['speed'] = float(input("速度 (m/s, 默认300): ") or "300")
            radar_state['direction'] = float(input("方向 (度, 默认90): ") or "90")
            radar_state['work_mode'] = int(input("工作模式 (0-4, 默认2): ") or "2")
            radar_state['threat_level'] = int(input("威胁等级 (1-5, 默认3): ") or "3")
            radar_state['threat_value'] = float(input("威胁值 (0-1, 默认0.5): ") or "0.5")
            radar_state['threat_confidence'] = float(input("威胁置信度 (0-1, 默认0.7): ") or "0.7")
            radar_state['threat_urgency'] = float(input("威胁紧急度 (0-1, 默认0.6): ") or "0.6")
            
            # 运行预测
            print("\n" + "="*60)
            print("自定义场景预测结果")
            print("="*60)
            
            result = predictor.predict_with_interpretation(radar_state)
            print_prediction_result("自定义场景", radar_state, result)
            
        else:
            # 运行示例场景
            scenarios = create_example_scenarios()
            
            if args.scenario >= 0 and args.scenario < len(scenarios):
                # 运行指定场景
                scenario = scenarios[args.scenario]
                print(f"\n运行场景: {scenario['name']}")
                result = predictor.predict_with_interpretation(scenario['radar_state'])
                print_prediction_result(scenario['name'], scenario['radar_state'], result)
            else:
                # 运行所有场景
                print("\n" + "="*60)
                print("运行示例场景")
                print("="*60)
                
                for i, scenario in enumerate(scenarios):
                    print(f"\n场景 {i+1}: {scenario['name']}")
                    print("-" * 40)
                    
                    result = predictor.predict_with_interpretation(scenario['radar_state'])
                    print_prediction_result(scenario['name'], scenario['radar_state'], result)
        
        return 0
        
    except Exception as e:
        print(f"推理失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def print_prediction_result(scenario_name: str, radar_state: Dict, result: Dict):
    """打印预测结果"""
    print(f"\n📡 雷达参数:")
    print(f"   频率: {radar_state['frequency']/1e9:.1f} GHz")
    print(f"   脉宽: {radar_state['pulse_width']*1e6:.1f} μs")
    print(f"   功率: {radar_state['power']/1e6:.1f} MW")
    print(f"   距离: {radar_state['distance']} km")
    print(f"   威胁等级: {radar_state['threat_level']}")
    
    if 'jamming_decision' in result:
        decision = result['jamming_decision']
        print(f"\n🎯 干扰决策:")
        print(f"   推荐类型: {decision['recommended_type']}")
        print(f"   置信度: {decision['confidence']:.2f}")
        
        print(f"\n📊 所有类型概率:")
        for jamming_type, prob in decision['all_probabilities'].items():
            bar = "█" * int(prob * 20)
            print(f"   {jamming_type:8s}: {prob:.3f} {bar}")
    
    if 'interpretation' in result:
        interp = result['interpretation']
        print(f"\n💡 决策解释:")
        if 'action' in interp:
            print(f"   行动建议: {interp['action']}")
        if 'reason' in interp:
            print(f"   决策理由: {interp['reason']}")
        if 'threat_assessment' in interp:
            print(f"   威胁评估: {interp['threat_assessment']}")
        if 'frequency_band' in interp:
            print(f"   频段分析: {interp['frequency_band']}")


if __name__ == "__main__":
    exit(main())
