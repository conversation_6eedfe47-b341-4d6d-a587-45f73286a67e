#!/usr/bin/env python3
"""
PPO模型转换为ONNX格式
支持Actor和Critic网络的独立转换和联合转换
"""

import os
import sys
import torch
import numpy as np
from typing import Dict, Optional, Tuple

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_modules.system_integration.gpu_rl_accelerator import ActorCriticAccelerator, ActorNetwork, PPOCriticNetwork
from complete_jamming_system import SystemConfig

def load_trained_model(model_path: str) -> ActorCriticAccelerator:
    """加载训练好的PPO模型"""
    print(f"加载PPO模型: {model_path}")
    
    # 创建配置
    config = {
        'enable_gpu': True,
        'gpu_device': 0,
        'use_mixed_precision': False,  # ONNX转换时关闭混合精度
        'batch_size': 64,
        'state_dim': 12,
        'action_dim': 52,
        'actor_lr': 0.0003,
        'critic_lr': 0.001,
        'gamma': 0.99,
        'ppo_epochs': 4,
        'ppo_clip': 0.2,
        'entropy_coef': 0.01,
        'value_loss_coef': 0.5,
        'max_grad_norm': 0.5
    }
    
    # 初始化加速器
    accelerator = ActorCriticAccelerator(config)
    
    # 加载模型权重
    if not accelerator.load_model(model_path):
        raise ValueError(f"无法加载模型: {model_path}")
    
    # 设置为评估模式
    accelerator.actor.eval()
    accelerator.critic.eval()
    
    print("✓ 模型加载成功")
    return accelerator

def convert_actor_to_onnx(actor_model: ActorNetwork, output_path: str, 
                         state_dim: int = 12, batch_size: int = 1) -> bool:
    """将Actor网络转换为ONNX格式"""
    try:
        print(f"转换Actor网络到ONNX: {output_path}")
        
        # 创建示例输入
        dummy_input = torch.randn(batch_size, state_dim, dtype=torch.float32)
        
        # 如果模型在GPU上，将输入也移到GPU
        if next(actor_model.parameters()).is_cuda:
            dummy_input = dummy_input.cuda()
        
        # 设置动态轴（支持不同批次大小）
        dynamic_axes = {
            'input': {0: 'batch_size'},
            'threat_level_probs': {0: 'batch_size'},
            'jamming_type_probs': {0: 'batch_size'},
            'comb_params': {0: 'batch_size'},
            'isrj_params': {0: 'batch_size'},
            'broadband_params': {0: 'batch_size'},
            'smart_noise_params': {0: 'batch_size'},
            'deception_params': {0: 'batch_size'}
        }
        
        # 导出ONNX
        torch.onnx.export(
            actor_model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=[
                'threat_level_probs',
                'jamming_type_probs', 
                'comb_params',
                'isrj_params',
                'broadband_params',
                'smart_noise_params',
                'deception_params'
            ],
            dynamic_axes=dynamic_axes,
            verbose=False
        )
        
        print(f"✓ Actor网络已转换为ONNX: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ Actor网络转换失败: {e}")
        return False

def convert_critic_to_onnx(critic_model: PPOCriticNetwork, output_path: str,
                          state_dim: int = 12, batch_size: int = 1) -> bool:
    """将Critic网络转换为ONNX格式"""
    try:
        print(f"转换Critic网络到ONNX: {output_path}")
        
        # 创建示例输入
        dummy_input = torch.randn(batch_size, state_dim, dtype=torch.float32)
        
        # 如果模型在GPU上，将输入也移到GPU
        if next(critic_model.parameters()).is_cuda:
            dummy_input = dummy_input.cuda()
        
        # 设置动态轴
        dynamic_axes = {
            'input': {0: 'batch_size'},
            'value': {0: 'batch_size'}
        }
        
        # 导出ONNX
        torch.onnx.export(
            critic_model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['value'],
            dynamic_axes=dynamic_axes,
            verbose=False
        )
        
        print(f"✓ Critic网络已转换为ONNX: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ Critic网络转换失败: {e}")
        return False

class CombinedPPOModel(torch.nn.Module):
    """组合的PPO模型，包含Actor和Critic"""
    
    def __init__(self, actor: ActorNetwork, critic: PPOCriticNetwork):
        super(CombinedPPOModel, self).__init__()
        self.actor = actor
        self.critic = critic
    
    def forward(self, state):
        """前向传播，返回Actor和Critic的输出"""
        actor_output = self.actor(state)
        critic_output = self.critic(state)
        
        return (
            actor_output['threat_level_probs'],
            actor_output['jamming_type_probs'],
            actor_output['comb_params'],
            actor_output['isrj_params'],
            actor_output['broadband_params'],
            actor_output['smart_noise_params'],
            actor_output['deception_params'],
            critic_output
        )

def convert_combined_to_onnx(actor_model: ActorNetwork, critic_model: PPOCriticNetwork,
                           output_path: str, state_dim: int = 12, batch_size: int = 1) -> bool:
    """将Actor和Critic组合转换为单个ONNX模型"""
    try:
        print(f"转换组合PPO模型到ONNX: {output_path}")
        
        # 创建组合模型
        combined_model = CombinedPPOModel(actor_model, critic_model)
        combined_model.eval()
        
        # 创建示例输入
        dummy_input = torch.randn(batch_size, state_dim, dtype=torch.float32)
        
        # 如果模型在GPU上，将输入也移到GPU
        if next(actor_model.parameters()).is_cuda:
            dummy_input = dummy_input.cuda()
            combined_model = combined_model.cuda()
        
        # 设置动态轴
        dynamic_axes = {
            'input': {0: 'batch_size'},
            'threat_level_probs': {0: 'batch_size'},
            'jamming_type_probs': {0: 'batch_size'},
            'comb_params': {0: 'batch_size'},
            'isrj_params': {0: 'batch_size'},
            'broadband_params': {0: 'batch_size'},
            'smart_noise_params': {0: 'batch_size'},
            'deception_params': {0: 'batch_size'},
            'state_value': {0: 'batch_size'}
        }
        
        # 导出ONNX
        torch.onnx.export(
            combined_model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=[
                'threat_level_probs',
                'jamming_type_probs',
                'comb_params',
                'isrj_params',
                'broadband_params',
                'smart_noise_params',
                'deception_params',
                'state_value'
            ],
            dynamic_axes=dynamic_axes,
            verbose=False
        )
        
        print(f"✓ 组合PPO模型已转换为ONNX: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ 组合模型转换失败: {e}")
        return False

def verify_onnx_model(onnx_path: str, original_model, test_input: torch.Tensor) -> bool:
    """验证ONNX模型的正确性"""
    try:
        import onnx
        import onnxruntime as ort
        
        print(f"验证ONNX模型: {onnx_path}")
        
        # 检查ONNX模型
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("✓ ONNX模型结构检查通过")
        
        # 创建ONNX Runtime会话
        ort_session = ort.InferenceSession(onnx_path)
        
        # 准备输入数据
        if test_input.is_cuda:
            test_input_np = test_input.cpu().numpy()
        else:
            test_input_np = test_input.numpy()
        
        # ONNX推理
        ort_inputs = {ort_session.get_inputs()[0].name: test_input_np}
        ort_outputs = ort_session.run(None, ort_inputs)
        
        # 原始模型推理
        with torch.no_grad():
            if hasattr(original_model, 'forward'):
                torch_output = original_model(test_input)
            else:
                torch_output = original_model.actor(test_input)
        
        # 比较输出（简单检查第一个输出）
        if isinstance(torch_output, dict):
            torch_first_output = list(torch_output.values())[0]
        elif isinstance(torch_output, tuple):
            torch_first_output = torch_output[0]
        else:
            torch_first_output = torch_output
            
        if torch_first_output.is_cuda:
            torch_first_output = torch_first_output.cpu()
        
        diff = np.abs(ort_outputs[0] - torch_first_output.numpy()).max()
        
        if diff < 1e-5:
            print(f"✓ ONNX模型验证通过，最大差异: {diff:.2e}")
            return True
        else:
            print(f"✗ ONNX模型验证失败，最大差异: {diff:.2e}")
            return False
            
    except ImportError:
        print("⚠ 缺少onnx或onnxruntime库，跳过验证")
        return True
    except Exception as e:
        print(f"✗ ONNX模型验证失败: {e}")
        return False

def main():
    """主函数"""
    print("PPO模型ONNX转换工具")
    print("=" * 60)
    
    # 配置
    model_path = "models/jamming_model_ppo.pth"
    output_dir = "models/onnx"
    
    # 检查模型文件
    if not os.path.exists(model_path):
        print(f"✗ 模型文件不存在: {model_path}")
        print("请先训练模型或指定正确的模型路径")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 加载模型
        accelerator = load_trained_model(model_path)
        
        # 创建测试输入
        test_input = torch.randn(1, 12, dtype=torch.float32)
        if accelerator.gpu_enabled:
            test_input = test_input.cuda()
        
        # 转换Actor网络
        actor_onnx_path = os.path.join(output_dir, "ppo_actor.onnx")
        actor_success = convert_actor_to_onnx(
            accelerator.actor, actor_onnx_path, state_dim=12, batch_size=1
        )
        
        # 转换Critic网络
        critic_onnx_path = os.path.join(output_dir, "ppo_critic.onnx")
        critic_success = convert_critic_to_onnx(
            accelerator.critic, critic_onnx_path, state_dim=12, batch_size=1
        )
        
        # 转换组合模型
        combined_onnx_path = os.path.join(output_dir, "ppo_combined.onnx")
        combined_success = convert_combined_to_onnx(
            accelerator.actor, accelerator.critic, combined_onnx_path, state_dim=12, batch_size=1
        )
        
        # 验证模型
        if actor_success:
            verify_onnx_model(actor_onnx_path, accelerator, test_input)
        
        if critic_success:
            verify_onnx_model(critic_onnx_path, accelerator, test_input)
        
        # 总结
        print("\n" + "=" * 60)
        print("转换结果:")
        print(f"Actor网络: {'✓ 成功' if actor_success else '✗ 失败'}")
        print(f"Critic网络: {'✓ 成功' if critic_success else '✗ 失败'}")
        print(f"组合模型: {'✓ 成功' if combined_success else '✗ 失败'}")
        
        if actor_success or critic_success or combined_success:
            print(f"\nONNX模型已保存到: {output_dir}")
            print("\n使用方法:")
            print("1. 使用ONNX Runtime进行推理")
            print("2. 部署到边缘设备或嵌入式系统")
            print("3. 集成到C++/C#/Java等应用中")
        
    except Exception as e:
        print(f"✗ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
