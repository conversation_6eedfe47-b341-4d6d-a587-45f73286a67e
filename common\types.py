"""
雷达干扰系统类型定义
包含所有枚举类型和数据结构定义
"""

from enum import Enum


class RadarType(Enum):
    """雷达类型枚举"""
    GUIDANCE = 0           # 制导雷达
    FIRE_CONTROL = 1       # 火控雷达
    COMMAND_GUIDANCE = 2   # 引导指挥雷达
    SEARCH = 3             # 搜索雷达
    NAVIGATION = 4         # 导航雷达


class WorkMode(Enum):
    """工作模式枚举"""
    SILENT = 0     # 静默模式
    SEARCH = 1     # 搜索模式
    TRACK = 2      # 跟踪模式
    IMAGING = 3    # 成像模式
    GUIDANCE = 4   # 制导模式


class JammingType(Enum):
    """干扰类型枚举"""
    NO_JAMMING = 0              # 无干扰
    RANGE_DECEPTION = 1         # 距离拖引干扰
    VELOCITY_DECEPTION = 2      # 速度拖引干扰
    RANGE_VELOCITY_DECEPTION = 3 # 距离速度联合拖引
    COMB_SPECTRUM = 4           # 梳状谱干扰
    NOISE_AMPLITUDE = 5         # 噪声调幅干扰
    NOISE_FREQUENCY = 6         # 噪声调频干扰
    NOISE_PHASE = 7             # 噪声调相干扰
    SWEEP_FREQUENCY = 8         # 扫频噪声干扰
