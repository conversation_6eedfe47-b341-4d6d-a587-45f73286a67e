#ifndef THREAT_EVALUATOR_H
#define THREAT_EVALUATOR_H

#include "data_structures.h"

#ifdef __cplusplus
extern "C" {
#endif

// 威胁评估函数
RadarErrorCode evaluate_threat(const RadarParams* radar_params, ThreatAssessment* assessment);

// 计算威胁值
double calculate_threat_value(const RadarParams* radar_params);

// 确定威胁等级
int determine_threat_level(double threat_value);

#ifdef __cplusplus
}
#endif

#endif // THREAT_EVALUATOR_H