"""
雷达电子战Actor-Critic强化学习示例
演示如何使用Actor-Critic方法进行雷达干扰决策
"""

import sys
import os
import numpy as np
import time
from typing import Dict, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_modules.system_integration.gpu_rl_accelerator import ActorCriticAccelerator


def create_actor_critic_config() -> Dict:
    """创建Actor-Critic配置"""
    return {
        # GPU设置
        'enable_gpu': True,
        'gpu_device': 0,
        'use_mixed_precision': True,
        'batch_size': 64,
        
        # 网络结构
        'state_dim': 12,        # 雷达状态维度 (增加威胁评估输入)
        'action_dim': 52,       # 动作参数维度 (威胁等级5 + 干扰类型5 + 参数42)
        'hidden_dims': [256, 128, 64],
        
        # 学习参数
        'actor_lr': 0.0001,     # Actor学习率
        'critic_lr': 0.0002,    # Critic学习率
        'gamma': 0.99,          # 折扣因子
        'tau': 0.005,           # 软更新参数
        
        # 探索参数
        'noise_std': 0.2,       # 初始噪声标准差
        'noise_decay': 0.9995,  # 噪声衰减
        'noise_min': 0.01,      # 最小噪声
        
        # 经验回放
        'memory_size': 50000,
    }


def generate_radar_scenario() -> Dict:
    """生成随机雷达场景"""
    scenarios = [
        # 制导雷达场景
        {
            'frequency': np.random.uniform(8000, 12000),    # 8-12 GHz
            'pw': np.random.uniform(0.1, 2.0),             # 0.1-2 μs
            'prt': np.random.uniform(50, 200),             # 50-200 μs
            'power': np.random.uniform(1e5, 1e7),          # 100kW-10MW
            'distance': np.random.uniform(10, 80),         # 10-80 km
            'speed': np.random.uniform(200, 600),          # 200-600 m/s
            'direction': np.random.uniform(0, 360),        # 0-360度
            'work_mode': 4,  # 制导模式
            'threat_level': np.random.randint(1, 5),       # 威胁等级1-5
            'threat_value': np.random.uniform(0.3, 0.9)
        },
        # 搜索雷达场景
        {
            'frequency': np.random.uniform(2000, 6000),     # 2-6 GHz
            'pw': np.random.uniform(1.0, 10.0),            # 1-10 μs
            'prt': np.random.uniform(100, 500),            # 100-500 μs
            'power': np.random.uniform(1e6, 5e6),          # 1-5 MW
            'distance': np.random.uniform(50, 200),        # 50-200 km
            'speed': np.random.uniform(100, 400),          # 100-400 m/s
            'direction': np.random.uniform(0, 360),
            'work_mode': 1,  # 搜索模式
            'threat_level': np.random.randint(2, 4),
            'threat_value': np.random.uniform(0.2, 0.7)
        },
        # 跟踪雷达场景
        {
            'frequency': np.random.uniform(14000, 18000),   # 14-18 GHz
            'pw': np.random.uniform(0.5, 3.0),             # 0.5-3 μs
            'prt': np.random.uniform(80, 300),             # 80-300 μs
            'power': np.random.uniform(5e5, 2e6),          # 500kW-2MW
            'distance': np.random.uniform(20, 100),        # 20-100 km
            'speed': np.random.uniform(150, 500),          # 150-500 m/s
            'direction': np.random.uniform(0, 360),
            'work_mode': 2,  # 跟踪模式
            'threat_level': np.random.randint(2, 5),
            'threat_value': np.random.uniform(0.4, 0.8)
        }
    ]
    
    return scenarios[np.random.randint(0, len(scenarios))]


def calculate_jamming_reward(radar_state: Dict, action: Dict, jamming_effectiveness: float) -> float:
    """计算干扰奖励函数"""
    base_reward = 0.0

    # 威胁等级评估奖励
    predicted_threat = action.get('threat_level', 3)
    actual_threat = radar_state.get('threat_level', 3)
    threat_accuracy = 1.0 - abs(predicted_threat - actual_threat) / 5.0
    threat_reward = threat_accuracy * 0.3

    # 干扰类型适配奖励
    jamming_type = action.get('jamming_type', 0)
    work_mode = radar_state.get('work_mode', 1)

    # 不同工作模式适合不同干扰类型
    type_bonus = 0.0
    if work_mode == 4 and jamming_type in [1, 4]:  # 制导雷达适合间歇采样和拖引
        type_bonus = 0.4
    elif work_mode == 1 and jamming_type in [2, 3]:  # 搜索雷达适合噪声干扰
        type_bonus = 0.3
    elif work_mode == 2 and jamming_type in [0, 1]:  # 跟踪雷达适合梳状谱和间歇采样
        type_bonus = 0.35
    elif work_mode == 3 and jamming_type in [0, 3]:  # 成像雷达适合梳状谱和灵巧噪声
        type_bonus = 0.3

    # 干扰效果奖励
    effectiveness_reward = jamming_effectiveness * 0.5

    # 威胁等级匹配奖励 - 高威胁应该选择更有效的干扰类型
    if predicted_threat >= 4 and jamming_type in [1, 4]:  # 高威胁选择强干扰
        threat_match_bonus = 0.2
    elif predicted_threat <= 2 and jamming_type in [2, 3]:  # 低威胁选择温和干扰
        threat_match_bonus = 0.1
    else:
        threat_match_bonus = 0.0

    base_reward = threat_reward + type_bonus + effectiveness_reward + threat_match_bonus

    return np.clip(base_reward, -1.0, 1.0)


def simulate_jamming_effectiveness(radar_state: Dict, action: Dict) -> float:
    """模拟干扰效果"""
    # 基础效果
    base_effectiveness = 0.5

    # 频率匹配影响
    radar_freq = radar_state.get('frequency', 10000)
    freq_match = 1.0 - abs(radar_freq - 10000) / 20000  # 假设干扰频率为10GHz

    # 干扰类型效果
    jamming_type = action.get('jamming_type', 0)
    work_mode = radar_state.get('work_mode', 1)

    # 不同干扰类型对不同工作模式的效果
    type_effectiveness = {
        0: 0.7,  # 梳状谱
        1: 0.8,  # 间歇采样转发
        2: 0.6,  # 宽带阻塞噪声
        3: 0.75, # 灵巧噪声
        4: 0.85  # 拖引
    }

    # 工作模式匹配加成
    mode_bonus = 1.0
    if work_mode == 4 and jamming_type in [1, 4]:  # 制导雷达
        mode_bonus = 1.3
    elif work_mode == 1 and jamming_type in [2, 3]:  # 搜索雷达
        mode_bonus = 1.2
    elif work_mode == 2 and jamming_type in [0, 1]:  # 跟踪雷达
        mode_bonus = 1.25

    # 威胁等级影响 - 高威胁目标更难干扰
    threat_level = radar_state.get('threat_level', 3)
    threat_factor = 1.2 - (threat_level - 1) * 0.1  # 威胁越高，干扰效果略降

    effectiveness = (base_effectiveness *
                    freq_match *
                    type_effectiveness.get(jamming_type, 0.5) *
                    mode_bonus *
                    threat_factor)

    # 添加随机性
    effectiveness += np.random.normal(0, 0.1)

    return np.clip(effectiveness, 0.0, 1.0)


def train_actor_critic_example():
    """Actor-Critic训练示例"""
    print("雷达电子战Actor-Critic强化学习训练\n")
    
    # 创建配置
    config = create_actor_critic_config()
    
    # 初始化Actor-Critic加速器
    ac_accelerator = ActorCriticAccelerator(config)
    
    # 训练参数
    num_episodes = 1000
    max_steps_per_episode = 50
    
    # 训练统计
    episode_rewards = []
    training_losses = []
    
    print(f"开始训练 {num_episodes} 个回合...")
    
    for episode in range(num_episodes):
        episode_reward = 0.0
        episode_start_time = time.time()
        
        for step in range(max_steps_per_episode):
            # 生成雷达场景
            radar_state = generate_radar_scenario()
            
            # 选择动作
            action = ac_accelerator.select_action(radar_state, training=True)
            
            # 模拟干扰效果
            effectiveness = simulate_jamming_effectiveness(radar_state, action)
            
            # 计算奖励
            reward = calculate_jamming_reward(radar_state, action, effectiveness)
            episode_reward += reward
            
            # 生成下一个状态
            next_radar_state = generate_radar_scenario()
            done = (step == max_steps_per_episode - 1)
            
            # 存储经验
            ac_accelerator.store_experience(
                state=radar_state,
                action=action,
                reward=reward,
                next_state=next_radar_state,
                done=done
            )
            
            # 训练网络
            if len(ac_accelerator.memory) >= config['batch_size']:
                train_result = ac_accelerator.train_actor_critic()
                training_losses.append(train_result)
        
        episode_rewards.append(episode_reward)
        episode_time = time.time() - episode_start_time
        
        # 打印训练进度
        if episode % 50 == 0:
            avg_reward = np.mean(episode_rewards[-50:]) if len(episode_rewards) >= 50 else np.mean(episode_rewards)
            stats = ac_accelerator.get_statistics()
            
            print(f"回合 {episode:4d} | "
                  f"平均奖励: {avg_reward:6.3f} | "
                  f"噪声: {stats['noise_std']:.4f} | "
                  f"内存: {stats['memory_size']:5d} | "
                  f"时间: {episode_time:.2f}s")
    
    print(f"\n训练完成")
    print(f"最终平均奖励: {np.mean(episode_rewards[-100:]):.3f}")
    
    # 保存模型
    model_path = "models/actor_critic_radar_jamming.pth"
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    ac_accelerator.save_model(model_path)
    
    return ac_accelerator, episode_rewards, training_losses


def test_actor_critic_example():
    """测试训练好的Actor-Critic模型"""
    print("\n测试Actor-Critic模型\n")
    
    config = create_actor_critic_config()
    ac_accelerator = ActorCriticAccelerator(config)
    
    # 加载模型
    model_path = "models/actor_critic_radar_jamming.pth"
    if ac_accelerator.load_model(model_path):
        print("模型加载成功，开始测试...\n")
        
        # 测试几个场景
        for i in range(5):
            radar_state = generate_radar_scenario()
            action = ac_accelerator.select_action(radar_state, training=False)
            effectiveness = simulate_jamming_effectiveness(radar_state, action)
            
            print(f"测试场景 {i+1}:")
            print(f"  雷达频率: {radar_state['frequency']:.1f} MHz")
            print(f"  工作模式: {radar_state['work_mode']} (威胁等级: {radar_state['threat_level']})")
            print(f"  决策: {'干扰' if action['should_jam'] else '不干扰'}")
            if action['should_jam']:
                print(f"  干扰类型: {action['jamming_type']}")
                print(f"  干扰功率: {action['jamming_power']:.3f}")
                print(f"  干扰效果: {effectiveness:.3f}")
            print()
    else:
        print("模型加载失败，请先运行训练")


if __name__ == "__main__":
    # 训练模型
    ac_accelerator, rewards, losses = train_actor_critic_example()
    
    # 测试模型
    test_actor_critic_example()
