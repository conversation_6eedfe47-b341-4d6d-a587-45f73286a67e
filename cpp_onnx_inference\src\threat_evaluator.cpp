#include "threat_evaluator.h"
#include <math.h>

// 预定义的权重（基于训练时的AHP+熵权法计算结果）
// 这些权重对应：[速度, 距离, 方向, PRF, 频率, 脉宽, 工作模式]
static const double WEIGHTS[7] = {0.0892, 0.2678, 0.0892, 0.1785, 0.0714, 0.1071, 0.1968};

// 威胁等级阈值（基于MATLAB代码）
static const double THREAT_THRESHOLDS[5][2] = {
    {0.792, 1.000},  // 威胁等级1（极高威胁）
    {0.655, 0.792},  // 威胁等级2（高威胁）
    {0.421, 0.655},  // 威胁等级3（中等威胁）
    {0.210, 0.421},  // 威胁等级4（低威胁）
    {0.000, 0.210}   // 威胁等级5（极低威胁）
};

// 计算运动隶属度
static void calculate_movement_membership(const RadarParams* radar_params,
                                        double* speed_membership,
                                        double* distance_membership,
                                        double* direction_membership) {
    // 速度隶属度计算（按照MATLAB代码逻辑）
    double speed_ma = radar_params->speed / 340.0;  // 转换为马赫数
    if (speed_ma <= 3.0) {
        *speed_membership = speed_ma / 3.0;
    } else {
        *speed_membership = 1.0;
    }

    // 距离隶属度计算（按照MATLAB代码逻辑）
    double l1 = 30.0;  // 最大威胁距离 (km)
    double l2 = 300.0; // 最小威胁距离 (km)
    if (radar_params->distance <= l1) {
        *distance_membership = 1.0;
    } else if (radar_params->distance < l2) {
        *distance_membership = (l2 - radar_params->distance) / (l2 - l1);
    } else {
        *distance_membership = 0.0;
    }

    // 方向隶属度计算（按照MATLAB代码逻辑）
    double direction_rad = radar_params->direction * M_PI / 180.0;
    if (direction_rad >= 0 && direction_rad <= M_PI) {
        *direction_membership = 1.0 / (1.0 + pow(direction_rad / M_PI, 2));
    } else if (direction_rad >= -M_PI && direction_rad < 0) {
        *direction_membership = 1.0 / (1.0 + pow(fabs(direction_rad) / M_PI, 2));
    } else {
        *direction_membership = 0.0;
    }
}

// 威胁评估函数
RadarErrorCode evaluate_threat(const RadarParams* radar_params, ThreatAssessment* assessment) {
    if (!radar_params || !assessment) {
        return RADAR_ERROR_INVALID_PARAM;
    }

    // 计算威胁值
    assessment->threat_value = calculate_threat_value(radar_params);

    // 确定威胁等级
    assessment->threat_level = determine_threat_level(assessment->threat_value);

    // 设置置信度
    assessment->confidence = 0.85;

    // 设置优先级
    assessment->priority = assessment->threat_value;

    return RADAR_SUCCESS;
}

// 计算威胁值（基于训练时的算法）
double calculate_threat_value(const RadarParams* radar_params) {
    if (!radar_params) {
        return 0.0;
    }

    // 计算各项隶属度
    double speed_membership, distance_membership, direction_membership;
    calculate_movement_membership(radar_params, &speed_membership, &distance_membership, &direction_membership);

    // PRF隶属度计算（按照MATLAB代码逻辑）
    double prf_khz = 1000.0 / radar_params->prt;  // PRT(μs) -> PRF(kHz)
    double prf_membership;
    if (prf_khz <= 0.1) {
        prf_membership = 0.0;
    } else {
        prf_membership = 1.0 - exp(-5.0 * pow(prf_khz - 0.1, 2));
    }

    // 频率隶属度计算（按照MATLAB代码逻辑）
    double freq_ghz = radar_params->frequency / 1000.0;  // MHz -> GHz
    double freq_membership;
    if (freq_ghz > 8.0) {
        freq_membership = 1.0;
    } else if (freq_ghz > 2.0) {
        freq_membership = 0.6;
    } else if (freq_ghz > 0.3) {
        freq_membership = 0.3;
    } else if (freq_ghz > 0.03) {
        freq_membership = 0.1;
    } else {
        freq_membership = 0.0;
    }

    // 脉宽隶属度计算（按照MATLAB代码逻辑）
    double pw_membership = 1.0 / (1.0 + pow(0.1 * radar_params->pulse_width, 2));

    // 工作模式隶属度计算（按照MATLAB代码逻辑）
    double mode_membership;
    switch (radar_params->work_mode) {
        case 0:  // 静默模式
            mode_membership = 0.0;
            break;
        case 1:  // 搜索模式
            mode_membership = 0.2;
            break;
        case 2:  // 跟踪模式
            mode_membership = 0.8;
            break;
        case 3:  // 成像模式
            mode_membership = 0.5;
            break;
        case 4:  // 制导模式
            mode_membership = 1.0;
            break;
        default:
            mode_membership = 0.0;
            break;
    }

    // 计算加权威胁值
    double membership_values[7] = {
        speed_membership,
        distance_membership,
        direction_membership,
        prf_membership,
        freq_membership,
        pw_membership,
        mode_membership
    };

    double threat_value = 0.0;
    for (int i = 0; i < 7; i++) {
        threat_value += membership_values[i] * WEIGHTS[i];
    }

    // 确保威胁值在0-1范围内
    return fmin(fmax(threat_value, 0.0), 1.0);
}

// 确定威胁等级（按照MATLAB代码中的威胁等级阈值）
int determine_threat_level(double threat_value) {
    // 按照MATLAB代码中的威胁等级阈值：[0, 0.21, 0.421, 0.655, 0.792, 1]
    if (threat_value > 0.792 && threat_value <= 1.000) {
        return 1;  // 一级威胁（极高威胁）
    } else if (threat_value > 0.655 && threat_value <= 0.792) {
        return 2;  // 二级威胁（高威胁）
    } else if (threat_value > 0.421 && threat_value <= 0.655) {
        return 3;  // 三级威胁（中等威胁）
    } else if (threat_value > 0.210 && threat_value <= 0.421) {
        return 4;  // 四级威胁（低威胁）
    } else if (threat_value >= 0.000 && threat_value <= 0.210) {
        return 5;  // 五级威胁（极低威胁）
    } else {
        // 威胁度值超出范围，返回默认值
        return 5;
    }
}