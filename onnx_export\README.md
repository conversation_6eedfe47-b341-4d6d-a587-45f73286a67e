# 雷达干扰决策模型转换工具

本工具包提供了将PyTorch训练的雷达干扰决策模型转换为ONNX和RKNN格式的完整解决方案，确保输入输出与标准格式对应。

## 功能特性

- ✅ **PyTorch到ONNX转换**: 支持Actor-Critic和PPO模型
- ✅ **ONNX到RKNN转换**: 适配瑞芯微NPU (RK3588/RK3566/RK3568/RK3576)
- ✅ **模型验证**: 确保转换后模型输出一致性
- ✅ **标准化接口**: 统一的输入输出规格
- ✅ **量化支持**: 自动量化优化推理性能
- ✅ **部署包生成**: 包含使用示例和文档

## 安装依赖

### 基础依赖
```bash
pip install torch torchvision
pip install onnx onnxruntime
pip install numpy
```

### RKNN工具包 (可选)
```bash
# 从瑞芯微官网下载并安装 rknn-toolkit2
pip install rknn-toolkit2
```

## 快速开始

### 1. 基本转换
```bash
# 转换PPO模型到ONNX和RKNN
python onnx_export/model_converter.py --input models/jamming_model_ppo.pth

# 输出文件将保存在 converted_models/ 目录下
```

### 2. 指定输出目录和平台
```bash
python onnx_export/model_converter.py \
    --input models/jamming_model_ppo.pth \
    --output-dir my_models \
    --platform rk3588
```

### 3. 高级选项
```bash
# 跳过验证和量化
python onnx_export/model_converter.py \
    --input models/jamming_model_ppo.pth \
    --no-validate \
    --no-quantize

# 使用GPU加速转换
python onnx_export/model_converter.py \
    --input models/jamming_model_ppo.pth \
    --device cuda
```

## 单独使用各个工具

### PyTorch到ONNX
```bash
python onnx_export/pytorch_to_onnx.py \
    --input models/jamming_model_ppo.pth \
    --output-dir models/onnx
```

### ONNX到RKNN
```bash
python onnx_export/onnx_to_rknn.py \
    --input models/onnx/jamming_model_ppo.onnx \
    --output-dir models/rknn \
    --platform rk3588
```

### 模型验证
```bash
python onnx_export/model_validator.py \
    --pytorch models/jamming_model_ppo.pth \
    --onnx models/onnx/jamming_model_ppo.onnx \
    --rknn models/rknn/jamming_model_ppo_rk3588.rknn
```

## 输入输出规格

### 输入规格
- **名称**: `state`
- **形状**: `[batch_size, 12]`
- **类型**: `float32`
- **描述**: 雷达状态向量，包含12个归一化特征

#### 输入特征说明
1. 频率 (归一化后 0-2.0，对应0-20GHz)
2. 脉宽 (归一化后)
3. 脉冲重复间隔 (归一化后)
4. 功率 (归一化后)
5. 距离 (归一化后)
6. 速度 (归一化后)
7. 方向 (归一化后)
8. 工作模式 (归一化后)
9. 威胁等级 (归一化后)
10. 威胁值 (0-1)
11. 威胁置信度 (0-1)
12. 威胁紧急度 (0-1)

### 输出规格
1. **jamming_type_probs**: `[batch_size, 5]` - 干扰类型概率分布
   - [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]
2. **combination_scores**: `[batch_size, 10]` - 组合干扰评估概率分布
3. **comb_params**: `[batch_size, 25]` - 梳状谱干扰参数
4. **isrj_params**: `[batch_size, 6]` - 间歇采样转发参数
5. **broadband_params**: `[batch_size, 1]` - 宽带阻塞噪声参数
6. **smart_noise_params**: `[batch_size, 7]` - 灵巧噪声参数
7. **deception_params**: `[batch_size, 3]` - 拖引干扰参数

## 使用转换后的模型

### ONNX推理示例
```python
import onnxruntime as ort
import numpy as np

# 加载模型
session = ort.InferenceSession('jamming_model_ppo.onnx')

# 准备输入数据 (雷达状态向量)
input_data = np.array([[
    1.0,  # 频率 (归一化)
    0.5,  # 脉宽
    2.0,  # PRT
    0.8,  # 功率
    0.3,  # 距离
    0.6,  # 速度
    0.4,  # 方向
    0.7,  # 工作模式
    0.8,  # 威胁等级
    0.9,  # 威胁值
    0.7,  # 威胁置信度
    0.8   # 威胁紧急度
]], dtype=np.float32)

# 推理
outputs = session.run(None, {'state': input_data})

# 解析输出
jamming_type_probs = outputs[0]  # 干扰类型概率
combination_scores = outputs[1]  # 组合干扰评估
comb_params = outputs[2]         # 梳状谱参数
isrj_params = outputs[3]         # 间歇采样参数
broadband_params = outputs[4]    # 宽带噪声参数
smart_noise_params = outputs[5]  # 灵巧噪声参数
deception_params = outputs[6]    # 拖引参数

print(f"推荐干扰类型: {np.argmax(jamming_type_probs)}")
```

### RKNN推理示例
```python
from rknn.api import RKNN
import numpy as np

# 加载模型
rknn = RKNN()
rknn.load_rknn('jamming_model_ppo_rk3588.rknn')
rknn.init_runtime()

# 准备输入数据
input_data = np.array([[...]], dtype=np.float32)

# 推理
outputs = rknn.inference(inputs=[input_data])

# 处理输出
jamming_type_probs = outputs[0]
# ... 其他输出

# 释放资源
rknn.release()
```

## 输出文件结构

转换完成后，输出目录结构如下：
```
converted_models/
├── onnx/
│   ├── jamming_model_ppo.onnx      # ONNX模型文件
│   └── metadata.json               # ONNX元数据
├── rknn/
│   ├── jamming_model_ppo_rk3588.rknn  # RKNN模型文件
│   └── rknn_metadata.json         # RKNN元数据
├── validation_report.json          # 验证报告
├── deployment_info.json           # 部署信息
└── README.md                      # 使用说明
```

## 故障排除

### 常见问题

1. **RKNN工具包未安装**
   ```
   错误: RKNN工具包未安装，请安装rknn-toolkit2
   ```
   解决: 从瑞芯微官网下载并安装对应版本的rknn-toolkit2

2. **模型加载失败**
   ```
   错误: 加载模型失败
   ```
   解决: 检查模型文件路径和格式，确保是支持的PyTorch模型

3. **验证失败**
   ```
   错误: 模型输出不一致
   ```
   解决: 检查模型转换参数，可能需要调整容差或禁用量化

### 调试选项

- 使用 `--no-validate` 跳过验证步骤
- 使用 `--no-quantize` 禁用量化
- 检查生成的 `validation_report.json` 了解详细差异

## 技术支持

如有问题，请检查：
1. 依赖包是否正确安装
2. 输入模型文件是否完整
3. 目标平台是否支持
4. 验证报告中的错误信息

## 更新日志

- v1.0.0: 初始版本，支持基本的PyTorch->ONNX->RKNN转换
- 支持PPO和Actor-Critic模型
- 包含完整的验证和部署包生成功能
