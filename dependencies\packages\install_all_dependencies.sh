#!/bin/bash
# 雷达电子战智能干扰决策系统 - 依赖包安装脚本

echo "雷达电子战智能干扰决策系统 - 依赖包安装"
echo "============================================"

# 设置基础目录
BASE_DIR="packages"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 选择安装阶段
echo ""
echo "请选择要安装的依赖包:"
echo "1) 训练阶段依赖包"
echo "2) ONNX转换阶段依赖包"
echo "3) RKNN部署阶段依赖包"
echo "4) 安装所有依赖包"
echo "5) 退出"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "安装训练阶段依赖包..."
        cd $BASE_DIR/training_packages
        bash install_training_packages.sh
        ;;
    2)
        echo "安装ONNX转换阶段依赖包..."
        cd $BASE_DIR/onnx_packages
        bash install_onnx_packages.sh
        ;;
    3)
        echo "安装RKNN部署阶段依赖包..."
        cd $BASE_DIR/rknn_packages
        bash install_rknn_packages.sh
        ;;
    4)
        echo "安装所有依赖包..."
        
        echo "1/3 安装训练阶段依赖包..."
        cd $BASE_DIR/training_packages
        bash install_training_packages.sh
        
        echo "2/3 安装ONNX转换阶段依赖包..."
        cd $BASE_DIR/onnx_packages
        bash install_onnx_packages.sh
        
        echo "3/3 安装RKNN部署阶段依赖包..."
        cd $BASE_DIR/rknn_packages
        bash install_rknn_packages.sh
        
        echo "所有依赖包安装完成!"
        ;;
    5)
        echo "退出安装"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo "依赖包安装完成!"
