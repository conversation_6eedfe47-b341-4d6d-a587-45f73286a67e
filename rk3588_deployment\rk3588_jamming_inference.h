#ifndef RK3588_JAMMING_INFERENCE_H
#define RK3588_JAMMING_INFERENCE_H

#include <QObject>
#include <QVector>
#include <QMap>
#include <QString>
#include <QDebug>
#include <QMutex>
#include <memory>

// RKNN API头文件
extern "C" {
#include "rknn_api.h"
}

/**
 * @brief 雷达输入数据结构
 */
struct RadarInput {
    double frequency;    // 载频 (MHz)
    double pw;          // 脉宽 (μs)
    double prt;         // 脉冲重复周期 (μs)
    double power;       // 功率 (W)
    double distance;    // 距离 (km)
    double speed;       // 速度 (m/s)
    double direction;   // 方向 (度)
    int work_mode;      // 工作模式 (0-4)
    
    RadarInput() : frequency(10000.0), pw(1.0), prt(100.0), power(1e6),
                   distance(50.0), speed(300.0), direction(0.0), work_mode(1) {}
};

/**
 * @brief 威胁评估数据结构
 */
struct ThreatAssessment {
    int threat_level;           // 威胁等级 (1-5)
    double threat_value;        // 威胁值 (0-1)
    double confidence;          // 置信度 (0-1)
    double urgency;            // 紧急度 (0-1)
    QString threat_level_name; // 威胁等级名称
    
    // 详细威胁分析
    double radar_type_threat;     // 雷达类型威胁度
    double platform_type_threat; // 平台类型威胁度
    double distance_threat;       // 距离威胁度
    double frequency_threat;      // 载频威胁度
    double mode_threat;          // 工作模式威胁度
    
    ThreatAssessment() : threat_level(3), threat_value(0.5), confidence(0.8),
                        urgency(0.5), threat_level_name("中等威胁"),
                        radar_type_threat(0.5), platform_type_threat(0.5),
                        distance_threat(0.5), frequency_threat(0.5), mode_threat(0.5) {}
};

/**
 * @brief 干扰决策结果结构
 */
struct JammingDecision {
    bool should_jam;              // 是否干扰
    int jamming_type;            // 干扰类型 (0-4)
    QString jamming_type_name;   // 干扰类型名称
    double jamming_power;        // 干扰功率 (0-1)
    double jamming_frequency;    // 干扰频率 (Hz)
    int predicted_threat_level;  // 预测威胁等级
    
    // 概率分布
    QVector<double> threat_level_probs;  // 威胁等级概率分布
    QVector<double> jamming_type_probs;  // 干扰类型概率分布
    
    // 干扰参数
    QVector<double> comb_params;         // 梳状谱参数
    QVector<double> isrj_params;         // 间歇采样参数
    QVector<double> broadband_params;    // 宽带噪声参数
    QVector<double> smart_noise_params;  // 灵巧噪声参数
    QVector<double> deception_params;    // 拖引参数
    
    JammingDecision() : should_jam(false), jamming_type(0), jamming_type_name("无干扰"),
                       jamming_power(0.0), jamming_frequency(10e9), predicted_threat_level(3) {}
};

/**
 * @brief RK3588雷达干扰决策推理器
 */
class RK3588JammingInference : public QObject
{
    Q_OBJECT

public:
    explicit RK3588JammingInference(QObject *parent = nullptr);
    ~RK3588JammingInference();

    /**
     * @brief 初始化RKNN模型
     * @param model_path RKNN模型文件路径
     * @return 是否初始化成功
     */
    bool initializeModel(const QString& model_path);

    /**
     * @brief 威胁评估（复现训练时的算法）
     * @param radar_input 雷达输入数据
     * @return 威胁评估结果
     */
    ThreatAssessment assessThreat(const RadarInput& radar_input);

    /**
     * @brief 进行干扰决策推理
     * @param radar_input 雷达输入数据
     * @return 干扰决策结果
     */
    JammingDecision predict(const RadarInput& radar_input);

    /**
     * @brief 批量推理
     * @param radar_inputs 多个雷达输入
     * @return 多个决策结果
     */
    QVector<JammingDecision> batchPredict(const QVector<RadarInput>& radar_inputs);

    /**
     * @brief 检查模型是否已初始化
     * @return 模型状态
     */
    bool isModelReady() const { return model_initialized_; }

    /**
     * @brief 获取模型信息
     * @return 模型信息字符串
     */
    QString getModelInfo() const;

    /**
     * @brief 获取推理性能统计
     * @return 性能统计信息
     */
    QString getPerformanceStats() const;

signals:
    /**
     * @brief 推理完成信号
     * @param decision 决策结果
     */
    void inferenceCompleted(const JammingDecision& decision);

    /**
     * @brief 错误信号
     * @param error_message 错误信息
     */
    void errorOccurred(const QString& error_message);

private:
    // RKNN相关
    rknn_context rknn_ctx_;
    rknn_input_output_num io_num_;
    rknn_tensor_attr* input_attrs_;
    rknn_tensor_attr* output_attrs_;
    bool model_initialized_;

    // 性能统计
    mutable QMutex stats_mutex_;
    int inference_count_;
    double total_inference_time_;
    double min_inference_time_;
    double max_inference_time_;

    // 干扰类型映射
    QMap<int, QString> jamming_type_names_;

    /**
     * @brief 预处理雷达输入为模型输入
     * @param radar_input 雷达输入
     * @param threat_assessment 威胁评估结果
     * @return 12维归一化向量
     */
    QVector<float> preprocessInput(const RadarInput& radar_input, 
                                  const ThreatAssessment& threat_assessment);

    /**
     * @brief 后处理模型输出
     * @param outputs RKNN模型输出
     * @param radar_input 原始雷达输入
     * @param threat_assessment 威胁评估
     * @return 干扰决策结果
     */
    JammingDecision postprocessOutput(rknn_output* outputs, 
                                     const RadarInput& radar_input,
                                     const ThreatAssessment& threat_assessment);

    /**
     * @brief 隶属度计算函数（复现训练时的算法）
     */
    double calculateRadarTypeMembership(int radar_type);
    double calculatePlatformTypeMembership(int platform_type);
    double calculateSpeedMembership(double speed);
    double calculateDistanceMembership(double distance);
    double calculateDirectionMembership(double direction);
    double calculatePRFMembership(double prt);
    double calculateFrequencyMembership(double frequency);
    double calculatePulseWidthMembership(double pw);
    double calculateWorkModeMembership(int work_mode);

    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats(double inference_time);

    /**
     * @brief 释放RKNN资源
     */
    void releaseRKNN();
};

#endif // RK3588_JAMMING_INFERENCE_H
