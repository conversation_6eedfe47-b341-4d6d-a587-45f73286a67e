﻿#include "threat_evaluator.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>




ThreatEvaluator* threat_evaluator_create(void) {
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)malloc(sizeof(ThreatEvaluatorImpl));
    if (!impl) {
        return NULL;
    }
    

    impl->initialized = 1;
    impl->frequency_weight = 0.3;
    impl->power_weight = 0.4;
    impl->distance_weight = 0.2;
    impl->mode_weight = 0.1;
    

    return (ThreatEvaluator*)impl;
}


void threat_evaluator_destroy(ThreatEvaluator* evaluator) {
    if (evaluator) {
        free(evaluator);

    }
}



int threat_validate_radar_params(const RadarParameters* params) {
    if (!params) return 0;

    if (params->frequency_mhz < 1000 || params->frequency_mhz > 20000) return 0;
    if (params->pulse_width_us < 0.1 || params->pulse_width_us > 100) return 0;
    if (params->power_w < 1000 || params->power_w > 10000000) return 0;
    if (params->distance_km < 1 || params->distance_km > 1000) return 0;
    if (params->work_mode < 0 || params->work_mode > 4) return 0;
    
    return 1;
}


static double calculate_frequency_threat(double frequency_mhz) {

    if (frequency_mhz >= 8000) {
        return 0.9;
    } else if (frequency_mhz >= 4000) {
        return 0.7;
    } else if (frequency_mhz >= 2000) {
        return 0.5;
    } else {
        return 0.3;
    }
}


static double calculate_power_threat(double power_w) {

    double log_power = log10(power_w);
    double normalized = (log_power - 3.0) / 4.0;
    return fmax(0.0, fmin(1.0, normalized));
}


static double calculate_distance_threat(double distance_km) {

    if (distance_km <= 50) {
        return 0.9;
    } else if (distance_km <= 100) {
        return 0.7;
    } else if (distance_km <= 200) {
        return 0.5;
    } else {
        return 0.2;
    }
}


static double calculate_mode_threat(int work_mode) {
    switch (work_mode) {
        case 0: return 0.1;
        case 1: return 0.4;
        case 2: return 0.8;
        case 3: return 0.9;
        case 4: return 0.95;
        default: return 0.3;
    }
}

int threat_evaluator_assess(ThreatEvaluator* evaluator, 
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result) {
    if (!evaluator || !radar_params || !result) {
        return -1;
    }
    
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)evaluator;
    if (!impl->initialized) {
        return -2;
    }
    
    if (!threat_validate_radar_params(radar_params)) {
        return -3;
    }
    

    memset(result, 0, sizeof(ThreatAssessmentData));
    

    result->frequency_threat = calculate_frequency_threat(radar_params->frequency_mhz);
    result->power_threat = calculate_power_threat(radar_params->power_w);
    result->distance_threat = calculate_distance_threat(radar_params->distance_km);
    result->mode_threat = calculate_mode_threat(radar_params->work_mode);

    result->threat_value = 
        result->frequency_threat * impl->frequency_weight +
        result->power_threat * impl->power_weight +
        result->distance_threat * impl->distance_weight +
        result->mode_threat * impl->mode_weight;
    

    result->threat_value = fmax(0.0, fmin(1.0, result->threat_value));
    

    if (result->threat_value >= 0.8) {
        result->threat_level = 1;
    } else if (result->threat_value >= 0.6) {
        result->threat_level = 2;
    } else if (result->threat_value >= 0.4) {
        result->threat_level = 3;
    } else if (result->threat_value >= 0.2) {
        result->threat_level = 4;
    } else {
        result->threat_level = 5;
    }
    

    double param_completeness = 1.0;
    double consistency_score = 1.0 - fabs(result->threat_value - 
        (result->frequency_threat + result->power_threat + 
         result->distance_threat + result->mode_threat) / 4.0);
    
    result->confidence = param_completeness * consistency_score * 0.9;
    result->confidence = fmax(0.5, fmin(1.0, result->confidence));


    result->priority = result->threat_value * result->confidence;
    result->priority = fmax(0.0, fmin(1.0, result->priority));


    
    return 0;
}


const char* threat_get_level_description(int threat_level) {
    switch (threat_level) {
        case 1: return "极度威胁";
        case 2: return "高威胁";
        case 3: return "中等威胁";
        case 4: return "低威胁";
        case 5: return "极低威胁";
        default: return "未知";
    }
}


double threat_calculate_composite_score(const ThreatAssessmentData* data) {
    if (!data) return 0.0;
    
    return (data->frequency_threat + data->power_threat + 
            data->distance_threat + data->mode_threat) / 4.0;
}
