"""
GPU加速信号处理模块
使用CuPy进行GPU并行信号处理，提升干扰信号生成和分析性能
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import time

try:
    import cupy as cp
    import cupyx.scipy.signal as cp_signal
    import cupyx.scipy.fft as cp_fft
    GPU_AVAILABLE = True
except ImportError:
    cp = np
    cp_signal = None
    cp_fft = None
    GPU_AVAILABLE = False


class GPUSignalProcessor:
    """GPU加速信号处理器"""
    
    def __init__(self, gpu_device: int = 0, enable_gpu: bool = True):
        """
        初始化GPU信号处理器
        
        Args:
            gpu_device: GPU设备ID
            enable_gpu: 是否启用GPU加速
        """
        self.gpu_enabled = enable_gpu and GPU_AVAILABLE
        self.gpu_device = gpu_device
        
        if self.gpu_enabled:
            try:
                cp.cuda.Device(gpu_device).use()
                # 预分配一些GPU内存
                _ = cp.zeros((1000, 1000), dtype=cp.complex64)
                print(f"GPU信号处理器初始化成功 (设备: {gpu_device})")
            except Exception as e:
                print(f"GPU初始化失败，使用CPU: {e}")
                self.gpu_enabled = False
        
        # 设置计算后端
        self.xp = cp if self.gpu_enabled else np
        self.signal_module = cp_signal if self.gpu_enabled else None
        self.fft_module = cp_fft if self.gpu_enabled else np.fft
        
        # 信号处理参数
        self.c0 = 3e8  # 光速
        self.default_fs = 1e9  # 默认采样率1GHz
    
    def generate_comb_spectrum_gpu(self, params: Dict, fs: float, f0: float, 
                                  duration: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        GPU加速梳状谱信号生成
        
        Args:
            params: 梳状谱参数
            fs: 采样频率
            f0: 载波频率
            duration: 信号持续时间
            
        Returns:
            (时间轴, 信号)
        """
        N = int(fs * duration)
        t = self.xp.arange(N) / fs
        
        # 提取参数
        count = params.get('count', 4)
        frequency_offsets = params.get('frequency_offsets', [100e3, 200e3, 300e3, 400e3])[:count]
        flicker_periods = params.get('flicker_periods', [20e-6, 25e-6, 30e-6, 35e-6])[:count]
        
        # 初始化信号
        signal = self.xp.zeros(N, dtype=self.xp.complex64)
        
        # 并行生成多个梳状谱分量
        for i in range(count):
            if i < len(frequency_offsets):
                freq_offset = frequency_offsets[i]
                flicker_period = flicker_periods[i] if i < len(flicker_periods) else 20e-6
                
                # 生成载波
                carrier = self.xp.exp(1j * 2 * self.xp.pi * (f0 + freq_offset) * t)
                
                # 生成闪烁调制
                flicker_freq = 1.0 / flicker_period
                flicker_mod = 0.5 * (1 + self.xp.cos(2 * self.xp.pi * flicker_freq * t))
                
                # 添加到总信号
                signal += carrier * flicker_mod / count
        
        # 转回CPU
        if self.gpu_enabled:
            t = cp.asnumpy(t)
            signal = cp.asnumpy(signal)
        
        return t, signal
    
    def generate_intermittent_sampling_gpu(self, params: Dict, radar_signal: np.ndarray,
                                         fs: float, duration: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        GPU加速间歇采样转发信号生成
        
        Args:
            params: 间歇采样参数
            radar_signal: 雷达信号
            fs: 采样频率
            duration: 信号持续时间
            
        Returns:
            (时间轴, 信号)
        """
        N = int(fs * duration)
        t = self.xp.arange(N) / fs
        
        # 将雷达信号转移到GPU
        if self.gpu_enabled:
            radar_signal_gpu = cp.asarray(radar_signal)
        else:
            radar_signal_gpu = radar_signal
        
        # 提取参数
        repeat_interval = params.get('repeat_interval', 100e-6)
        sampling_period = params.get('sampling_period', 1.5e-6)
        sampling_width = params.get('sampling_width', 0.8e-6)
        
        # 生成采样窗口
        sampling_freq = 1.0 / repeat_interval
        window_duty_cycle = sampling_width / repeat_interval
        
        # 生成周期性采样窗口
        window_signal = self.xp.where(
            (t % repeat_interval) < sampling_width, 1.0, 0.0
        )
        
        # 重复雷达信号以匹配时间长度
        radar_length = len(radar_signal_gpu)
        if N > radar_length:
            repeats = (N // radar_length) + 1
            extended_radar = self.xp.tile(radar_signal_gpu, repeats)[:N]
        else:
            extended_radar = radar_signal_gpu[:N]
        
        # 应用间歇采样
        sampled_signal = extended_radar * window_signal
        
        # 添加延迟转发
        delay_samples = int(params.get('delay_time', 1e-6) * fs)
        if delay_samples > 0 and delay_samples < N:
            delayed_signal = self.xp.zeros_like(sampled_signal)
            delayed_signal[delay_samples:] = sampled_signal[:-delay_samples]
            sampled_signal += delayed_signal * 0.8  # 延迟分量功率稍小
        
        # 转回CPU
        if self.gpu_enabled:
            t = cp.asnumpy(t)
            sampled_signal = cp.asnumpy(sampled_signal)
        
        return t, sampled_signal
    
    def generate_broadband_noise_gpu(self, params: Dict, fs: float, f0: float,
                                   duration: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        GPU加速宽带噪声信号生成
        
        Args:
            params: 噪声参数
            fs: 采样频率
            f0: 中心频率
            duration: 信号持续时间
            
        Returns:
            (时间轴, 信号)
        """
        N = int(fs * duration)
        t = self.xp.arange(N) / fs
        
        # 提取参数
        bandwidth_selection = params.get('bandwidth_selection', 10)
        
        # 带宽映射表 (MHz)
        bandwidth_table = [
            0, 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000,
            1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 7500, 10000
        ]
        
        bandwidth = bandwidth_table[min(bandwidth_selection, len(bandwidth_table) - 1)] * 1e6
        
        # 生成复高斯白噪声
        noise_i = self.xp.random.randn(N).astype(self.xp.float32)
        noise_q = self.xp.random.randn(N).astype(self.xp.float32)
        noise = (noise_i + 1j * noise_q) / self.xp.sqrt(2)
        
        # 频域滤波实现带宽限制
        if bandwidth < fs / 2:
            # FFT到频域
            noise_fft = self.fft_module.fft(noise)
            freqs = self.fft_module.fftfreq(N, 1/fs)
            
            # 创建带通滤波器
            filter_mask = self.xp.abs(freqs - f0) <= bandwidth / 2
            filter_mask = filter_mask.astype(self.xp.complex64)
            
            # 应用滤波器
            filtered_fft = noise_fft * filter_mask
            
            # IFFT回时域
            noise = self.fft_module.ifft(filtered_fft)
        
        # 载波调制
        carrier = self.xp.exp(1j * 2 * self.xp.pi * f0 * t)
        signal = noise * carrier
        
        # 转回CPU
        if self.gpu_enabled:
            t = cp.asnumpy(t)
            signal = cp.asnumpy(signal)
        
        return t, signal
    
    def generate_smart_noise_gpu(self, params: Dict, fs: float, f0: float,
                               duration: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        GPU加速灵巧噪声信号生成
        
        Args:
            params: 灵巧噪声参数
            fs: 采样频率
            f0: 中心频率
            duration: 信号持续时间
            
        Returns:
            (时间轴, 信号)
        """
        N = int(fs * duration)
        t = self.xp.arange(N) / fs
        
        # 提取参数
        noise_bandwidth = params.get('noise_bandwidth', 10) * 1e6  # MHz转Hz
        flicker_mode = params.get('flicker_mode', 2)
        flicker_hold_time = params.get('flicker_hold_time', 10e-6)
        doppler_bandwidth = params.get('doppler_noise_bandwidth', 50e3)
        
        # 生成基础噪声
        noise_i = self.xp.random.randn(N).astype(self.xp.float32)
        noise_q = self.xp.random.randn(N).astype(self.xp.float32)
        base_noise = (noise_i + 1j * noise_q) / self.xp.sqrt(2)
        
        # 多普勒调制
        if doppler_bandwidth > 0:
            doppler_freq = self.xp.random.uniform(-doppler_bandwidth/2, doppler_bandwidth/2)
            doppler_mod = self.xp.exp(1j * 2 * self.xp.pi * doppler_freq * t)
            base_noise *= doppler_mod
        
        # 闪烁调制
        if flicker_mode == 1:  # 固定闪烁
            flicker_freq = 1.0 / flicker_hold_time
            flicker_mod = 0.5 * (1 + self.xp.cos(2 * self.xp.pi * flicker_freq * t))
        else:  # 随机闪烁
            flicker_samples = int(flicker_hold_time * fs)
            num_segments = N // flicker_samples + 1
            flicker_levels = self.xp.random.rand(num_segments)
            flicker_mod = self.xp.repeat(flicker_levels, flicker_samples)[:N]
        
        # 应用调制
        modulated_noise = base_noise * flicker_mod
        
        # 载波调制
        carrier = self.xp.exp(1j * 2 * self.xp.pi * f0 * t)
        signal = modulated_noise * carrier
        
        # 转回CPU
        if self.gpu_enabled:
            t = cp.asnumpy(t)
            signal = cp.asnumpy(signal)
        
        return t, signal
    
    def analyze_signal_spectrum_gpu(self, signal: np.ndarray, fs: float) -> Dict:
        """
        GPU加速信号频谱分析
        
        Args:
            signal: 输入信号
            fs: 采样频率
            
        Returns:
            频谱分析结果
        """
        # 转移到GPU
        if self.gpu_enabled:
            signal_gpu = cp.asarray(signal)
        else:
            signal_gpu = signal
        
        N = len(signal_gpu)
        
        # FFT分析
        fft_result = self.fft_module.fft(signal_gpu)
        freqs = self.fft_module.fftfreq(N, 1/fs)
        
        # 计算功率谱密度
        psd = self.xp.abs(fft_result) ** 2 / (N * fs)
        
        # 找到峰值频率
        peak_idx = self.xp.argmax(psd)
        peak_freq = freqs[peak_idx]
        peak_power = psd[peak_idx]
        
        # 计算带宽（-3dB带宽）
        half_power = peak_power / 2
        above_half_power = psd > half_power
        bandwidth_indices = self.xp.where(above_half_power)[0]
        
        if len(bandwidth_indices) > 1:
            bandwidth = (bandwidth_indices[-1] - bandwidth_indices[0]) * fs / N
        else:
            bandwidth = fs / N
        
        # 转回CPU
        if self.gpu_enabled:
            peak_freq = cp.asnumpy(peak_freq)
            peak_power = cp.asnumpy(peak_power)
            bandwidth = cp.asnumpy(bandwidth)
            freqs = cp.asnumpy(freqs)
            psd = cp.asnumpy(psd)
        
        return {
            'peak_frequency': float(peak_freq),
            'peak_power': float(peak_power),
            'bandwidth': float(bandwidth),
            'frequencies': freqs,
            'power_spectrum': psd,
            'total_power': float(np.sum(psd) * fs / N)
        }
    
    def correlate_signals_gpu(self, signal1: np.ndarray, signal2: np.ndarray) -> Dict:
        """
        GPU加速信号相关性分析
        
        Args:
            signal1: 信号1
            signal2: 信号2
            
        Returns:
            相关性分析结果
        """
        # 转移到GPU
        if self.gpu_enabled:
            sig1_gpu = cp.asarray(signal1)
            sig2_gpu = cp.asarray(signal2)
        else:
            sig1_gpu = signal1
            sig2_gpu = signal2
        
        # 确保信号长度相同
        min_len = min(len(sig1_gpu), len(sig2_gpu))
        sig1_gpu = sig1_gpu[:min_len]
        sig2_gpu = sig2_gpu[:min_len]
        
        # 计算互相关
        if self.gpu_enabled and cp_signal:
            correlation = cp_signal.correlate(sig1_gpu, sig2_gpu, mode='full')
        else:
            correlation = np.correlate(sig1_gpu, sig2_gpu, mode='full')
        
        # 找到最大相关值和延迟
        max_corr_idx = self.xp.argmax(self.xp.abs(correlation))
        max_correlation = correlation[max_corr_idx]
        delay = max_corr_idx - (len(correlation) // 2)
        
        # 计算归一化相关系数
        norm_factor = self.xp.sqrt(self.xp.sum(self.xp.abs(sig1_gpu)**2) * 
                                  self.xp.sum(self.xp.abs(sig2_gpu)**2))
        normalized_correlation = self.xp.abs(max_correlation) / norm_factor
        
        # 转回CPU
        if self.gpu_enabled:
            max_correlation = cp.asnumpy(max_correlation)
            delay = cp.asnumpy(delay)
            normalized_correlation = cp.asnumpy(normalized_correlation)
            correlation = cp.asnumpy(correlation)
        
        return {
            'max_correlation': complex(max_correlation),
            'delay_samples': int(delay),
            'normalized_correlation': float(normalized_correlation),
            'full_correlation': correlation
        }
    
    def batch_process_signals(self, signal_batch: List[np.ndarray], 
                            processing_func: str, **kwargs) -> List[Dict]:
        """
        批量处理信号
        
        Args:
            signal_batch: 信号批次
            processing_func: 处理函数名
            **kwargs: 处理函数参数
            
        Returns:
            处理结果列表
        """
        results = []
        
        if self.gpu_enabled:
            # GPU批处理
            for signal in signal_batch:
                if processing_func == 'spectrum_analysis':
                    result = self.analyze_signal_spectrum_gpu(signal, **kwargs)
                elif processing_func == 'comb_generation':
                    result = self.generate_comb_spectrum_gpu(**kwargs)
                else:
                    result = {}
                results.append(result)
        else:
            # CPU批处理
            for signal in signal_batch:
                # 简化的CPU处理
                result = {'processed': True, 'signal_length': len(signal)}
                results.append(result)
        
        return results
    
    def get_gpu_memory_info(self) -> Dict:
        """获取GPU内存信息"""
        if self.gpu_enabled:
            mempool = cp.get_default_memory_pool()
            return {
                'used_bytes': mempool.used_bytes(),
                'total_bytes': mempool.total_bytes(),
                'gpu_enabled': True
            }
        else:
            return {'gpu_enabled': False}
