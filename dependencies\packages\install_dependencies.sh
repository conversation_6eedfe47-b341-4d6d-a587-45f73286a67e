#!/bin/bash
# 雷达电子战智能干扰决策系统 - 统一依赖包安装脚本
# 处理跨目录的依赖包安装

echo "雷达电子战智能干扰决策系统 - 依赖包安装"
echo "============================================"

# 设置包目录
TRAINING_DIR="packages/training_packages"
ONNX_DIR="packages/onnx_packages"
RKNN_DIR="packages/rknn_packages"

# 检查目录是否存在
if [ ! -d "$TRAINING_DIR" ]; then
    echo "错误: 训练包目录不存在: $TRAINING_DIR"
    exit 1
fi

if [ ! -d "$ONNX_DIR" ]; then
    echo "错误: ONNX包目录不存在: $ONNX_DIR"
    exit 1
fi

if [ ! -d "$RKNN_DIR" ]; then
    echo "错误: RKNN包目录不存在: $RKNN_DIR"
    exit 1
fi

# 创建联合查找路径
FIND_LINKS="--find-links $TRAINING_DIR --find-links $ONNX_DIR --find-links $RKNN_DIR"

echo "Python版本: $(python --version)"
echo ""

# 选择安装模式
echo "请选择安装模式:"
echo "1) 训练环境 (PyTorch + 训练依赖)"
echo "2) ONNX转换环境 (ONNX + 转换工具)"
echo "3) RKNN部署环境 (RKNN + 部署工具)"
echo "4) 完整环境 (所有依赖)"
echo "5) 自定义安装"
echo "6) 退出"

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo "安装训练环境..."
        echo "1/4 安装核心计算库..."
        pip install $FIND_LINKS --no-index numpy scipy matplotlib
        
        echo "2/4 安装PyTorch..."
        pip install $FIND_LINKS --no-index torch torchvision torchaudio
        
        echo "3/4 安装GPU加速库..."
        pip install $FIND_LINKS --no-index cupy-cuda11x || echo "CuPy安装失败，跳过GPU加速"
        
        echo "4/4 安装其他训练依赖..."
        pip install $FIND_LINKS --no-index scikit-learn pandas tqdm pyyaml loguru
        pip install $FIND_LINKS --no-index optuna statsmodels joblib h5py psutil
        ;;
    2)
        echo "安装ONNX转换环境..."
        echo "1/3 安装基础依赖..."
        pip install $FIND_LINKS --no-index numpy torch torchvision
        
        echo "2/3 安装ONNX核心..."
        pip install $FIND_LINKS --no-index onnx onnxruntime onnxruntime-gpu
        
        echo "3/3 安装ONNX工具..."
        pip install $FIND_LINKS --no-index onnx-simplifier onnxoptimizer netron
        pip install $FIND_LINKS --no-index tf2onnx fastapi uvicorn
        ;;
    3)
        echo "安装RKNN部署环境..."
        echo "1/4 安装基础依赖..."
        pip install $FIND_LINKS --no-index numpy onnx onnxruntime
        
        echo "2/4 安装图像处理..."
        pip install $FIND_LINKS --no-index opencv-python opencv-contrib-python pillow
        
        echo "3/4 安装开发工具..."
        pip install $FIND_LINKS --no-index pytest black mypy flake8 isort
        
        echo "4/4 安装其他工具..."
        pip install $FIND_LINKS --no-index sphinx netron cryptography
        
        echo ""
        echo "注意: RKNN工具包需要手动安装"
        echo "请从瑞芯微官网下载对应版本的.whl文件"
        echo "下载地址: https://github.com/rockchip-linux/rknn-toolkit2"
        ;;
    4)
        echo "安装完整环境..."
        echo "这将安装所有可用的依赖包..."
        
        echo "1/6 安装核心计算库..."
        pip install $FIND_LINKS --no-index numpy scipy matplotlib pandas
        
        echo "2/6 安装PyTorch..."
        pip install $FIND_LINKS --no-index torch torchvision torchaudio
        
        echo "3/6 安装ONNX..."
        pip install $FIND_LINKS --no-index onnx onnxruntime onnx-simplifier
        
        echo "4/6 安装图像处理..."
        pip install $FIND_LINKS --no-index opencv-python pillow
        
        echo "5/6 安装机器学习..."
        pip install $FIND_LINKS --no-index scikit-learn optuna statsmodels
        
        echo "6/6 安装工具库..."
        pip install $FIND_LINKS --no-index tqdm pyyaml loguru pytest black
        
        echo "安装GPU加速库..."
        pip install $FIND_LINKS --no-index cupy-cuda11x || echo "CuPy安装失败，跳过"
        ;;
    5)
        echo "自定义安装模式"
        echo "可用的包目录:"
        echo "  - $TRAINING_DIR"
        echo "  - $ONNX_DIR" 
        echo "  - $RKNN_DIR"
        echo ""
        echo "使用以下命令安装特定包:"
        echo "pip install $FIND_LINKS --no-index <package_name>"
        echo ""
        echo "例如:"
        echo "pip install $FIND_LINKS --no-index torch"
        echo "pip install $FIND_LINKS --no-index onnx"
        echo "pip install $FIND_LINKS --no-index opencv-python"
        ;;
    6)
        echo "退出安装"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo "依赖包安装完成!"
echo ""
echo "验证安装:"
echo "python -c \"import torch; print(f'PyTorch: {torch.__version__}')\" 2>/dev/null || echo 'PyTorch未安装'"
echo "python -c \"import onnx; print(f'ONNX: {onnx.__version__}')\" 2>/dev/null || echo 'ONNX未安装'"
echo "python -c \"import cv2; print(f'OpenCV: {cv2.__version__}')\" 2>/dev/null || echo 'OpenCV未安装'"
echo "python -c \"import numpy; print(f'NumPy: {numpy.__version__}')\" 2>/dev/null || echo 'NumPy未安装'"
