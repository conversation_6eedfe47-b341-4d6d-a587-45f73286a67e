#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型转换为RKNN模型
将训练好的ONNX模型转换为RKNN格式，用于C++推理
"""

import os
import sys
import argparse
import numpy as np
from pathlib import Path

try:
    from rknn.api import RKNN
    RKNN_AVAILABLE = True
except ImportError:
    RKNN_AVAILABLE = False
    print("错误: RKNN Toolkit2未安装")
    print("请安装: pip install rknn-toolkit2")
    sys.exit(1)


def convert_onnx_to_rknn(onnx_path: str, rknn_path: str, 
                        target_platform: str = 'rk3588',
                        quantize: bool = False,
                        dataset_path: str = None):
    """
    转换ONNX模型到RKNN格式
    
    Args:
        onnx_path: ONNX模型文件路径
        rknn_path: 输出RKNN模型文件路径
        target_platform: 目标平台 (rk3588, rk3566, etc.)
        quantize: 是否进行量化
        dataset_path: 量化数据集路径
    """
    print(f"开始转换ONNX模型到RKNN格式")
    print(f"输入文件: {onnx_path}")
    print(f"输出文件: {rknn_path}")
    print(f"目标平台: {target_platform}")
    print(f"量化: {'是' if quantize else '否'}")
    
    # 创建RKNN对象
    rknn = RKNN(verbose=True)
    
    try:
        # 配置模型
        print("\n1. 配置模型...")
        # 雷达状态向量是12维，所以mean_values和std_values也需要是12维
        mean_values = [[0] * 12]  # 12维零向量，不进行均值归一化
        std_values = [[1] * 12]   # 12维单位向量，不进行标准差归一化

        if quantize:
            ret = rknn.config(
                mean_values=mean_values,
                std_values=std_values,
                target_platform=target_platform,
                quantized_dtype='asymmetric_quantized-u8',
                optimization_level=3
            )
        else:
            # 不量化时不指定quantized_dtype
            ret = rknn.config(
                mean_values=mean_values,
                std_values=std_values,
                target_platform=target_platform,
                optimization_level=3
            )
        
        if ret != 0:
            print(f"配置模型失败: {ret}")
            return False
        
        # 加载ONNX模型
        print("\n2. 加载ONNX模型...")
        # 指定固定的输入形状，因为RKNN不支持动态形状
        # 雷达状态向量输入形状为 [1, 12]
        ret = rknn.load_onnx(
            model=onnx_path,
            inputs=['state'],
            input_size_list=[[1, 12]]
        )
        if ret != 0:
            print(f"加载ONNX模型失败: {ret}")
            return False
        
        # 构建模型
        print("\n3. 构建RKNN模型...")
        if quantize and dataset_path:
            print(f"使用量化数据集: {dataset_path}")
            ret = rknn.build(do_quantization=True, dataset=dataset_path)
        else:
            ret = rknn.build(do_quantization=False)
        
        if ret != 0:
            print(f"构建模型失败: {ret}")
            return False
        
        # 导出RKNN模型
        print("\n4. 导出RKNN模型...")
        ret = rknn.export_rknn(rknn_path)
        if ret != 0:
            print(f"导出模型失败: {ret}")
            return False
        
        print(f"✅ 模型转换成功: {rknn_path}")
        
        # 验证模型
        print("\n5. 验证模型...")
        verify_rknn_model(rknn, rknn_path)
        
        return True
        
    except Exception as e:
        print(f"转换过程出错: {e}")
        return False
    
    finally:
        # 释放资源
        rknn.release()


def verify_rknn_model(rknn, rknn_path: str):
    """验证RKNN模型"""
    try:
        # 初始化运行时环境
        ret = rknn.init_runtime()
        if ret != 0:
            print(f"初始化运行时失败: {ret}")
            return
        
        # 创建测试输入
        test_input = create_test_input()
        
        # 运行推理
        outputs = rknn.inference(inputs=[test_input])
        
        if outputs:
            print(f"✅ 模型验证成功")
            print(f"   输入形状: {test_input.shape}")
            print(f"   输出数量: {len(outputs)}")
            for i, output in enumerate(outputs):
                print(f"   输出{i+1}形状: {output.shape}")
                print(f"   输出{i+1}范围: [{output.min():.3f}, {output.max():.3f}]")
        else:
            print("❌ 模型验证失败")
            
    except Exception as e:
        print(f"模型验证出错: {e}")


def create_test_input():
    """创建测试输入数据"""
    # 雷达状态向量 (12维) - 与cpp_onnx_inference/src/external_api.cpp保持一致
    test_input = np.array([
        0.95,  # 频率归一化: 9500/10000
        0.2,   # 脉宽归一化: 2.0/10.0
        0.0002,# PRT归一化: 2.0/10000.0
        0.0008,# 功率归一化: 800/1000000.0
        0.167, # 距离归一化: 50/300.0
        0.3,   # 速度归一化: 300/1000.0
        0.125, # 方向归一化: 45/360.0
        0.5,   # 工作模式归一化: 2/4.0
        0.8,   # 威胁值 (直接使用)
        0.4,   # 威胁等级归一化: 2/5.0
        0.9,   # 威胁置信度
        0.8    # 威胁优先级
    ], dtype=np.float32).reshape(1, -1)

    return test_input


def generate_quantization_dataset(output_path: str, num_samples: int = 100):
    """生成量化数据集"""
    print(f"生成量化数据集: {output_path}")
    
    dataset = []
    for i in range(num_samples):
        # 生成随机的雷达参数
        sample = np.random.uniform(0, 2, (1, 12)).astype(np.float32)
        dataset.append(sample)
    
    # 保存数据集
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    np.save(output_path, np.array(dataset))
    
    print(f"✅ 量化数据集已生成: {output_path}")
    print(f"   样本数量: {num_samples}")
    print(f"   样本形状: {dataset[0].shape}")
    
    return output_path


def get_model_info(onnx_path: str):
    """获取ONNX模型信息"""
    try:
        import onnx
        model = onnx.load(onnx_path)
        
        print(f"\nONNX模型信息:")
        print(f"  模型版本: {model.ir_version}")
        print(f"  生产者: {model.producer_name}")
        print(f"  生产者版本: {model.producer_version}")
        
        # 输入信息
        print(f"  输入:")
        for input_info in model.graph.input:
            shape = [dim.dim_value for dim in input_info.type.tensor_type.shape.dim]
            print(f"    {input_info.name}: {shape}")
        
        # 输出信息
        print(f"  输出:")
        for output_info in model.graph.output:
            shape = [dim.dim_value for dim in output_info.type.tensor_type.shape.dim]
            print(f"    {output_info.name}: {shape}")
            
    except ImportError:
        print("ONNX库未安装，跳过模型信息显示")
    except Exception as e:
        print(f"获取模型信息失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ONNX到RKNN模型转换工具')
    parser.add_argument('--input', '-i', type=str, required=True,
                        help='输入ONNX模型文件路径')
    parser.add_argument('--output', '-o', type=str,
                        help='输出RKNN模型文件路径 (默认: 与输入文件同名)')
    parser.add_argument('--platform', '-p', type=str, default='rk3588',
                        choices=['rk3588', 'rk3566', 'rk3568', 'rk3562'],
                        help='目标平台')
    parser.add_argument('--quantize', '-q', action='store_true',
                        help='启用量化')
    parser.add_argument('--dataset', '-d', type=str,
                        help='量化数据集路径')
    parser.add_argument('--generate-dataset', action='store_true',
                        help='生成量化数据集')
    parser.add_argument('--dataset-samples', type=int, default=100,
                        help='量化数据集样本数量')
    parser.add_argument('--info', action='store_true',
                        help='显示ONNX模型信息')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1
    
    # 设置输出文件路径
    if args.output:
        output_path = args.output
    else:
        input_path = Path(args.input)
        output_path = str(input_path.with_suffix('.rknn'))
    
    # 显示模型信息
    if args.info:
        get_model_info(args.input)
    
    # 生成量化数据集
    dataset_path = args.dataset
    if args.generate_dataset:
        dataset_dir = os.path.join(os.path.dirname(output_path), 'dataset')
        dataset_path = os.path.join(dataset_dir, 'quantization_dataset.npy')
        generate_quantization_dataset(dataset_path, args.dataset_samples)
    
    # 转换模型
    success = convert_onnx_to_rknn(
        onnx_path=args.input,
        rknn_path=output_path,
        target_platform=args.platform,
        quantize=args.quantize,
        dataset_path=dataset_path
    )
    
    if success:
        print(f"\n🎉 转换完成!")
        print(f"RKNN模型已保存到: {output_path}")
        
        # 显示文件大小
        onnx_size = os.path.getsize(args.input) / 1024 / 1024
        rknn_size = os.path.getsize(output_path) / 1024 / 1024
        print(f"文件大小: ONNX {onnx_size:.2f} MB -> RKNN {rknn_size:.2f} MB")
        
        # 使用说明
        print(f"\n使用说明:")
        print(f"1. 将 {output_path} 复制到 cpp_rknn_inference/models/ 目录")
        print(f"2. 编译C++项目: cmake .. && make")
        print(f"3. 运行测试: ./cpp_rknn_inference --model models/{os.path.basename(output_path)}")
        
        return 0
    else:
        print(f"\n❌ 转换失败!")
        return 1


if __name__ == "__main__":
    exit(main())
