# 雷达电子战Actor-Critic强化学习指南

## 概述

本指南介绍如何使用Actor-Critic方法进行雷达电子战智能干扰决策。Actor-Critic是一种结合了策略梯度和价值函数的强化学习方法，特别适合雷达对抗场景。

## 为什么选择Actor-Critic

### 1. 连续动作空间优势
- 直接参数输出: Actor网络可以直接输出连续的干扰参数（功率、频率、时间等）
- 避免离散化损失: 不需要将连续参数离散化，保持参数精度
- 自然适配: 雷达干扰本质上是连续控制问题

### 2. 实时决策能力
- 快速响应: Actor直接输出动作，无需搜索最优Q值
- 低延迟: 适合雷达对抗的实时性要求
- 稳定输出: Critic提供价值评估，指导Actor改进

### 3. 多目标优化
- 复合奖励: 可以同时优化干扰效果和资源消耗
- 灵活策略: 支持复杂的决策策略
- 自适应学习: 根据环境反馈自动调整策略

## 网络架构

### Actor网络
```
输入: 雷达状态 (10维)
├── 特征提取器 (256→128→64)
├── 干扰决策头 → 是否干扰 (1维)
├── 干扰类型头 → 干扰类型概率 (5维)
├── 干扰功率头 → 功率值 (1维)
└── 参数头组
    ├── 梳状谱参数 (25维)
    ├── 间歇采样参数 (6维)
    ├── 宽带噪声参数 (1维)
    ├── 灵巧噪声参数 (7维)
    └── 拖引参数 (3维)
```

### Critic网络
```
输入: 状态 (10维) + 动作 (42维)
├── 状态编码器 (256→128)
├── 动作编码器 (64)
└── 价值输出 (1维)
```

## 状态空间定义

雷达状态包含10个关键特征：

| 特征 | 描述 | 单位 | 归一化范围 |
|------|------|------|------------|
| frequency | 雷达载频 | MHz | 0-2 (0-20GHz) |
| pw | 脉冲宽度 | μs | 原值 |
| prt | 脉冲重复周期 | μs | 0-1 (0-1000μs) |
| power | 发射功率 | W | 0-1 (0-10MW) |
| distance | 目标距离 | km | 0-1 (0-100km) |
| speed | 目标速度 | m/s | 0-1 (0-500m/s) |
| direction | 方向角 | 度 | 0-1 (0-180°) |
| work_mode | 工作模式 | - | 0-1 (0-4) |
| threat_level | 威胁等级 | - | 0-1 (1-5) |
| threat_value | 威胁值 | - | 0-1 |

## 动作空间定义

Actor输出47维动作向量：

| 组件 | 维度 | 描述 |
|------|------|------|
| 威胁等级 | 5 | 威胁等级1-5的概率分布 |
| 干扰类型 | 5 | 5种干扰类型概率分布 |
| 梳状谱参数 | 25 | 频偏、周期、保持时间等 |
| 间歇采样参数 | 6 | 重复间隔、采样周期等 |
| 宽带噪声参数 | 1 | 噪声带宽选择 |
| 灵巧噪声参数 | 7 | 多普勒、闪烁参数等 |
| 拖引参数 | 3 | 距离、速度、时间偏移 |

## 输出格式

系统输出标准格式为：**[威胁等级, 干扰类型, 干扰参数...]**

### 输出示例
```python
# 示例输出: [4, 1, 120.5, 1, 3.2, 1.5, 45000.0, 2.0]
# 解释:
# - 威胁等级: 4 (高威胁)
# - 干扰类型: 1 (间歇采样转发干扰)
# - 参数1: 120.5 (重复转发时间间隔 μs)
# - 参数2: 1 (间歇采样开关)
# - 参数3: 3.2 (间歇采样周期 μs)
# - 参数4: 1.5 (间歇采样宽度 μs)
# - 参数5: 45000.0 (干扰覆盖距离 m)
# - 参数6: 2.0 (脉冲采样长度 μs)
```

## 干扰类型说明

### 0. 梳状谱干扰
- **适用场景**: 脉冲压缩雷达
- **参数**: 梳状谱个数(1-8)、频偏序列、闪烁周期、保持时间
- **效果**: 破坏脉冲压缩处理

### 1. 间歇采样转发干扰
- **适用场景**: 制导雷达、跟踪雷达
- **参数**: 重复间隔、采样周期、采样宽度、覆盖距离
- **效果**: 产生虚假目标

### 2. 宽带阻塞噪声干扰
- **适用场景**: 搜索雷达
- **参数**: 噪声带宽选择
- **效果**: 降低信噪比

### 3. 灵巧噪声干扰
- **适用场景**: 多普勒雷达
- **参数**: 噪声源、多普勒参数、闪烁参数
- **效果**: 干扰多普勒处理

### 4. 拖引干扰
- **适用场景**: 制导雷达
- **参数**: 距离偏移、速度偏移、持续时间
- **效果**: 诱导雷达跟踪虚假目标

## 奖励函数设计

```python
def calculate_reward(radar_state, action, effectiveness):
    base_reward = 0.0
    
    # 威胁等级奖励
    threat_bonus = radar_state['threat_level'] * 0.2
    
    if action['should_jam']:
        # 干扰效果奖励
        effectiveness_reward = effectiveness * (1.0 + threat_bonus)
        
        # 功率效率惩罚
        power_penalty = action['jamming_power'] * 0.1
        
        # 类型适配奖励
        type_bonus = get_type_bonus(radar_state['work_mode'], 
                                   action['jamming_type'])
        
        base_reward = effectiveness_reward + type_bonus - power_penalty
    else:
        # 不干扰的奖励/惩罚
        if radar_state['threat_level'] <= 2:
            base_reward = 0.1  # 低威胁不干扰正确
        else:
            base_reward = -0.3  # 高威胁不干扰错误
    
    return np.clip(base_reward, -1.0, 1.0)
```

## 使用示例

### 1. 基本配置
```python
config = {
    'enable_gpu': True,
    'state_dim': 10,
    'action_dim': 42,
    'actor_lr': 0.0001,
    'critic_lr': 0.0002,
    'gamma': 0.99,
    'tau': 0.005,
    'noise_std': 0.2,
    'batch_size': 64,
    'memory_size': 50000
}
```

### 2. 初始化和训练
```python
from core_modules.system_integration.gpu_rl_accelerator import ActorCriticAccelerator

# 初始化
ac_accelerator = ActorCriticAccelerator(config)

# 训练循环
for episode in range(num_episodes):
    radar_state = generate_radar_scenario()
    action = ac_accelerator.select_action(radar_state, training=True)
    
    # 执行动作，获得奖励
    reward = calculate_reward(radar_state, action, effectiveness)
    
    # 存储经验
    ac_accelerator.store_experience(state, action, reward, next_state, done)
    
    # 训练网络
    if ready_to_train:
        train_result = ac_accelerator.train_actor_critic()
```

### 3. 推理使用
```python
from radar_jamming_interface import RadarJammingInterface

# 初始化接口
interface = RadarJammingInterface("models/actor_critic_radar_jamming.pth")

# 输入雷达状态
radar_state = {
    'frequency': 10000,  # MHz
    'pw': 1.0,          # μs
    'prt': 100,         # μs
    'distance': 25,     # km
    'speed': 400,       # m/s
    'work_mode': 4      # 制导模式
}

# 获得决策结果
output = interface.make_jamming_decision(radar_state)
# 输出格式: [威胁等级, 干扰类型, 参数1, 参数2, ...]

threat_level = output[0]    # 威胁等级 1-5
jamming_type = output[1]    # 干扰类型 0-4
jamming_params = output[2:] # 具体干扰参数
```

## 训练技巧

### 1. 超参数调优
- **学习率**: Actor学习率通常比Critic小一半
- **软更新**: τ=0.005 提供稳定的目标网络更新
- **噪声衰减**: 从0.2逐渐衰减到0.01

### 2. 网络结构优化
- **层归一化**: 提高训练稳定性
- **梯度裁剪**: 防止梯度爆炸
- **Dropout**: 防止过拟合

### 3. 经验回放
- **缓冲区大小**: 50000个经验足够大多数场景
- **批次大小**: 64是GPU利用率和训练稳定性的平衡点

## 性能优化

### 1. GPU加速
- 使用CUDA进行并行计算
- 混合精度训练减少显存占用
- 批处理提高计算效率

### 2. 内存管理
- 循环缓冲区避免内存泄漏
- 及时释放不需要的张量
- 使用torch.no_grad()减少内存占用

### 3. 实时性优化
- 预编译网络减少推理延迟
- 异步训练和推理
- 缓存常用计算结果

## 常见问题

### Q: 为什么选择Actor-Critic而不是DQN？
A: Actor-Critic更适合连续动作空间，可以直接输出干扰参数，避免离散化损失。

### Q: 如何处理不同类型的雷达？
A: 通过状态编码包含雷达类型信息，网络会自动学习针对不同雷达的最优策略。

### Q: 训练需要多长时间？
A: 在GPU上训练1000个回合通常需要1-2小时，具体取决于硬件配置。

### Q: 如何评估模型性能？
A: 主要看平均奖励、干扰成功率、功率效率等指标。

## 扩展方向

1. **多智能体**: 支持多个干扰源协同作战
2. **元学习**: 快速适应新型雷达
3. **对抗训练**: 与雷达自适应算法对抗训练
4. **迁移学习**: 利用仿真数据训练，迁移到实际环境
