#!/bin/bash
# ONNX转换依赖包安装脚本 (优化版)

echo "安装ONNX转换依赖包..."

# 设置包目录 (支持跨目录查找)
ONNX_DIR="."
TRAINING_DIR="../training_packages"
RKNN_DIR="../rknn_packages"
FIND_LINKS="--find-links $ONNX_DIR --find-links $TRAINING_DIR --find-links $RKNN_DIR"

echo "1/4 安装基础依赖..."
pip install $FIND_LINKS --no-index numpy torch torchvision

echo "2/4 安装ONNX核心..."
pip install $FIND_LINKS --no-index onnx onnxruntime onnxruntime-gpu

echo "3/4 安装ONNX工具..."
pip install $FIND_LINKS --no-index onnx-simplifier onnxoptimizer netron

echo "4/4 安装转换和服务工具..."
pip install $FIND_LINKS --no-index tf2onnx fastapi uvicorn flask
pip install $FIND_LINKS --no-index pytest memory-profiler requests

echo "ONNX转换依赖包安装完成!"
