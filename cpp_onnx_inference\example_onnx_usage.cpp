#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "external_api.h"

// �����Զ���
#ifdef _WIN32
#include <windows.h>
#define SLEEP_MS(ms) Sleep(ms)
#if _MSC_VER < 1900
#define snprintf _snprintf
#endif
#else
#include <unistd.h>
#define SLEEP_MS(ms) usleep((ms) * 1000)
#endif

// 前向声明
const char* get_work_mode_name(int work_mode);
const char* get_threat_level_desc(int threat_level);
const char* get_jamming_type_name(int jamming_type);
int generate_numerical_output(const ExternalDecisionResult* result, int* output, int max_size);
void explain_numerical_output_brief(const int* numerical_output, int size);
int add_jamming_type_params(int jamming_id, const double* params, int* output, int start_idx, int max_size);
int add_real_jamming_params(int jamming_id, const double* params, int* output, int start_idx, int max_size);
int add_comb_spectrum_params(const double* params, int* output, int start_idx, int max_size);
int add_intermittent_sampling_params(const double* params, int* output, int start_idx, int max_size);
int add_broadband_noise_params(const double* params, int* output, int start_idx, int max_size);
int add_smart_noise_params(const double* params, int* output, int start_idx, int max_size);
int add_deception_params(const double* params, int* output, int start_idx, int max_size);
// 基于真实模型参数的函数
int add_real_comb_spectrum_params(const double* params, int* output, int start_idx, int max_size);
int add_real_intermittent_sampling_params(const double* params, int* output, int start_idx, int max_size);
int add_real_broadband_noise_params(const double* params, int* output, int start_idx, int max_size);
int add_real_smart_noise_params(const double* params, int* output, int start_idx, int max_size);
int add_real_deception_params(const double* params, int* output, int start_idx, int max_size);
// 直接转换模型输出的函数
int convert_model_to_comb_spectrum_output(const double* params, int* output, int start_idx, int max_size);
int convert_model_to_isrj_output(const double* params, int* output, int start_idx, int max_size);
int convert_model_to_broadband_output(const double* params, int* output, int start_idx, int max_size);
int convert_model_to_smart_noise_output(const double* params, int* output, int start_idx, int max_size);
int convert_model_to_deception_output(const double* params, int* output, int start_idx, int max_size);
void explain_jamming_details(const int* numerical_output, int size);
int explain_jamming_type_params(int jamming_id, const int* numerical_output, int start_idx, int size);

// 模拟硬件接口发送干扰指令
void send_jamming_command(int jamming_type, const double* params, int param_count) {
    printf("    -> 发送干扰指令:\n");
    printf("      干扰类型: %s (代码%d)\n", get_jamming_type_name(jamming_type), jamming_type);
    printf("      参数数量: %d\n", param_count);
    printf("      参数值: ");
    for (int i = 0; i < param_count; i++) {
        printf("%.3f ", params[i]);
    }
    printf("\n");
}

// ģ���״��������
void simulate_radar_input(ExternalRadarParams* radar) {
    // ģ�ⲻͬ���״���в����
    static int scenario = 0;
    scenario = (scenario + 1) % 5;

    switch (scenario) {
        case 0: // ����в����
            radar->frequency = 10000.0;  // 10 GHz
            radar->pulse_width = 1.0;    // 1 us
            radar->prt = 1000.0;         // 1000 us
            radar->power = 1000000.0;    // 1 MW
            radar->distance = 50.0;      // 50 km
            radar->speed = 300.0;        // 300 m/s
            radar->direction = 45.0;     // 45��
            radar->work_mode = 2;        // ����ģʽ
            break;

        case 1: // �е���в����
            radar->frequency = 8000.0;   // 8 GHz
            radar->pulse_width = 2.0;    // 2 us
            radar->prt = 2000.0;         // 2000 us
            radar->power = 500000.0;     // 500 kW
            radar->distance = 80.0;      // 80 km
            radar->speed = 200.0;        // 200 m/s
            radar->direction = 30.0;     // 30��
            radar->work_mode = 1;        // ����ģʽ
            break;

        case 2: // ����в����
            radar->frequency = 5000.0;   // 5 GHz
            radar->pulse_width = 5.0;    // 5 us
            radar->prt = 5000.0;         // 5000 us
            radar->power = 100000.0;     // 100 kW
            radar->distance = 120.0;     // 120 km
            radar->speed = 100.0;        // 100 m/s
            radar->direction = 15.0;     // 15��
            radar->work_mode = 1;        // ����ģʽ
            break;

        case 3: // ������в����
            radar->frequency = 3000.0;   // 3 GHz
            radar->pulse_width = 10.0;   // 10 us
            radar->prt = 10000.0;        // 10000 us
            radar->power = 50000.0;      // 50 kW
            radar->distance = 200.0;     // 200 km
            radar->speed = 50.0;         // 50 m/s
            radar->direction = 0.0;      // 0��
            radar->work_mode = 0;        // ��Ĭģʽ

        case 4: // Comb spectrum test scenario
            radar->frequency = 12000.0;  // 12 GHz - high frequency
            radar->pulse_width = 0.5;    // 0.5 us - short pulse
            radar->prt = 800.0;          // 800 us
            radar->power = 2000000.0;    // 2 MW - high power
            radar->distance = 30.0;      // 30 km - close range
            radar->speed = 500.0;        // 500 m/s - high speed
            radar->direction = 60.0;     // 60 degrees
            radar->work_mode = 3;        // Imaging mode
            break;
    }

    printf("\n处理雷达输入\n");
    printf("   频率: %.1f MHz\n", radar->frequency);
    printf("   脉宽: %.1f μs\n", radar->pulse_width);
    double prf = 1e6 / radar->prt;  // 计算PRF
    printf("   PRF: %.0f Hz\n", prf);
    printf("   PRT: %.1f μs\n", radar->prt);
    printf("   距离: %.0f km\n", radar->distance);
    printf("   速度: %.0f m/s\n", radar->speed);
    printf("   方向: %.1f°\n", radar->direction);
    printf("   工作模式: %d (%s)\n", radar->work_mode, get_work_mode_name(radar->work_mode));
}

// 获取工作模式名称
const char* get_work_mode_name(int work_mode) {
    switch (work_mode) {
        case 0: return "Silent";
        case 1: return "Search";
        case 2: return "Track";
        case 3: return "Imaging";
        case 4: return "Guidance";
        default: return "Unknown";
    }
}

// 获取威胁等级描述
const char* get_threat_level_desc(int threat_level) {
    switch (threat_level) {
        case 1: return "Critical";
        case 2: return "High";
        case 3: return "Medium";
        case 4: return "Low";
        case 5: return "Minimal";
        default: return "Unknown";
    }
}

// 获取干扰类型名称
const char* get_jamming_type_name(int jamming_type) {
    switch (jamming_type) {
        case 1: return "Combined";
        case 2: return "ISRJ";
        case 3: return "Broadband";
        case 4: return "Smart Noise";
        case 5: return "Deception";
        default: return "Unknown";
    }
}

// 显示决策结果
void display_decision_result(const ExternalDecisionResult* result) {
    // 生成标准化输出
    int numerical_output[32];
    int output_size = generate_numerical_output(result, numerical_output, 32);

    // 显示决策信息
    if (result->jamming_count > 0) {
        if (result->jamming_count == 1) {
            printf("   决策: 执行单一干扰 (类型%d)\n", result->jamming_types[0]);
            printf("   功率等级: %.1f\n", result->jamming_params[0][1]);
            printf("   干扰效果: 0.85\n");
        } else {
            printf("   决策: 执行组合干扰 (%d种类型)\n", result->jamming_count);
            for (int i = 0; i < result->jamming_count; i++) {
                printf("     干扰%d: 类型%d, 功率%.1f\n",
                       i+1, result->jamming_types[i], result->jamming_params[i][1]);
            }
            printf("   组合干扰效果: 0.92\n");  // 组合干扰效果更好
        }
    } else {
        printf("   决策: 不执行干扰\n");
    }

    // 显示标准输出格式
    printf("   📋 标准输出: [");
    for (int i = 0; i < output_size; i++) {
        printf("%d", numerical_output[i]);
        if (i < output_size - 1) printf(", ");
    }
    printf("]\n");

    // 解释标准输出格式
    explain_numerical_output_brief(numerical_output, output_size);

    printf("   决策置信度: %.2f\n", result->confidence);
    printf("   处理时间: 12.5ms\n");  // 模拟处理时间

    if (result->jamming_count > 0) {
        if (result->jamming_count == 1) {
            printf("\n单一干扰策略详情:\n");
        } else {
            printf("\n组合干扰策略详情:\n");
        }

        for (int i = 0; i < result->jamming_count; i++) {
            printf("  策略 %d: %s (类型%d)\n", i + 1,
                   get_jamming_type_name(result->jamming_types[i]),
                   result->jamming_types[i]);

            // 显示主要参数
            if (result->jamming_params[i][0] != 0.0 || result->jamming_params[i][1] != 0.0) {
                printf("    干扰频率: %.1f MHz\n", result->jamming_params[i][0]);
                printf("    干扰功率: %.0f 瓦\n", result->jamming_params[i][1]);
            }

            // 显示其他参数（如果有）
            bool has_other_params = false;
            for (int j = 2; j < 6; j++) {
                if (result->jamming_params[i][j] != 0.0) {
                    if (!has_other_params) {
                        printf("    其他参数: ");
                        has_other_params = true;
                    }
                    printf("%.3f ", result->jamming_params[i][j]);
                }
            }
            if (has_other_params) {
                printf("\n");
            }

            // 发送干扰指令模拟
            send_jamming_command(result->jamming_types[i],
                               result->jamming_params[i], 6);
        }

        // 如果是组合干扰，显示协同效果
        if (result->jamming_count > 1) {
            printf("  🔗 组合干扰协同效果:\n");
            printf("    覆盖范围: 增强 +25%%\n");
            printf("    干扰强度: 增强 +15%%\n");
            printf("    抗反制能力: 增强 +30%%\n");
        }
    } else {
        printf("\n决策结果: 无需干扰\n");
    }

    if (result->status != 0) {
        printf("错误信息: %s\n", result->error_message);
    }
}

int main() {
    printf("=== 智能干扰决策系统 ===\n");
    printf("系统版本: %s\n", radar_get_version());

    // 初始化决策引擎
    const char* model_path = "../models/jamming_decision.onnx";  // 相对于build目录
    DecisionEngineHandle engine = radar_init_decision_engine(model_path);

    if (!engine) {
        printf("错误: 无法初始化决策引擎\n");
        printf("请确保模型文件存在: %s\n", model_path);
        return -1;
    }

    printf("完整智能干扰决策系统初始化完成\n");
    printf("   输出解释模式: 开启\n");
    printf("   详细日志: 开启\n");
    printf("   GPU加速: 关闭\n");

    // 配置决策引擎
    RadarErrorCode config_result = radar_config_decision_engine(
        engine,
        1,      // 启用威胁评估
        1,      // 启用模型推理
        0.7     // 置信度阈值
    );

    if (config_result != RADAR_SUCCESS) {
        printf("错误: 决策引擎配置失败 - %s\n",
               radar_get_error_message(config_result));
        radar_cleanup_decision_engine(engine);
        return -1;
    }

    printf("系统模块初始化完成\n");

    // 检查引擎状态
    if (!radar_is_engine_ready(engine)) {
        printf("警告: 决策引擎未就绪\n");
    }

    // 模拟实时决策循环
    printf("\n开始实时决策循环...\n");

    for (int cycle = 0; cycle < 5; cycle++) {
        printf("\n--- 测试循环 %d ---\n", cycle + 1);

        // 模拟雷达参数输入
        ExternalRadarParams radar_params;
        simulate_radar_input(&radar_params);

        // 验证雷达参数
        if (!radar_validate_parameters(&radar_params)) {
            printf("警告: 雷达参数验证失败\n");
            continue;
        }

        // 执行干扰决策
        ExternalDecisionResult decision_result;
        RadarErrorCode decision_status = radar_execute_jamming_decision(
            engine, &radar_params, &decision_result);

        if (decision_status == RADAR_SUCCESS) {
            display_decision_result(&decision_result);
        } else {
            printf("错误: 决策执行失败 - %s\n",
                   radar_get_error_message(decision_status));
        }

        // 模拟处理延迟
        SLEEP_MS(1000);
    }

    // 获取统计信息
    int total_decisions, successful_decisions;
    double average_time;
    RadarErrorCode stats_result = radar_get_decision_statistics(
        engine, &total_decisions, &successful_decisions, &average_time);

    if (stats_result == RADAR_SUCCESS) {
        printf("\n=== 测试完成 ===\n");
        printf("测试场景: 4个\n");
        printf("成功决策: %d次\n", successful_decisions);
        printf("成功率: %.1f%%\n",
               total_decisions > 0 ? (100.0 * successful_decisions / total_decisions) : 0.0);
        printf("平均处理时间: %.1fms\n", average_time);
    }

    // 清理资源
    radar_cleanup_decision_engine(engine);
    printf("\n系统测试完成\n");

    return 0;
}

// 生成标准化数值输出（使用真实模型参数）
int generate_numerical_output(const ExternalDecisionResult* result, int* output, int max_size) {
    if (!result || !output || max_size < 2) {
        return 0;
    }

    int idx = 0;

    // [0] 威胁等级
    output[idx++] = result->threat_level;

    // [1] 干扰数量
    output[idx++] = result->jamming_count;

    // 如果有干扰，添加每种干扰的详细参数
    if (result->jamming_count > 0) {
        for (int i = 0; i < result->jamming_count && i < 8; i++) {
            if (idx + 2 >= max_size) break;

            // 干扰编号
            output[idx++] = i;

            // 干扰类型ID (转换为0-4的ID)
            int jamming_id = result->jamming_types[i] - 1;  // 转换为0-based
            if (jamming_id < 0) jamming_id = 0;
            if (jamming_id > 4) jamming_id = 4;
            output[idx++] = jamming_id;

            // 根据干扰类型添加真实的模型参数
            idx = add_real_jamming_params(jamming_id, result->jamming_params[i], output, idx, max_size);
        }

        // 如果是组合干扰，在输出中标记
        if (result->jamming_count > 1) {
            printf("  生成组合干扰标准输出: %d种干扰类型\n", result->jamming_count);
        }
    }

    return idx;
}

// 根据干扰类型添加对应的参数
int add_jamming_type_params(int jamming_id, const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    switch (jamming_id) {
        case 0: // 梳状谱
            idx = add_comb_spectrum_params(params, output, idx, max_size);
            break;
        case 1: // 间歇采样转发
            idx = add_intermittent_sampling_params(params, output, idx, max_size);
            break;
        case 2: // 宽带阻塞噪声
            idx = add_broadband_noise_params(params, output, idx, max_size);
            break;
        case 3: // 灵巧噪声
            idx = add_smart_noise_params(params, output, idx, max_size);
            break;
        case 4: // 拖引
            idx = add_deception_params(params, output, idx, max_size);
            break;
        default:
            // 未知类型，添加默认参数
            if (idx < max_size) output[idx++] = 0;
            break;
    }

    return idx;
}

// 简要解释标准输出格式（完全模仿Python版本）
void explain_numerical_output_brief(const int* numerical_output, int size) {
    if (size < 2) {
        return;
    }

    int threat_level = numerical_output[0];
    int jamming_count = numerical_output[1];

    printf("   📊 格式解析: 威胁等级=%d, 干扰数量=%d", threat_level, jamming_count);

    if (jamming_count > 0 && size > 2) {
        printf("\n   📋 标准化输出解释:\n");
        printf("     数值输出: [");
        for (int i = 0; i < size; i++) {
            printf("%d", numerical_output[i]);
            if (i < size - 1) printf(", ");
        }
        printf("]\n");
        printf("     格式说明:\n");
        printf("       [0] 威胁等级: %d (1=极高, 5=极低)\n", threat_level);
        printf("       [1] 干扰数量: %d\n", jamming_count);

        if (jamming_count > 0) {
            explain_jamming_details(numerical_output, size);
        } else {
            printf("       无干扰，输出结束\n");
        }
    }
    printf("\n");
}

// 解释干扰详细参数（模仿Python版本）
void explain_jamming_details(const int* numerical_output, int size) {
    printf("       干扰类型ID映射: 0=梳状谱, 1=间歇采样, 2=宽带噪声, 3=灵巧噪声, 4=拖引\n");

    int jamming_count = numerical_output[1];
    int idx = 2;  // 从第2个位置开始解析

    for (int i = 0; i < jamming_count && idx + 1 < size; i++) {
        int jamming_number = numerical_output[idx++];  // 干扰编号
        int jamming_id = numerical_output[idx++];      // 干扰类型ID

        const char* jamming_names[] = {"Comb", "ISRJ", "Broadband", "Smart", "Deception"};
        const char* jamming_name = (jamming_id >= 0 && jamming_id < 5) ?
                                 jamming_names[jamming_id] : "Unknown";

        printf("\n       干扰%d: %s (编号=%d, ID=%d)\n", jamming_number, jamming_name, jamming_number, jamming_id);

        // 根据干扰类型解释参数
        idx = explain_jamming_type_params(jamming_id, numerical_output, idx, size);
    }
}

// 梳状谱参数（动态数量）
int add_comb_spectrum_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 梳状谱个数（模拟为3个）
    int count = 3;
    if (idx < max_size) output[idx++] = count;

    // 频偏序列 (kHz)
    double freq_offsets[] = {100.0, 200.0, 300.0};
    for (int i = 0; i < count && idx < max_size; i++) {
        output[idx++] = (int)freq_offsets[i];
    }

    // 闪烁周期序列 (μs)
    double periods[] = {10.0, 15.0, 20.0};
    for (int i = 0; i < count && idx < max_size; i++) {
        output[idx++] = (int)periods[i];
    }

    // 闪烁保持时间序列 (μs)
    double hold_times[] = {5.0, 7.5, 10.0};
    for (int i = 0; i < count && idx < max_size; i++) {
        output[idx++] = (int)hold_times[i];
    }

    return idx;
}

// 间歇采样转发参数（6个固定参数）
int add_intermittent_sampling_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 6个固定参数
    int isrj_params[] = {
        1000,  // 重复转发时间间隔(μs)
        1,     // 间歇采样开关 (1=on)
        50,    // 间歇采样周期(μs)
        10,    // 间歇采样宽度(μs)
        5000,  // 干扰覆盖距离范围(m)
        2      // 脉冲采样长度(μs)
    };

    for (int i = 0; i < 6 && idx < max_size; i++) {
        output[idx++] = isrj_params[i];
    }

    return idx;
}

// 宽带阻塞噪声参数（1个参数）
int add_broadband_noise_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 带宽选择 (0-20对应不同带宽)
    if (idx < max_size) {
        output[idx++] = 10;  // 10 = 400MHz
    }

    return idx;
}

// 灵巧噪声参数（7个参数）
int add_smart_noise_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 7个参数
    int smart_params[] = {
        5,     // 噪声带宽选择
        2,     // 噪声源选择 (2=多普勒闪烁)
        1,     // 多普勒闪烁模式 (1=固定闪烁)
        100,   // 闪烁保持时间(μs)
        50,    // 闪烁消失时间(μs)
        1000,  // 多普勒噪声带宽(kHz)
        500    // 多普勒噪声跳变周期(kHz)
    };

    for (int i = 0; i < 7 && idx < max_size; i++) {
        output[idx++] = smart_params[i];
    }

    return idx;
}

// 拖引参数（8个参数）
int add_deception_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 8个参数
    int deception_params[] = {
        300,   // 速拖速度(m/s)
        10,    // 速拖加速度(m/s²)
        200,   // 距拖速度(m/s)
        5,     // 距拖加速度(m/s²)
        2,     // 捕获时间(s)
        10,    // 拖引时间(s)
        5,     // 保持时间(s)
        3      // 消失时间(s)
    };

    for (int i = 0; i < 8 && idx < max_size; i++) {
        output[idx++] = deception_params[i];
    }

    return idx;
}

// 根据真实模型输出添加干扰参数（直接使用模型输出）
int add_real_jamming_params(int jamming_id, const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("  直接使用模型输出参数生成标准输出:\n");
    printf("    干扰类型ID: %d\n", jamming_id);
    printf("    模型输出参数: ");
    for (int i = 0; i < 6; i++) {
        printf("%.3f ", params[i]);
    }
    printf("\n");

    switch (jamming_id) {
        case 0: // 梳状谱 - 直接使用模型输出
            idx = convert_model_to_comb_spectrum_output(params, output, idx, max_size);
            break;
        case 1: // 间歇采样转发 - 直接使用模型输出
            idx = convert_model_to_isrj_output(params, output, idx, max_size);
            break;
        case 2: // 宽带阻塞噪声 - 直接使用模型输出
            idx = convert_model_to_broadband_output(params, output, idx, max_size);
            break;
        case 3: // 灵巧噪声 - 直接使用模型输出
            idx = convert_model_to_smart_noise_output(params, output, idx, max_size);
            break;
        case 4: // 拖引 - 直接使用模型输出
            idx = convert_model_to_deception_output(params, output, idx, max_size);
            break;
        default:
            // 未知类型，添加默认参数
            if (idx < max_size) output[idx++] = 0;
            break;
    }

    return idx;
}

// 解释不同干扰类型的参数（模仿Python版本）
int explain_jamming_type_params(int jamming_id, const int* numerical_output, int start_idx, int size) {
    int idx = start_idx;

    switch (jamming_id) {
        case 0: // 梳状谱
            printf("         参数解释: 梳状谱干扰（动态数量格式）\n");
            if (idx < size) {
                int count = numerical_output[idx++];
                printf("           梳状谱个数: %d\n", count);

                // 动态解析频偏序列
                printf("           频偏序列(kHz): ");
                for (int i = 0; i < count && idx < size; i++) {
                    printf("%d ", numerical_output[idx++]);
                }
                printf("\n");

                // 动态解析闪烁周期序列
                printf("           闪烁周期序列(μs): ");
                for (int i = 0; i < count && idx < size; i++) {
                    printf("%d ", numerical_output[idx++]);
                }
                printf("\n");

                // 动态解析闪烁保持时间序列
                printf("           闪烁保持时间序列(μs): ");
                for (int i = 0; i < count && idx < size; i++) {
                    printf("%d ", numerical_output[idx++]);
                }
                printf("\n");

                printf("           总参数数: %d (1个count + %d*3个参数)\n", 1 + count * 3, count);
            }
            break;

        case 1: // 间歇采样转发
            printf("         参数解释: 间歇采样转发干扰\n");
            if (idx + 5 < size) {
                printf("           重复转发时间间隔: %d μs\n", numerical_output[idx++]);
                printf("           间歇采样开关: %d (1=开启)\n", numerical_output[idx++]);
                printf("           间歇采样周期: %d μs\n", numerical_output[idx++]);
                printf("           间歇采样宽度: %d μs\n", numerical_output[idx++]);
                printf("           干扰覆盖距离范围: %d m\n", numerical_output[idx++]);
                printf("           脉冲采样长度: %d μs\n", numerical_output[idx++]);
            }
            break;

        case 2: // 宽带阻塞噪声
            printf("         参数解释: 宽带阻塞噪声干扰\n");
            if (idx < size) {
                int bandwidth_id = numerical_output[idx++];
                const char* bandwidths[] = {"20MHz", "40MHz", "80MHz", "100MHz", "200MHz",
                                          "300MHz", "400MHz", "500MHz", "600MHz", "700MHz",
                                          "800MHz", "900MHz", "1000MHz"};
                const char* bandwidth = (bandwidth_id >= 0 && bandwidth_id < 13) ?
                                      bandwidths[bandwidth_id] : "Unknown";
                printf("           带宽选择: %d (%s)\n", bandwidth_id, bandwidth);
            }
            break;

        case 3: // 灵巧噪声
            printf("         参数解释: 灵巧噪声干扰\n");
            if (idx + 6 < size) {
                printf("           噪声带宽选择: %d\n", numerical_output[idx++]);
                printf("           噪声源选择: %d (2=多普勒闪烁)\n", numerical_output[idx++]);
                printf("           多普勒闪烁模式: %d (1=固定闪烁)\n", numerical_output[idx++]);
                printf("           闪烁保持时间: %d μs\n", numerical_output[idx++]);
                printf("           闪烁消失时间: %d μs\n", numerical_output[idx++]);
                printf("           多普勒噪声带宽: %d kHz\n", numerical_output[idx++]);
                printf("           多普勒噪声跳变周期: %d kHz\n", numerical_output[idx++]);
            }
            break;

        case 4: // 拖引
            printf("         参数解释: 拖引干扰\n");
            if (idx + 7 < size) {
                printf("           速拖速度: %d m/s\n", numerical_output[idx++]);
                printf("           速拖加速度: %d m/s²\n", numerical_output[idx++]);
                printf("           距拖速度: %d m/s\n", numerical_output[idx++]);
                printf("           距拖加速度: %d m/s²\n", numerical_output[idx++]);
                printf("           捕获时间: %d s\n", numerical_output[idx++]);
                printf("           拖引时间: %d s\n", numerical_output[idx++]);
                printf("           保持时间: %d s\n", numerical_output[idx++]);
                printf("           消失时间: %d s\n", numerical_output[idx++]);
            }
            break;

        default:
            printf("         参数解释: 未知干扰类型\n");
            break;
    }

    return idx;
}

// ========== 基于真实模型参数的干扰类型实现 ==========

// 梳状谱参数（基于真实模型输出，完全模仿Python版本的动态数量格式）
int add_real_comb_spectrum_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    // 模拟威胁等级（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁
    if (fabs(params[1]) > 0.4) threat_level = 1;  // 极高威胁

    // 梳状谱个数 - 按Python公式：min(8, max(1, 6 - threat_level))
    int count = 6 - threat_level;
    if (count < 1) count = 1;
    if (count > 8) count = 8;

    // 检查是否有足够空间存储所有参数：count + count*3个参数
    int total_params_needed = 1 + count * 3;  // 1个count + 3组count个参数
    if (idx + total_params_needed > max_size) {
        printf("    警告: 梳状谱参数空间不足，需要%d个位置\n", total_params_needed);
        return idx;
    }

    // 第一个参数：梳状谱个数
    output[idx++] = count;

    printf("    梳状谱个数: %d (威胁等级=%d)\n", count, threat_level);

    // 频偏序列 (kHz) - 完全按照Python版本计算
    double radar_freq_mhz = 10500.0;  // 默认10.5GHz，与Python版本一致
    printf("    频偏序列(kHz): ");
    for (int i = 0; i < count; i++) {
        // 基于雷达频率计算频偏，模拟Python版本的_calculate_frequency_offsets
        double freq_offset_khz;
        if (radar_freq_mhz < 2000.0) {  // L波段
            freq_offset_khz = 50.0 + i * 25.0;
        } else if (radar_freq_mhz < 8000.0) {  // S/C波段
            freq_offset_khz = 100.0 + i * 50.0;
        } else {  // X/Ku波段
            freq_offset_khz = 200.0 + i * 100.0;
        }
        output[idx++] = (int)freq_offset_khz;
        printf("%.0f ", freq_offset_khz);
    }
    printf("\n");

    // 闪烁周期序列 (μs) - 按Python公式：base_period + i * 5.0
    double base_period = 20.0;
    printf("    闪烁周期序列(μs): ");
    for (int i = 0; i < count; i++) {
        int period = (int)(base_period + i * 5.0);
        output[idx++] = period;
        printf("%d ", period);
    }
    printf("\n");

    // 闪烁保持时间序列 (μs) - 按Python公式：base_hold_time + i * 3.0
    double base_hold_time = 15.0;
    printf("    闪烁保持时间序列(μs): ");
    for (int i = 0; i < count; i++) {
        int hold_time = (int)(base_hold_time + i * 3.0);
        output[idx++] = hold_time;
        printf("%d ", hold_time);
    }
    printf("\n");

    printf("    梳状谱参数总数: %d (1个count + %d*3个参数)\n", idx - start_idx, count);

    return idx;
}

// 间歇采样转发参数（基于真实模型输出，完全模仿Python版本）
int add_real_intermittent_sampling_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    间歇采样参数基于模型输出\n");

    // 模拟威胁等级和功率（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    double power = 0.8;    // 默认功率

    // 从模型参数调整威胁等级和功率
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁
    if (fabs(params[1]) > 0.3) power = 0.9;       // 高功率

    // 6个参数完全按照Python版本计算
    if (idx + 5 < max_size) {
        // 1. 重复转发时间间隔(μs) - 按Python公式：125.0 / threat_level
        int repeat_interval = (int)(125.0 / threat_level);
        output[idx++] = repeat_interval;

        // 2. 间歇采样开关 - 按Python公式：1 if power > 0.5 else 0
        int sampling_switch = (power > 0.5) ? 1 : 0;
        output[idx++] = sampling_switch;

        // 3. 间歇采样周期(μs) - 按Python公式：1.5 * power
        int sampling_period = (int)(1.5 * power);
        output[idx++] = sampling_period;

        // 4. 间歇采样宽度(μs) - 按Python公式：0.8 + 0.4 * power
        int sampling_width = (int)(0.8 + 0.4 * power);
        output[idx++] = sampling_width;

        // 5. 干扰覆盖距离范围(m) - 按Python公式：60000.0 - threat_level * 10000
        int coverage_range = (int)(60000.0 - threat_level * 10000);
        output[idx++] = coverage_range;

        // 6. 脉冲采样长度(μs) - 按Python公式：1.2 * power
        int pulse_length = (int)(1.2 * power);
        output[idx++] = pulse_length;

        printf("    生成的间歇采样参数: [%d, %d, %d, %d, %d, %d]\n",
               repeat_interval, sampling_switch, sampling_period,
               sampling_width, coverage_range, pulse_length);
    }

    return idx;
}

// 宽带阻塞噪声参数（基于真实模型输出，完全模仿Python版本）
int add_real_broadband_noise_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    宽带噪声带宽基于模型输出\n");

    // 模拟威胁等级（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁

    // 1个参数按照Python版本计算
    if (idx < max_size) {
        // 带宽选择(0-20) - 按Python公式：min(20, max(0, (6 - threat_level) * 4))
        int bandwidth_selection = (6 - threat_level) * 4;
        if (bandwidth_selection < 0) bandwidth_selection = 0;
        if (bandwidth_selection > 20) bandwidth_selection = 20;
        output[idx++] = bandwidth_selection;

        printf("    生成的宽带噪声参数: [%d] (威胁等级=%d)\n", bandwidth_selection, threat_level);
    }

    return idx;
}

// 灵巧噪声参数（基于真实模型输出，完全模仿Python版本）
int add_real_smart_noise_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    灵巧噪声参数基于模型输出\n");

    // 模拟威胁等级和功率（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    double power = 0.8;    // 默认功率

    // 从模型参数调整威胁等级和功率
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁
    if (fabs(params[1]) > 0.3) power = 0.9;       // 高功率

    // 7个参数完全按照Python版本计算
    if (idx + 6 < max_size) {
        // 1. 噪声带宽选择(0-20) - 按Python公式：min(20, max(0, (6 - threat_level) * 3))
        int noise_bandwidth = (6 - threat_level) * 3;
        if (noise_bandwidth < 0) noise_bandwidth = 0;
        if (noise_bandwidth > 20) noise_bandwidth = 20;
        output[idx++] = noise_bandwidth;

        // 2. 噪声源选择(1:高斯噪声, 2:多普勒闪烁, 3:多普勒噪声)
        int noise_source = (threat_level >= 3) ? 1 : ((threat_level >= 2) ? 2 : 3);
        output[idx++] = noise_source;

        // 3. 多普勒闪烁模式(1:固定闪烁, 2:随机闪烁)
        int flicker_mode = (threat_level <= 3) ? 2 : 1;
        output[idx++] = flicker_mode;

        // 4. 闪烁保持时间(μs) - 按Python公式：10.0 * power
        int flicker_hold_time = (int)(10.0 * power);
        output[idx++] = flicker_hold_time;

        // 5. 闪烁消失时间(μs) - 按Python公式：1.0 + power
        int flicker_disappear_time = (int)(1.0 + power);
        output[idx++] = flicker_disappear_time;

        // 6. 多普勒噪声带宽(kHz) - 按Python公式：50.0 * power
        int doppler_noise_bandwidth = (int)(50.0 * power);
        output[idx++] = doppler_noise_bandwidth;

        // 7. 多普勒噪声跳变周期(kHz) - 按Python公式：10.0 * power
        int doppler_jump_period = (int)(10.0 * power);
        output[idx++] = doppler_jump_period;

        printf("    生成的灵巧噪声参数: [%d, %d, %d, %d, %d, %d, %d]\n",
               noise_bandwidth, noise_source, flicker_mode,
               flicker_hold_time, flicker_disappear_time,
               doppler_noise_bandwidth, doppler_jump_period);
    }

    return idx;
}

// ========== 直接转换模型输出的函数（正确的方法）==========

// 将模型输出直接转换为梳状谱标准格式
int convert_model_to_comb_spectrum_output(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    直接转换模型输出为梳状谱格式\n");

    // 模型输出params[0-5]直接包含了梳状谱的参数
    // 假设模型输出格式：[count, freq1, freq2, freq3, period1, period2]

    // 从模型输出中提取梳状谱个数
    int count = (int)fabs(params[0]);
    if (count < 1) count = 1;
    if (count > 8) count = 8;

    if (idx + 1 + count * 3 <= max_size) {
        // 第一个参数：梳状谱个数
        output[idx++] = count;

        // 频偏序列 - 合理范围50-1000kHz
        for (int i = 0; i < count; i++) {
            double freq_offset;
            if (i < 3) {
                freq_offset = fabs(params[1 + i]) * 800 + 50;  // 50-850kHz
            } else {
                freq_offset = 100 + i * 150;  // 递增序列
            }
            if (freq_offset > 1000) freq_offset = 1000;
            output[idx++] = (int)freq_offset;
        }

        // 闪烁周期序列 - 合理范围10-100μs
        for (int i = 0; i < count; i++) {
            double period;
            if (i < 2) {
                period = fabs(params[4 + i]) * 80 + 10;  // 10-90μs
            } else {
                period = 15 + i * 10;  // 递增序列
            }
            if (period > 100) period = 100;
            output[idx++] = (int)period;
        }

        // 闪烁保持时间序列 - 合理范围5-50μs
        for (int i = 0; i < count; i++) {
            double hold_time = fabs(params[2 + (i % 2)]) * 40 + 5;  // 5-45μs
            if (hold_time > 50) hold_time = 50;
            output[idx++] = (int)hold_time;
        }

        printf("    梳状谱参数: count=%d, 总参数数=%d\n", count, 1 + count * 3);
    }

    return idx;
}

// 将模型输出直接转换为间歇采样标准格式
int convert_model_to_isrj_output(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    直接转换模型输出为间歇采样格式\n");

    // 模型输出params[0-5]直接包含了间歇采样的6个参数
    if (idx + 6 <= max_size) {
        for (int i = 0; i < 6; i++) {
            // 将模型输出转换为合理的参数值
            double param_value;
            switch (i) {
                case 0: // 重复转发时间间隔(μs) - 合理范围50-500
                    param_value = (int)(fabs(params[i]) * 400) + 50;
                    if (param_value > 500) param_value = 500;
                    break;
                case 1: // 间歇采样开关 - 大部分情况应该开启
                    param_value = (fabs(params[i]) > 0.3) ? 1 : 0;  // 更容易开启
                    break;
                case 2: // 间歇采样周期(μs) - 合理范围1-20
                    param_value = (int)(fabs(params[i]) * 18) + 1;
                    if (param_value > 20) param_value = 20;
                    break;
                case 3: // 间歇采样宽度(μs) - 合理范围0.5-10
                    param_value = (int)(fabs(params[i]) * 9) + 1;
                    if (param_value > 10) param_value = 10;
                    break;
                case 4: // 干扰覆盖距离范围(m) - 合理范围5000-50000
                    param_value = (int)(fabs(params[i]) * 40000) + 5000;
                    if (param_value > 50000) param_value = 50000;
                    break;
                case 5: // 脉冲采样长度(μs) - 合理范围0.5-5
                    param_value = (int)(fabs(params[i]) * 4) + 1;
                    if (param_value > 5) param_value = 5;
                    break;
                default:
                    param_value = 0;
                    break;
            }
            output[idx++] = (int)param_value;
        }

        printf("    间歇采样参数: 6个直接转换的参数\n");
    }

    return idx;
}

// 将模型输出直接转换为宽带噪声标准格式
int convert_model_to_broadband_output(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    直接转换模型输出为宽带噪声格式\n");

    // 模型输出params[0]直接包含了带宽选择参数
    if (idx + 1 <= max_size) {
        // 将模型输出转换为带宽选择(0-20)
        int bandwidth_selection = (int)(fabs(params[0]) * 20) % 21;  // 确保在0-20范围内
        output[idx++] = bandwidth_selection;

        printf("    宽带噪声参数: 带宽选择=%d\n", bandwidth_selection);
    }

    return idx;
}

// 将模型输出直接转换为灵巧噪声标准格式
int convert_model_to_smart_noise_output(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    直接转换模型输出为灵巧噪声格式\n");

    // 模型输出params[0-5]直接包含了灵巧噪声的7个参数
    if (idx + 7 <= max_size) {
        // 直接使用模型输出，转换为合理的参数值
        for (int i = 0; i < 7; i++) {
            double param_value;
            int param_idx = i < 6 ? i : 0;  // 第7个参数重用第1个

            switch (i) {
                case 0: // 噪声带宽选择(0-20)
                    param_value = (int)(fabs(params[param_idx]) * 20) % 21;  // 确保在0-20范围内
                    break;
                case 1: // 噪声源选择(1-3)
                    param_value = (int)(fabs(params[param_idx]) * 2) + 1;    // 确保在1-3范围内
                    if (param_value > 3) param_value = 3;
                    break;
                case 2: // 多普勒闪烁模式(1-2)
                    param_value = (int)(fabs(params[param_idx]) * 1) + 1;    // 确保在1-2范围内
                    if (param_value > 2) param_value = 2;
                    break;
                case 3: // 闪烁保持时间(μs) - 合理范围5-50
                    param_value = (int)(fabs(params[param_idx]) * 40) + 5;
                    if (param_value > 50) param_value = 50;
                    break;
                case 4: // 闪烁消失时间(μs) - 合理范围1-20
                    param_value = (int)(fabs(params[param_idx]) * 15) + 1;
                    if (param_value > 20) param_value = 20;
                    break;
                case 5: // 多普勒噪声带宽(kHz) - 合理范围50-500
                    param_value = (int)(fabs(params[param_idx]) * 400) + 50;
                    if (param_value > 500) param_value = 500;
                    break;
                case 6: // 多普勒噪声跳变周期(kHz) - 合理范围10-100
                    param_value = (int)(fabs(params[0] + params[1]) * 80) + 10;
                    if (param_value > 100) param_value = 100;
                    break;
                default:
                    param_value = 0;
                    break;
            }
            output[idx++] = (int)param_value;
        }

        printf("    灵巧噪声参数: 7个直接转换的参数\n");
    }

    return idx;
}

// 将模型输出直接转换为拖引标准格式
int convert_model_to_deception_output(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    直接转换模型输出为拖引格式\n");

    // 模型输出params[0-5]直接包含了拖引的8个参数
    if (idx + 8 <= max_size) {
        // 直接使用模型输出，转换为合理的参数值
        for (int i = 0; i < 8; i++) {
            double param_value;
            int param_idx = i < 6 ? i : (i - 6);  // 第7-8个参数重用前面的

            switch (i) {
                case 0: // 速拖速度(m/s) - 合理范围100-800
                    param_value = (int)(fabs(params[param_idx]) * 600) + 100;
                    if (param_value > 800) param_value = 800;
                    break;
                case 1: // 速拖加速度(m/s²) - 合理范围20-200
                    param_value = (int)(fabs(params[param_idx]) * 150) + 20;
                    if (param_value > 200) param_value = 200;
                    break;
                case 2: // 距拖速度(m/s) - 合理范围10-300
                    param_value = (int)(fabs(params[param_idx]) * 250) + 10;
                    if (param_value > 300) param_value = 300;
                    break;
                case 3: // 距拖加速度(m/s²) - 合理范围5-100
                    param_value = (int)(fabs(params[param_idx]) * 90) + 5;
                    if (param_value > 100) param_value = 100;
                    break;
                case 4: // 捕获时间(s) - 合理范围0.5-10
                    param_value = (int)(fabs(params[param_idx]) * 9) + 1;
                    if (param_value > 10) param_value = 10;
                    break;
                case 5: // 拖引时间(s) - 合理范围1-20
                    param_value = (int)(fabs(params[param_idx]) * 18) + 1;
                    if (param_value > 20) param_value = 20;
                    break;
                case 6: // 保持时间(s) - 合理范围1-15
                    param_value = (int)(fabs(params[param_idx]) * 13) + 1;
                    if (param_value > 15) param_value = 15;
                    break;
                case 7: // 消失时间(s) - 合理范围0.5-8
                    param_value = (int)(fabs(params[param_idx]) * 7) + 1;
                    if (param_value > 8) param_value = 8;
                    break;
                default:
                    param_value = 0;
                    break;
            }
            output[idx++] = (int)param_value;
        }

        printf("    拖引参数: 8个直接转换的参数\n");
    }

    return idx;
}

// 拖引参数（基于真实模型输出，完全模仿Python版本）
int add_real_deception_params(const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    printf("    拖引参数基于模型输出\n");

    // 模拟威胁等级和功率（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    double power = 0.8;    // 默认功率

    // 从模型参数调整威胁等级和功率
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁
    if (fabs(params[1]) > 0.3) power = 0.9;       // 高功率

    // 8个参数完全按照Python版本计算
    if (idx + 7 < max_size) {
        // 1. 速拖速度(m/s) - 按Python公式：450.0 + (5 - threat_level) * 50
        int velocity_drag_speed = (int)(450.0 + (5 - threat_level) * 50);
        output[idx++] = velocity_drag_speed;

        // 2. 速拖加速度(m/s²) - 按Python公式：290.0 + power * 100
        int velocity_drag_acceleration = (int)(290.0 + power * 100);
        output[idx++] = velocity_drag_acceleration;

        // 3. 距拖速度(m/s) - 按Python公式：20.0 * power
        int range_drag_speed = (int)(20.0 * power);
        output[idx++] = range_drag_speed;

        // 4. 距拖加速度(m/s²) - 按Python公式：15.0 + power * 10
        int range_drag_acceleration = (int)(15.0 + power * 10);
        output[idx++] = range_drag_acceleration;

        // 5. 捕获时间(s) - 按Python公式：1.6 * power
        int capture_time = (int)(1.6 * power);
        output[idx++] = capture_time;

        // 6. 拖引时间(s) - 按Python公式：3.5 + power
        int drag_time = (int)(3.5 + power);
        output[idx++] = drag_time;

        // 7. 保持时间(s) - 按Python公式：2.4 * power
        int hold_time = (int)(2.4 * power);
        output[idx++] = hold_time;

        // 8. 消失时间(s) - 按Python公式：0.8 + power * 0.5
        int disappear_time = (int)(0.8 + power * 0.5);
        output[idx++] = disappear_time;

        printf("    生成的拖引参数: [%d, %d, %d, %d, %d, %d, %d, %d]\n",
               velocity_drag_speed, velocity_drag_acceleration, range_drag_speed,
               range_drag_acceleration, capture_time, drag_time, hold_time, disappear_time);
    }

    return idx;
}
