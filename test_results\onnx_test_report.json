{"model_path": "models/onnx_fixed/jamming_model_ppo.onnx", "test_timestamp": "2025-07-19T09:28:00", "input_info": {"name": "state", "shape": ["batch_size", 12]}, "output_info": {"jamming_type_probs": ["batch_size", 5], "comb_params": ["batch_size", 25], "isrj_params": ["batch_size", 6], "broadband_params": ["batch_size", 1], "smart_noise_params": ["batch_size", 7], "deception_params": ["batch_size", 3], "threat_level_probs": ["batch_size", 5], "jamming_count_probs": ["batch_size", 4], "jamming1_probs": ["batch_size", 5], "jamming2_probs": ["batch_size", 5], "jamming3_probs": ["batch_size", 5]}, "statistics": {"jamming_type_probs": {"shape": [10, 5], "dtype": "float32", "min": 0.17032301425933838, "max": 0.23911958932876587, "mean": 0.20000000298023224, "std": 0.017942039296030998, "median": 0.19444482028484344, "q25": 0.18608379364013672, "q75": 0.20952153205871582, "is_probability": true, "sum_mean": 1.0, "sum_std": 4.61695606190915e-08, "range_type": "tanh_like (-1 to 1)"}, "comb_params": {"shape": [10, 25], "dtype": "float32", "min": -0.44896817207336426, "max": 0.49777787923812866, "mean": 0.07616351544857025, "std": 0.2284165918827057, "median": 0.13050813972949982, "q25": -0.06136368215084076, "q75": 0.25118643045425415, "range_type": "tanh_like (-1 to 1)"}, "isrj_params": {"shape": [10, 6], "dtype": "float32", "min": -0.3260810673236847, "max": 0.4552035331726074, "mean": 0.06544458121061325, "std": 0.23823900520801544, "median": 0.12670204043388367, "q25": -0.20775389671325684, "q75": 0.24990114569664001, "range_type": "tanh_like (-1 to 1)"}, "broadband_params": {"shape": [10, 1], "dtype": "float32", "min": -0.5747677683830261, "max": -0.23092739284038544, "mean": -0.39530840516090393, "std": 0.10259187966585159, "median": -0.3882729113101959, "q25": -0.4479796290397644, "q75": -0.3417883813381195, "range_type": "tanh_like (-1 to 1)"}, "smart_noise_params": {"shape": [10, 7], "dtype": "float32", "min": -0.22853006422519684, "max": 0.3717080056667328, "mean": 0.03867136687040329, "std": 0.14398498833179474, "median": 0.025540698319673538, "q25": -0.0575980581343174, "q75": 0.1083371713757515, "range_type": "tanh_like (-1 to 1)"}, "deception_params": {"shape": [10, 3], "dtype": "float32", "min": -0.21227772533893585, "max": 0.3079022467136383, "mean": 0.037254463881254196, "std": 0.14421656727790833, "median": 0.007158733904361725, "q25": -0.06904588639736176, "q75": 0.13328996300697327, "range_type": "tanh_like (-1 to 1)"}, "threat_level_probs": {"shape": [10, 5], "dtype": "float32", "min": 0.15790440142154694, "max": 0.2507181763648987, "mean": 0.20000000298023224, "std": 0.025044959038496017, "median": 0.1956886202096939, "q25": 0.17674791812896729, "q75": 0.21987499296665192, "is_probability": true, "sum_mean": 1.0, "sum_std": 6.529361940010858e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming_count_probs": {"shape": [10, 4], "dtype": "float32", "min": 0.20070961117744446, "max": 0.314206063747406, "mean": 0.25, "std": 0.03222482651472092, "median": 0.24656569957733154, "q25": 0.22414778172969818, "q75": 0.2653309106826782, "is_probability": true, "sum_mean": 1.0, "sum_std": 4.61695606190915e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming1_probs": {"shape": [10, 5], "dtype": "float32", "min": 0.17977838218212128, "max": 0.21995869278907776, "mean": 0.20000000298023224, "std": 0.010557328350841999, "median": 0.19727836549282074, "q25": 0.19144989550113678, "q75": 0.2083117812871933, "is_probability": true, "sum_mean": 1.0, "sum_std": 5.331201435865296e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming2_probs": {"shape": [10, 5], "dtype": "float32", "min": 0.17356720566749573, "max": 0.22835151851177216, "mean": 0.20000001788139343, "std": 0.014323769137263298, "median": 0.20201650261878967, "q25": 0.18834511935710907, "q75": 0.20956951379776, "is_probability": true, "sum_mean": 1.0, "sum_std": 8.637533710498246e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming3_probs": {"shape": [10, 5], "dtype": "float32", "min": 0.15998846292495728, "max": 0.2256515771150589, "mean": 0.20000000298023224, "std": 0.01855008490383625, "median": 0.20254409313201904, "q25": 0.1948135495185852, "q75": 0.21479643881320953, "is_probability": true, "sum_mean": 1.0, "sum_std": 5.960464477539063e-08, "range_type": "tanh_like (-1 to 1)"}, "performance": {"avg_inference_time": 0.00014455318450927733, "min_inference_time": 0.0, "max_inference_time": 0.0014455318450927734, "std_inference_time": 0.00043365955352783205, "success_rate": 1.0}}, "validation": {"overall_passed": true, "issues": [], "warnings": [], "details": {"jamming_type_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "comb_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "isrj_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "broadband_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "smart_noise_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "deception_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "threat_level_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "jamming_count_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "jamming1_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "jamming2_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "jamming3_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}}}}