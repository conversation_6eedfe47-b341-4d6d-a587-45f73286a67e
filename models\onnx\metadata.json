{"model_type": "radar_jamming_actor", "framework": "pytorch_to_onnx", "input_spec": {"name": "state", "shape": [null, 12], "dtype": "float32", "description": "雷达状态向量"}, "output_specs": [{"name": "jamming_type_probs", "shape": [null, 5], "dtype": "float32", "description": "干扰类型概率分布: [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]"}, {"name": "combination_scores", "shape": [null, 10], "dtype": "float32", "description": "组合干扰评估概率分布: 10种可能的两两组合"}, {"name": "comb_params", "shape": [null, 25], "dtype": "float32", "description": "梳状谱干扰参数: 25个参数"}, {"name": "isrj_params", "shape": [null, 6], "dtype": "float32", "description": "间歇采样转发参数: [频率偏移, 延时, 功率比, 脉冲数, 重复频率, 调制深度]"}, {"name": "broadband_params", "shape": [null, 1], "dtype": "float32", "description": "宽带阻塞噪声参数: [噪声功率]"}, {"name": "smart_noise_params", "shape": [null, 7], "dtype": "float32", "description": "灵巧噪声参数: 7个智能噪声调制参数"}, {"name": "deception_params", "shape": [null, 3], "dtype": "float32", "description": "拖引干扰参数: [距离拖引, 速度拖引, 角度拖引]"}], "model_config": {"actor_lr": 0.0003, "critic_lr": 0.001, "gamma": 0.99, "enable_learning": true, "enable_gpu": true, "gpu_device": 0, "batch_size": 64, "use_mixed_precision": false, "state_dim": 12, "action_dim": 71, "ppo_epochs": 4, "ppo_clip": 0.2, "entropy_coef": 0.01, "value_loss_coef": 0.5, "max_grad_norm": 0.5}, "export_config": {"opset_version": 11, "tolerance": 1e-05}}