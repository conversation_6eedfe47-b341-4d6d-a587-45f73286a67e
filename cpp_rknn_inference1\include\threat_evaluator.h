﻿#ifndef THREAT_EVALUATOR_H
#define THREAT_EVALUATOR_H

#ifdef __cplusplus
extern "C" {
#endif


typedef struct {
    double frequency_mhz;
    double pulse_width_us;
    double prt_us;
    double power_w;
    double distance_km;
    double speed_ms;
    double direction_deg;
    int work_mode;
} RadarParameters;


typedef struct {
    int threat_level;
    double threat_value;
    double confidence;
    double priority;

    double frequency_threat;
    double power_threat;
    double distance_threat;
    double mode_threat;
} ThreatAssessmentData;

typedef struct {
    int initialized;
    double frequency_weight;
    double power_weight;
    double distance_weight;
    double mode_weight;
} ThreatEvaluatorImpl;
// Threat evaluator handle
typedef void* ThreatEvaluator;


ThreatEvaluator* threat_evaluator_create(void);
void threat_evaluator_destroy(ThreatEvaluator* evaluator);
int threat_evaluator_assess(ThreatEvaluator* evaluator, 
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result);


const char* threat_get_level_description(int threat_level);
int threat_validate_radar_params(const RadarParameters* params);
double threat_calculate_composite_score(const ThreatAssessmentData* data);

#ifdef __cplusplus
}
#endif

#endif // THREAT_EVALUATOR_H
