#############################################################################
# Makefile for building: qt_c_radar
# Generated by qmake (3.1) (Qt 5.9.1)
# Project:  ..\qt_c_radar.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -DWIN32 -DWIN64 -DQT_DEPRECATED_WARNINGS -DHAS_RKNN=1 -D_CRT_SECURE_NO_WARNINGS -D_CRT_NONSTDC_NO_DEPRECATE -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -DWIN32_LEAN_AND_MEAN -DNOMINMAX -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -Zi -MDd -W3 -w44456 -w44457 -w44458 /Fdbuild\obj\qt_c_radar.vc.pdb $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /wd4101 /wd4819 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc /Fdbuild\obj\qt_c_radar.vc.pdb $(DEFINES)
INCPATH       = -I..\..\cpp_rknn_inference1 -I. -I..\rknn\include -I..\..\cpp_rknn_inference1 -I..\include -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtANGLE -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore -Ibuild\moc -I. -IC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /DEBUG /SUBSYSTEM:CONSOLE "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = /LIBPATH:C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Widgetsd.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Guid.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Networkd.lib C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Cored.lib 
QMAKE         = C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\qmake.exe
IDC           = idc
IDL           = midl /NOLOGO
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i
QINSTALL        = C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\qmake.exe -install qinstall -exe

####### Output directory

OBJECTS_DIR   = build\obj

####### Files

SOURCES       = ..\rknn\lib\rknn_api_stub.c \
		..\mainwindow.cpp \
		..\qt_main_c.cpp \
		..\src\threat_evaluator.c \
		..\src\rknn_inference.c \
		..\src\jamming_decision.c \
		..\workmodel\workmodel.cpp build\moc\moc_workmodel.cpp \
		build\moc\moc_mainwindow.cpp
OBJECTS       = build\obj\rknn_api_stub.obj \
		build\obj\mainwindow.obj \
		build\obj\qt_main_c.obj \
		build\obj\threat_evaluator.obj \
		build\obj\rknn_inference.obj \
		build\obj\jamming_decision.obj \
		build\obj\workmodel.obj \
		build\obj\moc_workmodel.obj \
		build\obj\moc_mainwindow.obj

DIST          =  Headers\mainwindow.h \
		..\include\threat_evaluator.h \
		..\include\rknn_inference.h \
		..\include\jamming_decision.h \
		..\workmodel\workmodel.h \
		..\mainwindow.h ..\rknn\lib\rknn_api_stub.c \
		..\mainwindow.cpp \
		..\qt_main_c.cpp \
		..\src\threat_evaluator.c \
		..\src\rknn_inference.c \
		..\src\jamming_decision.c \
		..\workmodel\workmodel.cpp
QMAKE_TARGET  = qt_c_radar
DESTDIR        = build\ #avoid trailing-slash linebreak
TARGET         = qt_c_radar.exe
DESTDIR_TARGET = build\qt_c_radar.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{..\src}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\src}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\src}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\src}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\workmodel}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\workmodel}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\workmodel}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\workmodel}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{build\moc}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{build\moc}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{build\moc}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{build\moc}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{.}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{.}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{.}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{.}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\rknn\lib}.cpp{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\rknn\lib}.cc{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\rknn\lib}.cxx{build\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

{..\rknn\lib}.c{build\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fobuild\obj\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Debug  $(DESTDIR_TARGET)

$(DESTDIR_TARGET): ui_mainwindow.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
build\obj\rknn_api_stub.obj build\obj\mainwindow.obj build\obj\qt_main_c.obj build\obj\threat_evaluator.obj build\obj\rknn_inference.obj build\obj\jamming_decision.obj build\obj\workmodel.obj build\obj\moc_workmodel.obj build\obj\moc_mainwindow.obj
$(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\qt_c_radar.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) qt_c_radar.zip $(SOURCES) $(DIST) ..\qt_c_radar.pro C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\spec_pre.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\common\angle.conf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\common\msvc-desktop.conf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\qconfig.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3danimation.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3danimation_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dcore.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dcore_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dextras.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dextras_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dinput.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dinput_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dlogic.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dlogic_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquick.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquick_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickanimation.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickextras.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickextras_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickinput.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickinput_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickrender.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickrender_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickscene2d.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3drender.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_3drender_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_accessibility_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axbase.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axbase_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axcontainer.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axcontainer_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axserver.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_axserver_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_bluetooth.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_bluetooth_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_bootstrap_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_charts.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_charts_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_concurrent.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_concurrent_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_core.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_core_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_datavisualization.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_datavisualization_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_dbus.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_dbus_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_designer.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_designer_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_designercomponents_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_egl_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_fb_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_gamepad.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_gamepad_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_gui.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_gui_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_help.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_help_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_location.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_location_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_multimedia.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_multimedia_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_multimediawidgets.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_network.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_network_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_networkauth.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_networkauth_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_nfc.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_nfc_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_opengl.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_opengl_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_openglextensions.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_openglextensions_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_positioning.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_positioning_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_printsupport.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_printsupport_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_purchasing.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_purchasing_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qml.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qml_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qmldebug_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qmldevtools_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qmltest.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qmltest_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quick.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quick_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quickcontrols2.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quickparticles_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quickwidgets.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_remoteobjects.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_remoteobjects_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_repparser.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_repparser_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_script.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_script_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_scripttools.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_scripttools_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_scxml.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_scxml_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_sensors.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_sensors_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_serialbus.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_serialbus_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_serialport.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_serialport_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_sql.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_sql_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_svg.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_svg_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_testlib.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_testlib_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_texttospeech.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_texttospeech_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_theme_support_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_uiplugin.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_uitools.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_uitools_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webchannel.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webchannel_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webengine.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webengine_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webenginecore.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webenginecore_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webenginecoreheaders_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webenginewidgets.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webenginewidgets_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_websockets.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_websockets_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webview.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_webview_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_widgets.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_widgets_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_winextras.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_winextras_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_xml.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_xml_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_xmlpatterns.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\modules\qt_lib_zlib_private.pri C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\qt_functions.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\qt_config.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\win32-msvc\qmake.conf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\spec_post.prf ..\.qmake.stash C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\exclusive_builds.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\common\msvc-version.conf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\toolchain.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\default_pre.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\win32\default_pre.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\resolve_config.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\exclusive_builds_post.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\default_post.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\build_pass.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\win32\console.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\qml_debug.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\precompile_header.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\warn_on.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\qt.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\resources.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\moc.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\win32\opengl.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\uic.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\qmake_use.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\file_copies.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\testcase_targets.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\exceptions.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\yacc.prf C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\lex.prf ..\qt_c_radar.pro C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Widgetsd.prl C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Guid.prl C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Networkd.prl C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\lib\Qt5Cored.prl    C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\data\dummy.cpp Headers\mainwindow.h ..\include\threat_evaluator.h ..\include\rknn_inference.h ..\include\jamming_decision.h ..\workmodel\workmodel.h ..\mainwindow.h ..\rknn\lib\rknn_api_stub.c ..\mainwindow.cpp ..\qt_main_c.cpp ..\src\threat_evaluator.c ..\src\rknn_inference.c ..\src\jamming_decision.c ..\workmodel\workmodel.cpp ..\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) build\obj\rknn_api_stub.obj build\obj\mainwindow.obj build\obj\qt_main_c.obj build\obj\threat_evaluator.obj build\obj\rknn_inference.obj build\obj\jamming_decision.obj build\obj\workmodel.obj build\obj\moc_workmodel.obj build\obj\moc_mainwindow.obj
	-$(DEL_FILE) build\qt_c_radar* build\qt_c_radar.exp build\obj\qt_c_radar.vc.pdb build\qt_c_radar.ilk build\qt_c_radar.idb

distclean: clean 
	-$(DEL_FILE) build\qt_c_radar.lib build\qt_c_radar.pdb
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: build\moc\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build\moc\moc_predefs.h
build\moc\moc_predefs.h: C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\data\dummy.cpp
	cl -BxC:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /wd4101 /wd4819 -Zi -MDd -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\mkspecs\features\data\dummy.cpp 2>NUL >build\moc\moc_predefs.h

compiler_moc_header_make_all: build\moc\moc_workmodel.cpp build\moc\moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build\moc\moc_workmodel.cpp build\moc\moc_mainwindow.cpp
build\moc\moc_workmodel.cpp: C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnamespace.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig-bootstrapped.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtcore-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsystemdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocessordetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcompilerdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtypeinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsysinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlogging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qflags.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbasicatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_bootstrap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qgenericatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_cxx11.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_msvc.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobalstatic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmutex.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnumeric.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qversiontagging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstring.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qchar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrefcount.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qarraydata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringbuilder.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qalgorithms.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiterator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhashfunctions.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpair.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearraylist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregexp.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringmatcher.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qscopedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmetatype.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvarlengtharray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontainerfwd.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgetsglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtguiglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtgui-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgets-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs_win.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmargins.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpaintdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrect.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsize.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpoint.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpalette.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcolor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgb.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgba64.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qbrush.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvector.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qmatrix.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpolygon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qregion.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdatastream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiodevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qline.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtransform.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpainterpath.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qimage.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixelformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qshareddata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhash.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfont.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontmetrics.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsizepolicy.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qkeysequence.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvariant.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdebug.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextstream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlocale.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qset.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontiguouscache.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurlquery.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfile.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfiledevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector2d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtouchdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsplitter.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qframe.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qpushbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qicon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvalidator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregularexpression.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcombobox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractitemdelegate.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyleoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyle.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabbar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qrubberband.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qabstractitemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qstandarditemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qlineedit.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpen.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcheckbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsettings.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmessagebox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qdialog.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocess.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpserver.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetworkglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetwork-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qabstractsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qhostaddress.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qudpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qthread.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector3d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qelapsedtimer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qreadwritelock.h \
		..\workmodel\workmodelrely.h \
		..\workmodel\workmodel.h \
		build\moc\moc_predefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\moc.exe
	C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include build/moc/moc_predefs.h -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/mkspecs/win32-msvc -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1 -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1/rknn/include -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1 -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1/include -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtWidgets -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtGui -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtANGLE -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtNetwork -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtCore -I. -I"C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\INCLUDE" -I"C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\ATLMFC\INCLUDE" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.10150.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.6\include\um" -I"C:\Program Files (x86)\Windows Kits\8.1\include\shared" -I"C:\Program Files (x86)\Windows Kits\8.1\include\um" -I"C:\Program Files (x86)\Windows Kits\8.1\include\winrt" ..\workmodel\workmodel.h -o build\moc\moc_workmodel.cpp

build\moc\moc_mainwindow.cpp: C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\QMainWindow \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmainwindow.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgetsglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtguiglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig-bootstrapped.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtcore-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsystemdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocessordetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcompilerdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtypeinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsysinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlogging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qflags.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbasicatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_bootstrap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qgenericatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_cxx11.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_msvc.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobalstatic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmutex.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnumeric.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qversiontagging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtgui-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgets-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnamespace.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs_win.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstring.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qchar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrefcount.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qarraydata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringbuilder.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qalgorithms.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiterator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhashfunctions.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpair.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearraylist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregexp.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringmatcher.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qscopedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmetatype.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvarlengtharray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontainerfwd.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmargins.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpaintdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrect.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsize.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpoint.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpalette.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcolor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgb.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgba64.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qbrush.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvector.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qmatrix.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpolygon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qregion.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdatastream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiodevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qline.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtransform.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpainterpath.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qimage.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixelformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qshareddata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhash.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfont.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontmetrics.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsizepolicy.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qkeysequence.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvariant.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdebug.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextstream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlocale.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qset.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontiguouscache.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurlquery.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfile.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfiledevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector2d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtouchdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qicon.h \
		..\mainwindow.h \
		build\moc\moc_predefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\moc.exe
	C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include build/moc/moc_predefs.h -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/mkspecs/win32-msvc -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1 -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1/rknn/include -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1 -ID:/zhuhongjun/P2336/P2336_7.15/gpu_version/cpp_rknn_inference1/include -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtWidgets -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtGui -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtANGLE -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtNetwork -IC:/Qt/Qt5.9.1/5.9.1/msvc2015_64/include/QtCore -I. -I"C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\INCLUDE" -I"C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\ATLMFC\INCLUDE" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.10150.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.6\include\um" -I"C:\Program Files (x86)\Windows Kits\8.1\include\shared" -I"C:\Program Files (x86)\Windows Kits\8.1\include\um" -I"C:\Program Files (x86)\Windows Kits\8.1\include\winrt" ..\mainwindow.h -o build\moc\moc_mainwindow.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ..\mainwindow.ui \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\uic.exe
	C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\bin\uic.exe ..\mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

build\obj\rknn_api_stub.obj: ..\rknn\lib\rknn_api_stub.c ..\rknn\include\rknn_api.h

build\obj\mainwindow.obj: ..\mainwindow.cpp ../mainwindow.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\QMainWindow \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmainwindow.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgetsglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtguiglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig-bootstrapped.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtcore-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsystemdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocessordetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcompilerdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtypeinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsysinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlogging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qflags.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbasicatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_bootstrap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qgenericatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_cxx11.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_msvc.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobalstatic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmutex.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnumeric.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qversiontagging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtgui-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgets-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnamespace.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs_win.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstring.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qchar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrefcount.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qarraydata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringbuilder.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qalgorithms.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiterator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhashfunctions.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpair.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearraylist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregexp.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringmatcher.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qscopedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmetatype.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvarlengtharray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontainerfwd.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmargins.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpaintdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrect.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsize.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpoint.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpalette.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcolor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgb.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgba64.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qbrush.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvector.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qmatrix.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpolygon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qregion.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdatastream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiodevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qline.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtransform.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpainterpath.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qimage.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixelformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qshareddata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhash.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfont.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontmetrics.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsizepolicy.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qkeysequence.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvariant.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdebug.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextstream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlocale.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qset.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontiguouscache.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurlquery.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfile.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfiledevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector2d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtouchdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qicon.h \
		ui_mainwindow.h \
		../workmodel/workmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsplitter.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qframe.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qpushbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvalidator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregularexpression.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcombobox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractitemdelegate.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyleoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyle.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabbar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qrubberband.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qabstractitemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qstandarditemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qlineedit.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpen.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcheckbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsettings.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmessagebox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qdialog.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocess.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpserver.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetworkglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetwork-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qabstractsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qhostaddress.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qudpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qthread.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector3d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qelapsedtimer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qreadwritelock.h \
		..\workmodel\workmodelrely.h

build\obj\qt_main_c.obj: ..\qt_main_c.cpp C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\QCoreApplication \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreapplication.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig-bootstrapped.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtcore-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsystemdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocessordetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcompilerdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtypeinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsysinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlogging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qflags.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbasicatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_bootstrap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qgenericatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_cxx11.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_msvc.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobalstatic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmutex.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnumeric.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qversiontagging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstring.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qchar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrefcount.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnamespace.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qarraydata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringbuilder.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qalgorithms.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiterator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhashfunctions.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpair.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearraylist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregexp.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringmatcher.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qscopedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmetatype.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvarlengtharray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontainerfwd.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qeventloop.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\QTextCodec \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextcodec.h \
		../mainwindow.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\QMainWindow \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmainwindow.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgetsglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtguiglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtgui-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgets-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs_win.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmargins.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpaintdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrect.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsize.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpoint.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpalette.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcolor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgb.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgba64.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qbrush.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvector.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qmatrix.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpolygon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qregion.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdatastream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiodevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qline.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtransform.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpainterpath.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qimage.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixelformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qshareddata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhash.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfont.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontmetrics.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsizepolicy.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qkeysequence.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvariant.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdebug.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextstream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlocale.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qset.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontiguouscache.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurlquery.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfile.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfiledevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector2d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtouchdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qicon.h \
		../workmodel/workmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsplitter.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qframe.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qpushbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvalidator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregularexpression.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcombobox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractitemdelegate.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyleoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyle.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabbar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qrubberband.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qabstractitemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qstandarditemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qlineedit.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpen.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcheckbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsettings.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmessagebox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qdialog.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocess.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpserver.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetworkglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetwork-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qabstractsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qhostaddress.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qudpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qthread.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector3d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qelapsedtimer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qreadwritelock.h \
		..\workmodel\workmodelrely.h \
		../include/threat_evaluator.h \
		../include/rknn_inference.h \
		../include/jamming_decision.h

build\obj\threat_evaluator.obj: ..\src\threat_evaluator.c ../include/threat_evaluator.h

build\obj\rknn_inference.obj: ..\src\rknn_inference.c ../include/rknn_inference.h \
		..\rknn\include\rknn_api.h

build\obj\jamming_decision.obj: ..\src\jamming_decision.c ../include/jamming_decision.h \
		../include/rknn_inference.h

build\obj\workmodel.obj: ..\workmodel\workmodel.cpp ../workmodel/workmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnamespace.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig-bootstrapped.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qconfig.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtcore-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsystemdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocessordetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcompilerdetection.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtypeinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsysinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlogging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qflags.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbasicatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_bootstrap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qgenericatomic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_cxx11.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qatomic_msvc.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qglobalstatic.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmutex.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qnumeric.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qversiontagging.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobjectdefs_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstring.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qchar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrefcount.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qarraydata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringbuilder.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qalgorithms.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiterator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhashfunctions.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpair.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qbytearraylist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringlist.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregexp.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qstringmatcher.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcoreevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qscopedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmetatype.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvarlengtharray.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontainerfwd.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qobject_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgetsglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtguiglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtgui-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtwidgets-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qwindowdefs_win.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmargins.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpaintdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qrect.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsize.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qpoint.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpalette.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcolor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgb.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qrgba64.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qbrush.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvector.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qmatrix.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpolygon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qregion.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdatastream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qiodevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qline.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtransform.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpainterpath.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qimage.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixelformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpixmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qshareddata.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qhash.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsharedpointer_impl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfont.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontmetrics.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qfontinfo.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsizepolicy.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qkeysequence.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qevent.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qvariant.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qmap.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qdebug.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qtextstream.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qlocale.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qset.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qcontiguouscache.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurl.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qurlquery.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfile.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qfiledevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector2d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtouchdevice.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qsplitter.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qframe.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qpushbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractbutton.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qicon.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractspinbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvalidator.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qregularexpression.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcombobox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractitemdelegate.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyleoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qabstractslider.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qstyle.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabbar.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qtabwidget.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qrubberband.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qabstractitemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qstandarditemmodel.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qlineedit.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextcursor.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextformat.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qpen.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qtextoption.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qcheckbox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qsettings.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qmessagebox.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtWidgets\qdialog.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qprocess.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpserver.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetworkglobal.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtnetwork-config.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qabstractsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qhostaddress.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qtcpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtNetwork\qudpsocket.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qthread.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtGui\qvector3d.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qelapsedtimer.h \
		C:\Qt\Qt5.9.1\5.9.1\msvc2015_64\include\QtCore\qreadwritelock.h \
		..\workmodel\workmodelrely.h

build\obj\moc_workmodel.obj: build\moc\moc_workmodel.cpp 

build\obj\moc_mainwindow.obj: build\moc\moc_mainwindow.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

