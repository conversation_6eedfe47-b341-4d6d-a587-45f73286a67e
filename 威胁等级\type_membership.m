function [membership_radar,membership_platform]= type_membership(radar_type,platform_type)
% 计算情报信息的隶属度
% 输入:
%   radar_type - 雷达类型字
%   platform_type - 平台类型
% 输出:
%   membership - 隶属度值

% 根据雷达类型确定隶属度
if contains(radar_type, ["火控雷达", "制导雷达"])
    membership_radar = 1.0;
elseif contains(radar_type, "引导指挥雷达")
    membership_radar = 0.8;
elseif contains(radar_type, "搜索雷达")
    membership_radar = 0.5;
elseif contains(radar_type, "导航雷达")
    membership_radar = 0.1;
else
    membership_radar = 0.0;
end


% 根据平台类型确定隶属度
if contains(platform_type, "导弹")
    membership_platform = 1.0;
elseif contains(platform_type, ["轰炸机", "战斗机", "战斗舰艇"])
    membership_platform = 0.9;
elseif contains(platform_type, ["预警机", "反潜机", "侦察船"])
    membership_platform = 0.7;
elseif contains(platform_type, ["空中加油机", "勤务舰船"])
    membership_platform = 0.5;
else
    membership_platform = 0;
end


end