﻿#include "jamming_decision.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>

/**
 * 干扰决策器实现
 */

// 干扰决策器内部结构
typedef struct {
    int initialized;
    double jamming_threshold;       // 干扰阈值
    double min_power;              // 最小功率
    double max_power;              // 最大功率
    
    // 统计信息
    int decision_count;
    double total_decision_time;
    int jamming_decisions;         // 干扰决策次数
    int no_jamming_decisions;      // 不干扰决策次数
} JammingDecisionImpl;

/**
 * 创建干扰决策器
 */
JammingDecision* jamming_decision_create(void) {
    JammingDecisionImpl* impl = (JammingDecisionImpl*)malloc(sizeof(JammingDecisionImpl));
    if (!impl) {
        return NULL;
    }
    
    // 初始化参数
    impl->initialized = 1;
    impl->jamming_threshold = JAMMING_THRESHOLD_MEDIUM;
    impl->min_power = JAMMING_POWER_MIN;
    impl->max_power = JAMMING_POWER_MAX;
    
    // 初始化统计信息
    impl->decision_count = 0;
    impl->total_decision_time = 0.0;
    impl->jamming_decisions = 0;
    impl->no_jamming_decisions = 0;
    
    printf("干扰决策器初始化成功\n");
    return (JammingDecision*)impl;
}

/**
 * 销毁干扰决策器
 */
void jamming_decision_destroy(JammingDecision* decision) {
    if (decision) {
        free(decision);
        printf("干扰决策器已销毁\n");
    }
}

/**
 * 根据威胁等级选择干扰类型
 */
static JammingType select_jamming_type(const ThreatAssessmentData* threat_data,
                                      const RadarParameters* radar_params) {
    // 基于威胁等级和雷达参数选择干扰类型
    if (threat_data->threat_level <= 2) {  // 高威胁
        if (radar_params->frequency_mhz > 8000.0) {
            return JAMMING_SMART_NOISE;  // 高频使用灵巧噪声
        } else {
            return JAMMING_DECEPTION;    // 低频使用拖引干扰
        }
    } else if (threat_data->threat_level == 3) {  // 中等威胁
        if (radar_params->work_mode == WORK_MODE_TRACK) {
            return JAMMING_ISRJ;         // 跟踪模式使用间歇采样
        } else {
            return JAMMING_BROADBAND;    // 其他模式使用宽带噪声
        }
    } else {  // 低威胁
        if (threat_data->threat_value > 0.3) {
            return JAMMING_BROADBAND;    // 轻微干扰
        } else {
            return JAMMING_NONE;         // 无需干扰
        }
    }
}

/**
 * 计算干扰功率
 */
static double calculate_jamming_power(JammingType type,
                                    const ThreatAssessmentData* threat_data,
                                    const RadarParameters* radar_params) {
    if (type == JAMMING_NONE) {
        return 0.0;
    }
    
    // 基础功率基于威胁等级
    double base_power = (6.0 - threat_data->threat_level) / 5.0;
    
    // 类型调整系数
    double type_multiplier = 1.0;
    switch (type) {
        case JAMMING_ISRJ:
            type_multiplier = 0.8;
            break;
        case JAMMING_BROADBAND:
            type_multiplier = 0.6;
            break;
        case JAMMING_SMART_NOISE:
            type_multiplier = 0.7;
            break;
        case JAMMING_DECEPTION:
            type_multiplier = 0.9;
            break;
        default:
            type_multiplier = 0.5;
            break;
    }
    
    // 距离调整
    double distance_factor = 1.0;
    if (radar_params->distance_km < 50.0) {
        distance_factor = 1.2;  // 近距离增强
    } else if (radar_params->distance_km > 150.0) {
        distance_factor = 0.8;  // 远距离减弱
    }
    
    double power = base_power * type_multiplier * distance_factor;
    
    // 限制在合理范围内
    if (power < JAMMING_POWER_MIN) power = JAMMING_POWER_MIN;
    if (power > JAMMING_POWER_MAX) power = JAMMING_POWER_MAX;
    
    return power;
}

/**
 * 计算干扰频率
 */
static double calculate_jamming_frequency(JammingType type,
                                        const RadarParameters* radar_params) {
    switch (type) {
        case JAMMING_ISRJ:
        case JAMMING_DECEPTION:
            // 间歇采样和拖引干扰使用相同频率
            return radar_params->frequency_mhz;
            
        case JAMMING_BROADBAND:
            // 宽带噪声使用中心频率
            return radar_params->frequency_mhz;
            
        case JAMMING_SMART_NOISE:
            // 灵巧噪声可能偏移频率
            return radar_params->frequency_mhz * 1.05;  // 5%偏移
            
        default:
            return radar_params->frequency_mhz;
    }
}

/**
 * 设置干扰参数
 */
static void set_jamming_parameters(JammingDecisionData* result,
                                  JammingType type,
                                  const RadarParameters* radar_params) {
    switch (type) {
        case JAMMING_ISRJ:
            result->bandwidth_mhz = radar_params->frequency_mhz * 0.1;  // 10%带宽
            result->duration_ms = 100.0;
            result->delay_ms = 5.0;
            
            // 间歇采样特定参数
            result->advanced_params.isrj_params.sampling_rate = 0.5;
            result->advanced_params.isrj_params.duty_cycle = 0.3;
            result->advanced_params.isrj_params.pulse_count = 10;
            break;
            
        case JAMMING_BROADBAND:
            result->bandwidth_mhz = radar_params->frequency_mhz * 0.2;  // 20%带宽
            result->duration_ms = 200.0;
            result->delay_ms = 2.0;
            
            // 宽带噪声特定参数
            result->advanced_params.broadband_params.noise_power = result->jamming_power * 0.8;
            result->advanced_params.broadband_params.center_frequency_mhz = radar_params->frequency_mhz;
            break;
            
        case JAMMING_SMART_NOISE:
            result->bandwidth_mhz = radar_params->frequency_mhz * 0.05; // 5%带宽
            result->duration_ms = 150.0;
            result->delay_ms = 1.0;
            
            // 灵巧噪声特定参数
            result->advanced_params.smart_noise_params.adaptation_rate = 0.1;
            result->advanced_params.smart_noise_params.filter_order = 8;
            break;
            
        case JAMMING_DECEPTION:
            result->bandwidth_mhz = radar_params->frequency_mhz * 0.01; // 1%带宽
            result->duration_ms = 300.0;
            result->delay_ms = 10.0;
            
            // 拖引干扰特定参数
            result->advanced_params.deception_params.false_target_count = 3.0;
            result->advanced_params.deception_params.range_offset_km = 5.0;
            result->advanced_params.deception_params.velocity_offset_ms = 20.0;
            break;
            
        default:
            result->bandwidth_mhz = 0.0;
            result->duration_ms = 0.0;
            result->delay_ms = 0.0;
            break;
    }
}

/**
 * 生成策略描述
 */
static void generate_strategy_description(JammingDecisionData* result) {
    if (!result->should_jam) {
        strcpy(result->strategy_description, "威胁等级较低，无需干扰");
        return;
    }
    
    const char* type_name = jamming_get_type_description(result->jamming_type);
    
    snprintf(result->strategy_description, sizeof(result->strategy_description),
             "采用%s，功率%.2f，频率%.1f MHz，持续%.1f ms",
             type_name,
             result->jamming_power,
             result->jamming_frequency_mhz,
             result->duration_ms);
}

/**
 * 执行干扰决策
 */
int jamming_decision_make(JammingDecision* decision,
                         const RadarParameters* radar_params,
                         const ThreatAssessmentData* threat_data,
                         JammingDecisionData* result) {
    if (!decision || !radar_params || !threat_data || !result) {
        return JAMMING_ERROR_INVALID;
    }
    
    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    if (!impl->initialized) {
        return JAMMING_ERROR_INVALID;
    }
    
    // 记录开始时间
    clock_t start_time = clock();
    
    // 清空结果结构
    memset(result, 0, sizeof(JammingDecisionData));
    
    // 判断是否需要干扰
    if (threat_data->threat_value >= impl->jamming_threshold) {
        result->should_jam = 1;
        result->jamming_type = select_jamming_type(threat_data, radar_params);
        
        // 计算干扰参数
        result->jamming_power = calculate_jamming_power(result->jamming_type, threat_data, radar_params);
        result->jamming_frequency_mhz = calculate_jamming_frequency(result->jamming_type, radar_params);
        
        // 设置详细参数
        set_jamming_parameters(result, result->jamming_type, radar_params);
        
        impl->jamming_decisions++;
    } else {
        result->should_jam = 0;
        result->jamming_type = JAMMING_NONE;
        result->jamming_power = 0.0;
        result->jamming_frequency_mhz = 0.0;
        
        impl->no_jamming_decisions++;
    }
    
    // 计算决策置信度
    result->decision_confidence = threat_data->confidence * 0.8 + 0.2;
    
    // 生成策略描述
    generate_strategy_description(result);
    
    // 计算处理时间
    clock_t end_time = clock();
    result->processing_time_ms = ((double)(end_time - start_time)) / CLOCKS_PER_SEC * 1000.0;
    
    // 更新统计信息
    impl->decision_count++;
    impl->total_decision_time += result->processing_time_ms;
    
    return JAMMING_SUCCESS;
}

/**
 * 基于RKNN推理的决策
 */
int jamming_decision_make_with_rknn(JammingDecision* decision,
                                   const RadarParameters* radar_params,
                                   const ThreatAssessmentData* threat_data,
                                   const RKNNInferenceData* rknn_result,
                                   JammingDecisionData* result) {
    if (!rknn_result || !rknn_result->success) {
        // 如果RKNN推理失败，回退到基础决策
        return jamming_decision_make(decision, radar_params, threat_data, result);
    }
    
    // 使用RKNN推理结果进行决策
    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    clock_t start_time = clock();
    
    memset(result, 0, sizeof(JammingDecisionData));
    
    // 使用RKNN预测的干扰类型
    result->jamming_type = (JammingType)rknn_result->predicted_jamming_type;
    result->should_jam = (result->jamming_type != JAMMING_NONE);
    
    if (result->should_jam) {
        // 使用RKNN预测结果调整功率
        float jamming_prob = rknn_result->jamming_type_probs[result->jamming_type];
        result->jamming_power = calculate_jamming_power(result->jamming_type, threat_data, radar_params) * jamming_prob;
        result->jamming_frequency_mhz = calculate_jamming_frequency(result->jamming_type, radar_params);
        
        set_jamming_parameters(result, result->jamming_type, radar_params);
        impl->jamming_decisions++;
    } else {
        impl->no_jamming_decisions++;
    }
    
    // 使用RKNN的置信度
    result->decision_confidence = 0.9;  // RKNN决策置信度较高
    
    generate_strategy_description(result);
    
    clock_t end_time = clock();
    result->processing_time_ms = ((double)(end_time - start_time)) / CLOCKS_PER_SEC * 1000.0;
    
    impl->decision_count++;
    impl->total_decision_time += result->processing_time_ms;
    
    return JAMMING_SUCCESS;
}

/**
 * 批量决策
 */
int jamming_decision_batch_make(JammingDecision* decision,
                               const RadarParameters* radar_params_array,
                               const ThreatAssessmentData* threat_data_array,
                               int batch_size,
                               JammingDecisionData* results) {
    if (!decision || !radar_params_array || !threat_data_array || !results || batch_size <= 0) {
        return JAMMING_ERROR_INVALID;
    }
    
    int success_count = 0;
    
    for (int i = 0; i < batch_size; i++) {
        int ret = jamming_decision_make(decision,
                                       &radar_params_array[i],
                                       &threat_data_array[i],
                                       &results[i]);
        if (ret == JAMMING_SUCCESS) {
            success_count++;
        }
    }
    
    return success_count;
}

/**
 * 设置干扰阈值
 */
int jamming_decision_set_threshold(JammingDecision* decision, double threshold) {
    if (!decision || threshold < 0.0 || threshold > 1.0) {
        return JAMMING_ERROR_THRESHOLD;
    }
    
    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    impl->jamming_threshold = threshold;
    
    printf("干扰阈值设置为: %.3f\n", threshold);
    return JAMMING_SUCCESS;
}

/**
 * 设置功率限制
 */
int jamming_decision_set_power_limits(JammingDecision* decision, 
                                     double min_power, 
                                     double max_power) {
    if (!decision || min_power < 0.0 || max_power > 1.0 || min_power >= max_power) {
        return JAMMING_ERROR_POWER;
    }
    
    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    impl->min_power = min_power;
    impl->max_power = max_power;
    
    printf("功率限制设置为: [%.3f, %.3f]\n", min_power, max_power);
    return JAMMING_SUCCESS;
}

/**
 * 获取干扰类型描述
 */
const char* jamming_get_type_description(JammingType type) {
    switch (type) {
        case JAMMING_NONE:
            return "无干扰";
        case JAMMING_ISRJ:
            return "间歇采样转发";
        case JAMMING_BROADBAND:
            return "宽带噪声干扰";
        case JAMMING_SMART_NOISE:
            return "灵巧噪声";
        case JAMMING_DECEPTION:
            return "拖引干扰";
        default:
            return "未知干扰类型";
    }
}

/**
 * 验证决策参数
 */
int jamming_decision_validate_params(const JammingDecisionData* decision_data) {
    if (!decision_data) {
        return JAMMING_ERROR_INVALID;
    }
    
    if (decision_data->should_jam) {
        if (decision_data->jamming_power < 0.0 || decision_data->jamming_power > 1.0) {
            return JAMMING_ERROR_POWER;
        }
        
        if (decision_data->jamming_type < JAMMING_NONE || decision_data->jamming_type > JAMMING_DECEPTION) {
            return JAMMING_ERROR_TYPE;
        }
    }
    
    return JAMMING_SUCCESS;
}

/**
 * 估算干扰效果
 */
double jamming_estimate_effectiveness(JammingType type,
                                    const RadarParameters* radar_params,
                                    const JammingDecisionData* decision_data) {
    if (!radar_params || !decision_data || type == JAMMING_NONE) {
        return 0.0;
    }
    
    double effectiveness = 0.5;  // 基础效果
    
    // 根据干扰类型调整
    switch (type) {
        case JAMMING_ISRJ:
            effectiveness = 0.7;
            break;
        case JAMMING_BROADBAND:
            effectiveness = 0.6;
            break;
        case JAMMING_SMART_NOISE:
            effectiveness = 0.8;
            break;
        case JAMMING_DECEPTION:
            effectiveness = 0.9;
            break;
        default:
            effectiveness = 0.3;
            break;
    }
    
    // 功率调整
    effectiveness *= decision_data->jamming_power;
    
    // 频率匹配度调整
    double freq_match = 1.0 - fabs(decision_data->jamming_frequency_mhz - radar_params->frequency_mhz) / radar_params->frequency_mhz;
    effectiveness *= freq_match;
    
    return fmin(effectiveness, 1.0);
}

/**
 * 获取统计信息
 */
const char* jamming_decision_get_stats(JammingDecision* decision) {
    static char stats_buffer[256];
    
    if (!decision) {
        snprintf(stats_buffer, sizeof(stats_buffer), "干扰决策器无效");
        return stats_buffer;
    }
    
    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    
    double avg_time = impl->decision_count > 0 ? 
                     impl->total_decision_time / impl->decision_count : 0.0;
    
    snprintf(stats_buffer, sizeof(stats_buffer),
             "决策次数: %d, 干扰决策: %d, 无干扰决策: %d, 平均耗时: %.2f ms",
             impl->decision_count,
             impl->jamming_decisions,
             impl->no_jamming_decisions,
             avg_time);
    
    return stats_buffer;
}
