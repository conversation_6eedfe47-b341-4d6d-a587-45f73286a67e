@echo off
echo === Qt Radar Jamming System Simple Build ===

REM Setup Visual Studio environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)

echo Visual Studio environment setup successful

REM Check ONNX Runtime
if exist "..\third_party\onnxruntime\lib\onnxruntime.lib" (
    echo Found ONNX Runtime, enabling real inference...
    set HAS_ONNXRUNTIME=1
) else (
    echo ONNX Runtime not found, using simulation mode...
    set HAS_ONNXRUNTIME=0
)

REM Create build directory
if not exist "build" mkdir build

REM Copy ONNX Runtime DLL if exists
if exist "..\third_party\onnxruntime\lib\onnxruntime.dll" (
    echo Copying ONNX Runtime DLL...
    copy "..\third_party\onnxruntime\lib\onnxruntime.dll" build\ > nul
)

echo Compiling...

REM Compile with MSVC directly
cl /EHsc /std:c++11 ^
   /I"..\include" ^
   /I"..\third_party\onnxruntime\include" ^
   /DHAS_ONNXRUNTIME=%HAS_ONNXRUNTIME% ^
   /Fe:build\RadarJammingQt.exe ^
   main.cpp ^
   ..\src\external_api.cpp ^
   ..\src\data_structures.cpp ^
   ..\src\jamming_decision.cpp ^
   ..\src\onnx_inference.cpp ^
   ..\src\threat_evaluator.cpp ^
   /link /LIBPATH:"..\third_party\onnxruntime\lib" onnxruntime.lib

if errorlevel 1 (
    echo Error: Compilation failed
    pause
    exit /b 1
)

echo.
echo Build successful!
echo Executable: build\RadarJammingQt.exe
echo.

REM Run test
echo Running test...
cd build
RadarJammingQt.exe
cd ..

echo.
echo Test completed
echo Build completed successfully!
pause
