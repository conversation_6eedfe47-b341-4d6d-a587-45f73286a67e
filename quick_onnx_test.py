#!/usr/bin/env python3
"""
快速测试ONNX转换功能
"""

import os
import sys
import torch
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖库"""
    print("检查依赖库...")
    
    missing_deps = []
    
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import onnx
        print(f"✓ ONNX: {onnx.__version__}")
    except ImportError:
        missing_deps.append("onnx")
    
    try:
        import onnxruntime as ort
        print(f"✓ ONNX Runtime: {ort.__version__}")
    except ImportError:
        missing_deps.append("onnxruntime")
    
    if missing_deps:
        print(f"✗ 缺少依赖: {missing_deps}")
        print("请安装: pip install torch onnx onnxruntime")
        return False
    
    return True

def check_model_exists():
    """检查模型文件是否存在"""
    print("\n检查模型文件...")
    
    model_paths = [
        "models/jamming_model_ppo.pth",
        "models/jamming_model.pth"
    ]
    
    for path in model_paths:
        if os.path.exists(path):
            print(f"✓ 找到模型: {path}")
            return path
    
    print("✗ 未找到训练好的模型")
    print("请先运行训练: python complete_jamming_system.py --episodes 100")
    return None

def test_conversion():
    """测试转换功能"""
    print("\n测试ONNX转换...")
    
    try:
        # 导入转换模块
        from convert_to_onnx import load_trained_model, convert_actor_to_onnx
        
        # 检查模型
        model_path = check_model_exists()
        if not model_path:
            return False
        
        # 加载模型
        print("加载PPO模型...")
        accelerator = load_trained_model(model_path)
        
        # 创建输出目录
        output_dir = "models/onnx_test"
        os.makedirs(output_dir, exist_ok=True)
        
        # 转换Actor网络
        actor_onnx_path = os.path.join(output_dir, "test_actor.onnx")
        success = convert_actor_to_onnx(
            accelerator.actor, actor_onnx_path, state_dim=12, batch_size=1
        )
        
        if success:
            print(f"✓ ONNX转换成功: {actor_onnx_path}")
            
            # 验证文件大小
            file_size = os.path.getsize(actor_onnx_path) / (1024 * 1024)  # MB
            print(f"✓ ONNX模型大小: {file_size:.2f} MB")
            
            return True
        else:
            print("✗ ONNX转换失败")
            return False
            
    except Exception as e:
        print(f"✗ 转换测试失败: {e}")
        return False

def test_inference():
    """测试ONNX推理"""
    print("\n测试ONNX推理...")
    
    try:
        import onnxruntime as ort
        
        # 检查ONNX模型
        onnx_path = "models/onnx_test/test_actor.onnx"
        if not os.path.exists(onnx_path):
            print("✗ ONNX模型不存在，请先运行转换测试")
            return False
        
        # 创建推理会话
        session = ort.InferenceSession(onnx_path)
        print("✓ ONNX Runtime会话创建成功")
        
        # 创建测试输入
        test_input = np.random.randn(1, 12).astype(np.float32)
        
        # 推理
        inputs = {session.get_inputs()[0].name: test_input}
        outputs = session.run(None, inputs)
        
        print(f"✓ 推理成功，输出数量: {len(outputs)}")
        print(f"✓ 威胁等级概率形状: {outputs[0].shape}")
        print(f"✓ 干扰类型概率形状: {outputs[1].shape}")
        
        # 检查输出合理性
        threat_probs = outputs[0][0]
        jamming_probs = outputs[1][0]
        
        if np.abs(np.sum(threat_probs) - 1.0) < 0.01:
            print("✓ 威胁等级概率归一化正确")
        else:
            print("⚠ 威胁等级概率归一化异常")
        
        if np.abs(np.sum(jamming_probs) - 1.0) < 0.01:
            print("✓ 干扰类型概率归一化正确")
        else:
            print("⚠ 干扰类型概率归一化异常")
        
        return True
        
    except Exception as e:
        print(f"✗ 推理测试失败: {e}")
        return False

def performance_test():
    """性能测试"""
    print("\n性能测试...")
    
    try:
        import onnxruntime as ort
        import time
        
        onnx_path = "models/onnx_test/test_actor.onnx"
        if not os.path.exists(onnx_path):
            print("✗ ONNX模型不存在")
            return False
        
        session = ort.InferenceSession(onnx_path)
        test_input = np.random.randn(1, 12).astype(np.float32)
        inputs = {session.get_inputs()[0].name: test_input}
        
        # 预热
        for _ in range(10):
            session.run(None, inputs)
        
        # 计时测试
        num_runs = 1000
        start_time = time.time()
        
        for _ in range(num_runs):
            session.run(None, inputs)
        
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_runs * 1000  # ms
        fps = 1000 / avg_time
        
        print(f"✓ 平均推理时间: {avg_time:.2f} ms")
        print(f"✓ 推理吞吐量: {fps:.1f} FPS")
        
        if avg_time < 10:
            print("✓ 推理速度优秀 (<10ms)")
        elif avg_time < 50:
            print("✓ 推理速度良好 (<50ms)")
        else:
            print("⚠ 推理速度较慢 (>50ms)")
        
        return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    print("\n清理测试文件...")
    
    try:
        import shutil
        test_dir = "models/onnx_test"
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print("✓ 测试文件已清理")
    except Exception as e:
        print(f"⚠ 清理失败: {e}")

def main():
    """主函数"""
    print("PPO模型ONNX转换快速测试")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 测试转换
    conversion_ok = test_conversion()
    
    # 测试推理
    inference_ok = False
    if conversion_ok:
        inference_ok = test_inference()
    
    # 性能测试
    performance_ok = False
    if inference_ok:
        performance_ok = performance_test()
    
    # 清理
    cleanup()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"依赖检查: ✓")
    print(f"ONNX转换: {'✓' if conversion_ok else '✗'}")
    print(f"推理测试: {'✓' if inference_ok else '✗'}")
    print(f"性能测试: {'✓' if performance_ok else '✗'}")
    
    if conversion_ok and inference_ok:
        print("\n✅ ONNX转换功能正常！")
        print("\n下一步:")
        print("1. 运行 python convert_to_onnx.py 进行完整转换")
        print("2. 运行 python onnx_inference_example.py 测试推理")
        print("3. 查看 ONNX_DEPLOYMENT_GUIDE.md 了解部署方法")
    else:
        print("\n❌ ONNX转换存在问题，请检查错误信息")

if __name__ == "__main__":
    main()
