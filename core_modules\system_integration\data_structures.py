"""
智能干扰系统数据结构定义
定义系统各模块间传递的数据格式
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import time

# 噪声带宽选择表格
NOISE_BANDWIDTH_TABLE = {
    0: "输出直流",
    1: 5,      # kHz
    2: 10,     # kHz
    3: 20,     # kHz
    4: 50,     # kHz
    5: 100,    # kHz
    6: 300,    # kHz
    7: 1000,   # kHz (1 MHz)
    8: 10000,  # kHz (10 MHz)
    9: 20000,  # kHz (20 MHz)
    10: 50000, # kHz (50 MHz)
    11: 100000, # kHz (100 MHz)
    12: 200000, # kHz (200 MHz)
    13: 300000, # kHz (300 MHz)
    14: 400000, # kHz (400 MHz)
    15: 500000, # kHz (500 MHz)
    16: 600000, # kHz (600 MHz)
    17: 700000, # kHz (700 MHz)
    18: 800000, # kHz (800 MHz)
    19: 900000, # kHz (900 MHz)
    20: 1000000 # kHz (1000 MHz)
}

@dataclass
class CombSpectrumParams:
    """梳状谱参数"""
    spectrum_count: int = 1  # 梳状谱个数 1-8
    frequency_offset_1: float = 0.0  # 频偏1 (kHz)
    flicker_period_1: float = 1.0  # 闪烁周期1 (us)
    flicker_hold_time_1: float = 0.5  # 闪烁保持时间1 (us)
    # 可扩展到8个梳状谱
    frequency_offsets: List[float] = field(default_factory=list)
    flicker_periods: List[float] = field(default_factory=list)
    flicker_hold_times: List[float] = field(default_factory=list)

@dataclass
class IntermittentSamplingParams:
    """间歇采样转发参数"""
    repeat_interval: float = 100.0  # 重复转发时间间隔 (us)
    sampling_switch: int = 1  # 间歇采样开关 0:off 1:on
    sampling_period: float = 10.0  # 间歇采样周期 (us)
    sampling_width: float = 2.0  # 间歇采样宽度 (us)
    coverage_range: float = 1000.0  # 干扰覆盖距离范围 (m)
    pulse_sampling_length: float = 1.0  # 脉冲采样长度 (us)

@dataclass
class BroadbandNoiseParams:
    """宽带阻塞噪声参数"""
    bandwidth_selection: int = 10  # 噪声带宽选择 (见噪声带宽选择表格)

@dataclass
class SmartNoiseParams:
    """灵巧噪声参数"""
    bandwidth_selection: int = 5  # 噪声带宽选择
    noise_source: int = 1  # 噪声源选择 1:高斯噪声 2:多普勒闪烁 3:多普勒噪声
    doppler_flicker_mode: int = 1  # 多普勒闪烁模式 1:固定闪烁 2:随机闪烁
    flicker_hold_time: float = 10.0  # 闪烁保持时间 (us)
    flicker_disappear_time: float = 5.0  # 闪烁消失时间 (us)
    doppler_noise_bandwidth: float = 100.0  # 多普勒噪声带宽 (kHz)
    doppler_noise_jump_period: float = 50.0  # 多普勒噪声跳变周期 (kHz)

@dataclass
class DeceptionParams:
    """拖引参数"""
    velocity_drag_speed: float = 100.0  # 速拖速度 (m/s)
    velocity_drag_acceleration: float = 10.0  # 速拖加速度 (m/s²)
    range_drag_speed: float = 50.0  # 距拖速度 (m/s)
    range_drag_acceleration: float = 5.0  # 距拖加速度 (m/s²)
    capture_time: float = 1.0  # 捕获时间 (s)
    drag_time: float = 3.0  # 拖引时间 (s)
    hold_time: float = 2.0  # 保持时间 (s)
    disappear_time: float = 1.0  # 消失时间 (s)


@dataclass
class RadarEmissionData:
    """雷达发射参数数据"""
    # 基本发射参数
    peak_power: float           # 峰值功率 (W)
    frequency: float           # 载频 (Hz)
    pulse_width: float         # 脉宽 (s)
    prf: float                # 脉冲重复频率 (Hz)
    antenna_gain: float       # 天线增益 (dB)

    # 工作状态
    operating_mode: str       # 工作模式
    beam_direction: float     # 波束指向 (度)
    scan_pattern: str         # 扫描模式

    # 目标信息
    target_range: float       # 目标距离 (km)
    target_velocity: float    # 目标速度 (m/s)
    target_rcs: float         # 目标RCS (m²)

    # 时间戳
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class SimplifiedRadarData:
    """简化的雷达数据结构 - 专用于智能决策模块（使用标准单位格式）"""
    # 基本发射参数（标准单位）
    frequency: float           # 载频 (MHz)
    pulse_width: float         # 脉宽 (μs)
    prf: float                # 脉冲重复周期 (μs)

    # 工作状态
    operating_mode: int       # 工作模式 (0-静默, 1-搜索, 2-跟踪, 3-成像, 4-制导)

    # 目标信息
    target_range: float       # 目标距离 (km)
    target_velocity: float    # 目标速度 (m/s)

    # 时间戳
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class ThreatAssessmentData:
    """威胁评估数据"""
    # 威胁等级
    threat_value: float           # 威胁度值 [0,1]
    threat_level: int            # 威胁等级 1-5
    threat_level_name: str       # 威胁等级名称
    
    # 各项威胁度分量
    radar_type_threat: float     # 雷达类型威胁度
    platform_type_threat: float # 平台类型威胁度
    distance_threat: float       # 距离威胁度
    frequency_threat: float      # 频率威胁度
    mode_threat: float          # 工作模式威胁度
    
    # 评估置信度
    confidence: float           # 评估置信度 [0,1]
    
    # 时间戳
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class JammingDecisionData:
    """干扰决策数据"""
    # 决策结果
    should_jam: bool            # 是否进行干扰
    jamming_type: str          # 干扰类型
    jamming_power: float       # 干扰功率 [0,1]
    jamming_frequency: float   # 干扰频率 (Hz)

    # 具体干扰参数 - 根据干扰类型选择对应参数
    comb_spectrum_params: Optional[CombSpectrumParams] = None
    intermittent_sampling_params: Optional[IntermittentSamplingParams] = None
    broadband_noise_params: Optional[BroadbandNoiseParams] = None
    smart_noise_params: Optional[SmartNoiseParams] = None
    deception_params: Optional[DeceptionParams] = None

    # 决策参数
    jamming_params: Dict = field(default_factory=dict)  # 兼容性保留
    priority: int = 3              # 干扰优先级 1-5
    duration: float = 5.0          # 预期干扰持续时间 (s)

    # 决策依据
    decision_reason: str = ""       # 决策原因
    confidence: float = 0.5         # 决策置信度 [0,1]

    # 时间戳
    timestamp: float = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

    def get_active_params(self) -> Optional[Any]:
        """获取当前激活的干扰参数"""
        if "梳状谱" in self.jamming_type and self.comb_spectrum_params:
            return self.comb_spectrum_params
        elif "间歇采样" in self.jamming_type and self.intermittent_sampling_params:
            return self.intermittent_sampling_params
        elif "宽带" in self.jamming_type and self.broadband_noise_params:
            return self.broadband_noise_params
        elif "灵巧" in self.jamming_type and self.smart_noise_params:
            return self.smart_noise_params
        elif "拖引" in self.jamming_type and self.deception_params:
            return self.deception_params
        return None


@dataclass
class JammingExecutionData:
    """干扰执行数据"""
    # 执行状态
    execution_success: bool     # 执行是否成功
    actual_jamming_type: str   # 实际执行的干扰类型
    actual_jamming_power: float # 实际干扰功率
    actual_frequency: float    # 实际干扰频率
    
    # 执行参数
    start_time: float          # 开始时间
    duration: float           # 实际持续时间
    equipment_status: str     # 设备状态
    
    # 干扰信号特征
    signal_bandwidth: float   # 信号带宽
    modulation_type: str     # 调制类型
    signal_quality: float    # 信号质量指标
    
    # 时间戳
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class EffectivenessData:
    """干扰效果评估数据"""
    # 效果指标
    overall_effectiveness: float    # 总体有效性 [0,1]
    threat_reduction: float        # 威胁降低程度 [0,1]
    detection_degradation: float   # 检测性能退化 [0,1]
    tracking_degradation: float    # 跟踪性能退化 [0,1]
    
    # 具体影响
    snr_degradation: float         # 信噪比退化 (dB)
    range_error: float            # 距离误差 (km)
    velocity_error: float         # 速度误差 (m/s)
    false_targets: int            # 虚假目标数量
    
    # 雷达响应
    radar_countermeasures: bool    # 雷达是否启动反制
    mode_change: bool             # 是否改变工作模式
    power_adjustment: bool        # 是否调整功率
    
    # 评估质量
    evaluation_confidence: float   # 评估置信度 [0,1]
    
    # 时间戳
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class FeedbackData:
    """反馈数据"""
    # 性能反馈
    decision_quality: float        # 决策质量评分 [0,1]
    execution_quality: float      # 执行质量评分 [0,1]
    system_performance: float     # 系统整体性能 [0,1]
    
    # 学习反馈
    learning_reward: float         # 强化学习奖励
    experience_value: float       # 经验价值
    model_update_needed: bool     # 是否需要模型更新
    
    # 系统调整建议
    power_adjustment: float       # 功率调整建议 [-1,1]
    frequency_adjustment: float   # 频率调整建议 [-1,1]
    type_change_suggestion: str   # 干扰类型变更建议
    
    # 时间戳
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class SystemCycleData:
    """完整系统周期数据"""
    cycle_id: int                      # 周期ID
    start_time: float                  # 开始时间
    end_time: float                    # 结束时间
    
    # 各阶段数据
    radar_emission: RadarEmissionData
    threat_assessment: ThreatAssessmentData
    jamming_decision: JammingDecisionData
    jamming_execution: JammingExecutionData
    effectiveness: EffectivenessData
    feedback: FeedbackData
    
    # 周期性能
    cycle_duration: float              # 周期耗时
    success: bool                      # 周期是否成功
    
    def __post_init__(self):
        if self.end_time is None:
            self.end_time = time.time()
        if self.cycle_duration is None:
            self.cycle_duration = self.end_time - self.start_time


class SystemMode(Enum):
    """系统工作模式"""
    PASSIVE_MONITORING = "passive_monitoring"    # 被动监控
    ACTIVE_JAMMING = "active_jamming"           # 主动干扰
    LEARNING_MODE = "learning_mode"             # 学习模式
    EVALUATION_MODE = "evaluation_mode"         # 评估模式
    EMERGENCY_MODE = "emergency_mode"           # 紧急模式


class JammingStrategy(Enum):
    """干扰策略 - 仅强化学习"""
    LEARNING_BASED = "learning_based" # 基于强化学习的策略


@dataclass
class SystemConfiguration:
    """系统配置"""
    # 工作模式
    system_mode: SystemMode = SystemMode.ACTIVE_JAMMING
    jamming_strategy: JammingStrategy = JammingStrategy.LEARNING_BASED
    
    # 时间参数
    cycle_interval: float = 0.1        # 周期间隔 (s)
    max_jamming_duration: float = 10.0 # 最大干扰持续时间 (s)
    
    # 阈值参数
    threat_threshold: float = 0.5      # 威胁阈值
    effectiveness_threshold: float = 0.3 # 有效性阈值
    
    # 学习参数
    learning_rate: float = 0.01        # 学习率
    exploration_rate: float = 0.1      # 探索率
    
    # 系统限制
    max_jamming_power: float = 1.0     # 最大干扰功率
    min_cycle_interval: float = 0.05   # 最小周期间隔
    
    # 安全参数
    emergency_stop_threshold: float = 0.9  # 紧急停止阈值
    max_consecutive_failures: int = 5      # 最大连续失败次数
