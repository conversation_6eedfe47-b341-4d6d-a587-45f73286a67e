# RK3588模型文件切换指南

## 概述

本指南介绍如何在RK3588雷达干扰推理系统中动态切换RKNN模型文件，支持运行时模型更换和管理。

## 目录结构

```
cpp_rknn_inference_utf8/
├── models/                     # 模型文件目录
│   ├── ppo_actor_rk3588.rknn  # 主模型文件
│   ├── ppo_actor_backup.rknn  # 备份模型文件
│   └── default.rknn           # 默认模型链接
├── scripts/
│   └── manage_models.py        # 模型管理脚本
├── include/
│   └── model_manager.h         # 模型管理器头文件
├── src/
│   └── model_manager.c         # 模型管理器实现
├── qt_main_c.cpp              # 主程序（支持模型加载）
└── model_switch_example.cpp   # 模型切换示例程序
```

## 使用方法

### 1. 命令行指定模型文件

```bash
# 编译主程序
qmake && make

# 使用指定模型文件运行
./rknn_jamming_inference ./models/ppo_actor_rk3588.rknn

# 使用默认模型文件运行
./rknn_jamming_inference
```

### 2. 交互式模型切换

```bash
# 编译模型切换示例程序
qmake CONFIG+=model_switch_example && make

# 运行交互式程序
./model_switch_example

# 或指定初始模型
./model_switch_example ./models/ppo_actor_rk3588.rknn
```

### 3. 使用模型管理脚本

```bash
# 进入脚本目录
cd scripts

# 列出所有模型文件
python manage_models.py list

# 复制新模型到模型目录
python manage_models.py copy /path/to/new_model.rknn --name new_model.rknn

# 验证模型文件
python manage_models.py validate ../models/ppo_actor_rk3588.rknn

# 设置默认模型
python manage_models.py default ppo_actor_rk3588.rknn

# 备份模型
python manage_models.py backup ppo_actor_rk3588.rknn

# 恢复模型
python manage_models.py restore ppo_actor_rk3588.rknn

# 删除模型
python manage_models.py remove old_model.rknn
```

## API接口

### ModelManager API

```c
// 创建模型管理器
ModelManager* manager = model_manager_create();

// 加载模型
int ret = model_manager_load_model(manager, "./models/new_model.rknn");

// 切换模型
ret = model_manager_switch_model(manager, "./models/another_model.rknn");

// 检查模型状态
if (model_manager_is_model_loaded(manager)) {
    printf("模型已加载: %s\n", model_manager_get_current_model_path(manager));
}

// 获取模型信息
ModelInfo info;
if (model_manager_get_model_info(manager, &info) == MODEL_SUCCESS) {
    printf("模型: %s, 版本: %s\n", info.model_name, info.version);
}

// 卸载模型
model_manager_unload_model(manager);

// 销毁管理器
model_manager_destroy(manager);
```

### 集成到现有代码

```c
// 在主程序中添加模型管理器
static ModelManager* g_model_manager = NULL;

// 初始化
g_model_manager = model_manager_create();

// 加载模型
if (load_rknn_model("./models/ppo_actor_rk3588.rknn")) {
    printf("AI推理模式\n");
} else {
    printf("传统算法模式\n");
}

// 在推理时检查模型状态
if (model_manager_is_model_loaded(g_model_manager)) {
    // 使用AI推理
    rknn_inference_predict(...);
} else {
    // 使用传统算法
    traditional_algorithm(...);
}
```

## 模型文件要求

### 文件格式
- **扩展名**: `.rknn`
- **最小大小**: 1KB以上
- **编码**: RKNN格式

### 输入输出规格
- **输入维度**: 12维浮点数向量
- **输出维度**: 10维（威胁等级5维 + 干扰类型5维）
- **数据类型**: float32

### 命名规范
```
ppo_actor_rk3588.rknn          # 主模型
ppo_actor_rk3588_v2.rknn       # 版本化模型
ppo_actor_backup.rknn          # 备份模型
ppo_actor_test.rknn            # 测试模型
```

## 运行时切换流程

### 1. 热切换（推荐）
```c
// 无需停止推理服务
int ret = model_manager_switch_model(manager, new_model_path);
if (ret == MODEL_SUCCESS) {
    printf("模型切换成功\n");
    // 新的推理请求将使用新模型
}
```

### 2. 冷切换
```c
// 先卸载当前模型
model_manager_unload_model(manager);

// 加载新模型
int ret = model_manager_load_model(manager, new_model_path);
```

## 错误处理

### 常见错误码
- `MODEL_SUCCESS (0)`: 操作成功
- `MODEL_ERROR_NOT_FOUND (-2)`: 模型文件不存在
- `MODEL_ERROR_LOAD_FAIL (-3)`: 模型加载失败
- `MODEL_ERROR_INCOMPATIBLE (-4)`: 模型不兼容

### 错误处理示例
```c
int ret = model_manager_load_model(manager, model_path);
switch (ret) {
    case MODEL_SUCCESS:
        printf("模型加载成功\n");
        break;
    case MODEL_ERROR_NOT_FOUND:
        printf("模型文件不存在: %s\n", model_path);
        break;
    case MODEL_ERROR_LOAD_FAIL:
        printf("模型加载失败，可能是格式错误\n");
        break;
    case MODEL_ERROR_INCOMPATIBLE:
        printf("模型不兼容，请检查输入输出维度\n");
        break;
    default:
        printf("未知错误: %d\n", ret);
        break;
}
```

## 性能优化

### 1. 预加载策略
```c
// 预加载多个模型到内存
model_manager_preload_models(manager, model_paths, count);

// 快速切换
model_manager_switch_to_preloaded(manager, model_index);
```

### 2. 异步加载
```c
// 后台异步加载模型
model_manager_load_async(manager, model_path, callback);

// 检查加载状态
if (model_manager_is_loading_complete(manager)) {
    model_manager_activate_loaded_model(manager);
}
```

### 3. 模型缓存
```c
// 启用模型缓存
model_manager_enable_cache(manager, max_cache_size);

// 缓存命中时快速切换
model_manager_switch_cached(manager, model_name);
```

## 监控和调试

### 1. 性能统计
```c
// 获取详细统计信息
const char* stats = model_manager_get_model_stats(manager);
printf("模型统计: %s\n", stats);

// 输出示例：
// 加载次数: 5, 切换次数: 3, 平均加载时间: 125.50 ms, 当前模型: ppo_actor_rk3588.rknn
```

### 2. 日志记录
```c
// 启用详细日志
model_manager_set_log_level(manager, LOG_LEVEL_DEBUG);

// 日志输出示例：
// [DEBUG] 开始加载模型: ./models/new_model.rknn
// [INFO] 模型验证通过: new_model.rknn (2.5 MB)
// [INFO] RKNN初始化成功
// [INFO] 模型加载完成，耗时: 156.78 ms
```

## 最佳实践

### 1. 模型版本管理
- 使用语义化版本号命名模型文件
- 保留多个版本以便回滚
- 定期备份重要模型

### 2. 安全考虑
- 验证模型文件完整性
- 限制模型文件访问权限
- 记录模型切换操作日志

### 3. 性能优化
- 预热新模型（运行几次推理）
- 监控内存使用情况
- 避免频繁切换模型

### 4. 错误恢复
- 保留备用模型文件
- 实现自动回滚机制
- 提供降级到传统算法的选项

## 故障排除

### 问题1: 模型加载失败
**症状**: `MODEL_ERROR_LOAD_FAIL`
**解决方案**:
1. 检查文件权限
2. 验证文件完整性
3. 确认RKNN版本兼容性

### 问题2: 推理结果异常
**症状**: 输出值不合理
**解决方案**:
1. 验证输入数据格式
2. 检查模型版本匹配
3. 对比不同模型的输出

### 问题3: 内存泄漏
**症状**: 长时间运行后内存增长
**解决方案**:
1. 确保正确调用销毁函数
2. 检查模型切换时的资源释放
3. 使用内存检测工具

通过以上指南，您可以灵活地在RK3588系统中管理和切换RKNN模型文件，实现动态的AI推理能力。
