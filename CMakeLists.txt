cmake_minimum_required(VERSION 3.16)

project(RK3588JammingInference VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core)

# 设置Qt自动处理
set(CMAKE_AUTOMOC ON)

# RK3588 RKNN库路径设置
set(RKNN_API_PATH "/usr/lib" CACHE PATH "Path to RKNN API library")
set(RKNN_INCLUDE_PATH "/usr/include" CACHE PATH "Path to RKNN API headers")

# 检查RKNN库是否存在
find_library(RKNN_API_LIB rknnrt PATHS ${RKNN_API_PATH} NO_DEFAULT_PATH)
if(NOT RKNN_API_LIB)
    message(FATAL_ERROR "RKNN API library not found in ${RKNN_API_PATH}")
endif()

# 检查RKNN头文件是否存在
find_path(RKNN_API_INCLUDE rknn_api.h PATHS ${RKNN_INCLUDE_PATH} NO_DEFAULT_PATH)
if(NOT RKNN_API_INCLUDE)
    message(FATAL_ERROR "RKNN API headers not found in ${RKNN_INCLUDE_PATH}")
endif()

message(STATUS "Found RKNN API library: ${RKNN_API_LIB}")
message(STATUS "Found RKNN API headers: ${RKNN_API_INCLUDE}")

# 创建推理库
add_library(rk3588_jamming_inference STATIC
    rk3588_jamming_inference.h
    rk3588_jamming_inference.cpp
)

# 设置库的包含目录
target_include_directories(rk3588_jamming_inference PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${RKNN_API_INCLUDE}
)

# 链接库
target_link_libraries(rk3588_jamming_inference PUBLIC
    Qt6::Core
    ${RKNN_API_LIB}
)

# 编译选项
target_compile_options(rk3588_jamming_inference PRIVATE
    -Wall
    -Wextra
    -O3
    -march=native
)

# 创建示例程序
add_executable(jamming_inference_example
    example_usage.cpp
)

target_link_libraries(jamming_inference_example PRIVATE
    rk3588_jamming_inference
    Qt6::Core
)

# 安装规则
install(TARGETS rk3588_jamming_inference
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES rk3588_jamming_inference.h
    DESTINATION include
)

install(TARGETS jamming_inference_example
    RUNTIME DESTINATION bin
)

# 创建pkg-config文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/rk3588_jamming_inference.pc.in"
    "${CMAKE_CURRENT_BINARY_DIR}/rk3588_jamming_inference.pc"
    @ONLY
)

install(FILES "${CMAKE_CURRENT_BINARY_DIR}/rk3588_jamming_inference.pc"
    DESTINATION lib/pkgconfig
)

# 打包配置
set(CPACK_PACKAGE_NAME "rk3588-jamming-inference")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "RK3588 Radar Jamming Decision Inference Library")
set(CPACK_PACKAGE_VENDOR "Jamming System")
set(CPACK_GENERATOR "DEB;TGZ")

# DEB包特定配置
set(CPACK_DEBIAN_PACKAGE_DEPENDS "libqt6core6, librknnrt")
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "<EMAIL>")
set(CPACK_DEBIAN_PACKAGE_DESCRIPTION "
 RK3588雷达干扰决策推理库
 .
 基于RKNN的高性能雷达干扰决策推理库，
 专为RK3588 NPU优化，支持实时推理。
")

include(CPack)

# 测试配置
enable_testing()

add_test(NAME basic_inference_test
    COMMAND jamming_inference_example
)

# 性能测试
add_custom_target(performance_test
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/jamming_inference_example
    DEPENDS jamming_inference_example
    COMMENT "Running performance test"
)

# 文档生成（如果有Doxygen）
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
    
    add_custom_target(doc_doxygen ALL
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# 显示配置信息
message(STATUS "")
message(STATUS "RK3588 Jamming Inference Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Qt6 version: ${Qt6_VERSION}")
message(STATUS "  RKNN API path: ${RKNN_API_PATH}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
