﻿#include <QCoreApplication>
#include <QTextCodec>
#include <cstdio>
#include <cstring>

// 包含所有核心模块
#include "include/threat_evaluator.h"
#include "include/rknn_inference.h"
#include "include/jamming_decision.h"

// 全局模块句柄
static ThreatEvaluator* g_threat_evaluator = NULL;
static RKNNInference* g_rknn_inference = NULL;
static JammingDecision* g_jamming_decision = NULL;

// 模拟雷达输入数据
void simulate_radar_input(RadarParameters* radar, int scenario) {
    const char* scenario_names[] = {
        "高威胁场景", "中等威胁场景", "高频场景", "低威胁场景", "跟踪场景"
    };

    switch (scenario) {
        case 0: // 高威胁场景
            radar->frequency_mhz = 8000.0;
            radar->pulse_width_us = 2.0;
            radar->prt_us = 2000.0;
            radar->power_w = 500000.0;
            radar->distance_km = 80.0;
            radar->speed_ms = 200.0;
            radar->direction_deg = 30.0;
            radar->work_mode = 1;
            break;

        case 1: // 中等威胁场景
            radar->frequency_mhz = 5000.0;
            radar->pulse_width_us = 5.0;
            radar->prt_us = 5000.0;
            radar->power_w = 200000.0;
            radar->distance_km = 120.0;
            radar->speed_ms = 100.0;
            radar->direction_deg = 15.0;
            radar->work_mode = 1;
            break;

        case 2: // 高频场景
            radar->frequency_mhz = 12000.0;
            radar->pulse_width_us = 0.5;
            radar->prt_us = 800.0;
            radar->power_w = 2000000.0;
            radar->distance_km = 30.0;
            radar->speed_ms = 300.0;
            radar->direction_deg = 45.0;
            radar->work_mode = 2;
            break;

        case 3: // 低威胁场景
            radar->frequency_mhz = 3000.0;
            radar->pulse_width_us = 10.0;
            radar->prt_us = 10000.0;
            radar->power_w = 50000.0;
            radar->distance_km = 200.0;
            radar->speed_ms = 50.0;
            radar->direction_deg = 90.0;
            radar->work_mode = 0;
            break;

        case 4: // 跟踪场景
            radar->frequency_mhz = 9000.0;
            radar->pulse_width_us = 1.0;
            radar->prt_us = 1500.0;
            radar->power_w = 800000.0;
            radar->distance_km = 60.0;
            radar->speed_ms = 150.0;
            radar->direction_deg = 60.0;
            radar->work_mode = 2;
            break;

        default:
            // 默认场景
            radar->frequency_mhz = 10000.0;
            radar->pulse_width_us = 1.0;
            radar->prt_us = 1000.0;
            radar->power_w = 1000000.0;
            radar->distance_km = 100.0;
            radar->speed_ms = 100.0;
            radar->direction_deg = 0.0;
            radar->work_mode = 1;
            break;
    }

    printf("场景 %d: %s\n", scenario, scenario_names[scenario]);
    printf("  频率: %.1f MHz\n", radar->frequency_mhz);
    printf("  脉宽: %.1f μs\n", radar->pulse_width_us);
    printf("  PRT: %.1f μs\n", radar->prt_us);
    printf("  功率: %.0f W\n", radar->power_w);
    printf("  距离: %.1f km\n", radar->distance_km);
    printf("  速度: %.1f m/s\n", radar->speed_ms);
    printf("  方向: %.1f°\n", radar->direction_deg);
    printf("  工作模式: %d\n", radar->work_mode);
}

// 打印威胁评估结果
void print_threat_assessment(const ThreatAssessmentData* threat) {
    printf("\n=== 威胁评估结果 ===\n");
    printf("威胁等级: %d (%s)\n", threat->threat_level, 
           threat_get_level_description(threat->threat_level));
    printf("威胁值: %.3f\n", threat->threat_value);
    printf("置信度: %.3f\n", threat->confidence);
    printf("优先级: %.3f\n", threat->priority);
    printf("详细威胁分析:\n");
    printf("  频率威胁度: %.3f\n", threat->frequency_threat);
    printf("  功率威胁度: %.3f\n", threat->power_threat);
    printf("  距离威胁度: %.3f\n", threat->distance_threat);
    printf("  模式威胁度: %.3f\n", threat->mode_threat);
}

// 打印干扰决策结果
void print_jamming_decision(const JammingDecisionData* decision) {
    printf("\n=== 干扰决策结果 ===\n");
    printf("是否干扰: %s\n", decision->should_jam ? "是" : "否");
    printf("干扰类型: %d (%s)\n", decision->jamming_type,
           jamming_get_type_description(decision->jamming_type));
    printf("干扰功率: %.3f\n", decision->jamming_power);
    printf("干扰频率: %.1f MHz\n", decision->jamming_frequency_mhz);
    printf("决策置信度: %.3f\n", decision->decision_confidence);
    
    if (decision->should_jam) {
        printf("干扰参数:\n");
        printf("  带宽: %.1f MHz\n", decision->bandwidth_mhz);
        printf("  持续时间: %.1f ms\n", decision->duration_ms);
        printf("  延迟: %.1f ms\n", decision->delay_ms);
    }
}

// 初始化所有模块
int initialize_modules() {
    printf("正在初始化雷达干扰决策系统...\n");
    
    // 初始化威胁评估器
    g_threat_evaluator = threat_evaluator_create();
    if (!g_threat_evaluator) {
        printf("错误: 威胁评估器初始化失败\n");
        return 0;
    }
    
    // 初始化RKNN推理引擎
    g_rknn_inference = rknn_inference_create();
    if (!g_rknn_inference) {
        printf("错误: RKNN推理引擎初始化失败\n");
        return 0;
    }
    
    // 初始化干扰决策器
    g_jamming_decision = jamming_decision_create();
    if (!g_jamming_decision) {
        printf("错误: 干扰决策器初始化失败\n");
        return 0;
    }
    
    printf("所有模块初始化成功!\n");
    return 1;
}

// 清理所有模块
void cleanup_modules() {
    printf("正在清理模块...\n");
    
    if (g_jamming_decision) {
        jamming_decision_destroy(g_jamming_decision);
        g_jamming_decision = NULL;
    }
    
    if (g_rknn_inference) {
        rknn_inference_destroy(g_rknn_inference);
        g_rknn_inference = NULL;
    }
    
    if (g_threat_evaluator) {
        threat_evaluator_destroy(g_threat_evaluator);
        g_threat_evaluator = NULL;
    }
    
    printf("模块清理完成\n");
}

// 运行单个测试场景
void run_test_scenario(int scenario_id) {

    
    // 1. 生成雷达输入
    RadarParameters radar;
    simulate_radar_input(&radar, scenario_id);
    
    // 2. 威胁评估
    ThreatAssessmentData threat;
    if (threat_evaluator_assess(g_threat_evaluator, &radar, &threat) != 0) {
        printf("错误: 威胁评估失败\n");
        return;
    }
    print_threat_assessment(&threat);
    
    // 3. RKNN推理（如果模型已加载）
    RKNNInferenceData inference_result;
    if (rknn_inference_predict(g_rknn_inference, &radar, &threat, &inference_result) == 0) {
        printf("\n=== RKNN推理结果 ===\n");
        printf("推理成功，耗时: %.2f ms\n", inference_result.inference_time_ms);
        printf("威胁等级概率: [");
        for (int i = 0; i < 5; i++) {
            printf("%.3f", inference_result.threat_level_probs[i]);
            if (i < 4) printf(", ");
        }
        printf("]\n");
        printf("干扰类型概率: [");
        for (int i = 0; i < 5; i++) {
            printf("%.3f", inference_result.jamming_type_probs[i]);
            if (i < 4) printf(", ");
        }
        printf("]\n");
    } else {
        printf("注意: RKNN推理跳过（模型未加载或推理失败）\n");
    }
    
    // 4. 干扰决策
    JammingDecisionData decision;
    if (jamming_decision_make(g_jamming_decision, &radar, &threat, &decision) != 0) {
        printf("错误: 干扰决策失败\n");
        return;
    }
    print_jamming_decision(&decision);
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 设置UTF-8编码
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    
    
    // 初始化所有模块
    if (!initialize_modules()) {
        printf("系统初始化失败，退出\n");
        return -1;
    }
    
    // 运行所有测试场景
    for (int i = 0; i < 5; i++) {
        run_test_scenario(i);
    }
    
    // 性能统计
    printf("\n" "=" * 50 "\n");
    printf("系统性能统计\n");
    printf("=" * 50 "\n");
    
    // 获取各模块性能统计
    printf("威胁评估器统计: %s\n", threat_evaluator_get_stats(g_threat_evaluator));
    printf("RKNN推理引擎统计: %s\n", rknn_inference_get_stats(g_rknn_inference));
    printf("干扰决策器统计: %s\n", jamming_decision_get_stats(g_jamming_decision));
    
    // 清理资源
    cleanup_modules();
    
    printf("\n程序执行完成\n");
    return 0;
}
