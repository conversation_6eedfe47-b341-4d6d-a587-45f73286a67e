cmake_minimum_required(VERSION 3.16)

project(RadarJammingQt VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt5
find_package(Qt5 REQUIRED COMPONENTS Core)

# 查找ONNX Runtime
set(ONNXRUNTIME_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../third_party/onnxruntime")
set(ONNXRUNTIME_INCLUDE_DIRS "${ONNXRUNTIME_ROOT_PATH}/include")
set(ONNXRUNTIME_LIB_DIRS "${ONNXRUNTIME_ROOT_PATH}/lib")

# 检查ONNX Runtime是否存在
if(EXISTS "${ONNXRUNTIME_LIB_DIRS}/onnxruntime.lib")
    set(HAS_ONNXRUNTIME ON)
    message(STATUS "Found ONNX Runtime, enabling real inference...")
else()
    set(HAS_ONNXRUNTIME OFF)
    message(STATUS "ONNX Runtime not found, using simulation mode...")
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${ONNXRUNTIME_INCLUDE_DIRS}
)

# 源文件
set(SOURCES
    main.cpp
    ../src/external_api.cpp
    ../src/data_structures.cpp
    ../src/jamming_decision.cpp
    ../src/onnx_inference.cpp
    ../src/threat_evaluator.cpp
)

# 头文件
set(HEADERS
    ../include/external_api.h
    ../include/data_structures.h
    ../include/jamming_decision.h
    ../include/onnx_inference.h
    ../include/threat_evaluator.h
)

# 创建可执行文件
add_executable(RadarJammingQt ${SOURCES} ${HEADERS})

# 链接Qt库
target_link_libraries(RadarJammingQt Qt5::Core)

# 链接ONNX Runtime（如果存在）
if(HAS_ONNXRUNTIME)
    target_compile_definitions(RadarJammingQt PRIVATE HAS_ONNXRUNTIME=1)
    target_link_directories(RadarJammingQt PRIVATE ${ONNXRUNTIME_LIB_DIRS})
    target_link_libraries(RadarJammingQt onnxruntime)
else()
    target_compile_definitions(RadarJammingQt PRIVATE HAS_ONNXRUNTIME=0)
endif()

# Windows特定配置
if(WIN32)
    # 设置控制台应用程序
    set_target_properties(RadarJammingQt PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
    
    # 复制ONNX Runtime DLL
    if(HAS_ONNXRUNTIME AND EXISTS "${ONNXRUNTIME_LIB_DIRS}/onnxruntime.dll")
        add_custom_command(TARGET RadarJammingQt POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${ONNXRUNTIME_LIB_DIRS}/onnxruntime.dll"
            $<TARGET_FILE_DIR:RadarJammingQt>
        )
    endif()
endif()

# 安装规则
install(TARGETS RadarJammingQt
    RUNTIME DESTINATION bin
)

# 如果存在ONNX Runtime DLL，也安装它
if(WIN32 AND HAS_ONNXRUNTIME AND EXISTS "${ONNXRUNTIME_LIB_DIRS}/onnxruntime.dll")
    install(FILES "${ONNXRUNTIME_LIB_DIRS}/onnxruntime.dll"
        DESTINATION bin
    )
endif()
