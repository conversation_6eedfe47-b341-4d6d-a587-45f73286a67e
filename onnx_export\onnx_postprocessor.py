#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型后处理器
将ONNX模型的多个输出转换为标准格式：[威胁等级, 干扰个数, 干扰类型1, 参数1..., 干扰类型2, 参数2...]
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any


class ONNXPostProcessor:
    """ONNX模型输出后处理器"""
    
    def __init__(self):
        """初始化后处理器"""
        # 干扰类型映射
        self.jamming_types = {
            0: "无干扰",
            1: "间歇采样",
            2: "宽带噪声", 
            3: "灵巧噪声",
            4: "拖引"
        }
        
        # 参数数量映射
        self.param_counts = {
            0: 0,   # 无干扰
            1: 6,   # 间歇采样转发参数
            2: 1,   # 宽带阻塞噪声参数
            3: 7,   # 灵巧噪声参数
            4: 3    # 拖引干扰参数
        }
    
    def process_outputs(self, onnx_outputs: List[np.ndarray]) -> Dict[str, Any]:
        """
        处理ONNX模型输出
        
        Args:
            onnx_outputs: ONNX模型的原始输出列表
            
        Returns:
            处理后的结果字典
        """
        # 解析输出（按照固定顺序）
        outputs = {
            'jamming_type_probs': onnx_outputs[0].flatten(),      # (5,)
            'comb_params': onnx_outputs[1].flatten(),             # (25,)
            'isrj_params': onnx_outputs[2].flatten(),             # (6,)
            'broadband_params': onnx_outputs[3].flatten(),        # (1,)
            'smart_noise_params': onnx_outputs[4].flatten(),      # (7,)
            'deception_params': onnx_outputs[5].flatten(),        # (3,)
            'threat_level_probs': onnx_outputs[6].flatten(),      # (5,)
            'jamming_count_probs': onnx_outputs[7].flatten(),     # (4,)
            'jamming1_probs': onnx_outputs[8].flatten(),          # (5,)
            'jamming2_probs': onnx_outputs[9].flatten(),          # (5,)
            'jamming3_probs': onnx_outputs[10].flatten()          # (5,)
        }
        
        # 生成标准格式输出
        standard_output = self._generate_standard_output(outputs)
        
        # 生成详细解释
        interpretation = self._generate_interpretation(outputs, standard_output)
        
        return {
            'raw_outputs': outputs,
            'standard_output': standard_output,
            'interpretation': interpretation
        }
    
    def _generate_standard_output(self, outputs: Dict[str, np.ndarray]) -> List[float]:
        """
        生成标准格式输出：[威胁等级, 干扰个数, 干扰类型1, 参数1..., 干扰类型2, 参数2...]
        
        Args:
            outputs: 解析后的输出字典
            
        Returns:
            标准格式输出列表
        """
        # 1. 威胁等级 (1-5)
        threat_level = np.argmax(outputs['threat_level_probs']) + 1
        
        # 2. 干扰个数 (0-3)
        jamming_count = np.argmax(outputs['jamming_count_probs'])
        
        # 3. 构建标准输出
        standard_output = [float(threat_level), float(jamming_count)]
        
        # 4. 根据干扰个数添加干扰类型和参数
        if jamming_count > 0:
            # 获取各个干扰类型的概率
            jamming_probs_list = [
                outputs['jamming1_probs'],
                outputs['jamming2_probs'],
                outputs['jamming3_probs']
            ]

            # 智能选择干扰类型（避免选择"无干扰"）
            selected_jammings = []
            for i in range(jamming_count):
                if i < len(jamming_probs_list):
                    probs = jamming_probs_list[i].copy()

                    # 如果已经有干扰了，降低"无干扰"的概率
                    if jamming_count > 0:
                        probs[0] = probs[0] * 0.1  # 大幅降低无干扰概率

                    # 选择概率最高的非零干扰类型
                    jamming_type = np.argmax(probs)

                    # 如果还是选中了"无干扰"，则选择主要干扰类型
                    if jamming_type == 0:
                        main_probs = outputs['jamming_type_probs'].copy()
                        main_probs[0] = 0  # 排除无干扰
                        jamming_type = np.argmax(main_probs)

                    selected_jammings.append(jamming_type)
                else:
                    # 如果超出范围，使用主要干扰类型（排除无干扰）
                    main_probs = outputs['jamming_type_probs'].copy()
                    main_probs[0] = 0  # 排除无干扰
                    jamming_type = np.argmax(main_probs)
                    selected_jammings.append(jamming_type)

            # 去重并限制干扰类型数量
            unique_jammings = []
            for jamming_type in selected_jammings:
                if jamming_type not in unique_jammings and jamming_type != 0:
                    unique_jammings.append(jamming_type)

            # 如果去重后数量不足，补充其他干扰类型
            if len(unique_jammings) < jamming_count:
                main_probs = outputs['jamming_type_probs'].copy()
                main_probs[0] = 0  # 排除无干扰
                for existing in unique_jammings:
                    main_probs[existing] = 0  # 排除已选择的

                while len(unique_jammings) < jamming_count and np.max(main_probs) > 0:
                    next_type = np.argmax(main_probs)
                    unique_jammings.append(next_type)
                    main_probs[next_type] = 0

            # 添加每个干扰的类型和参数
            for jamming_type in unique_jammings[:jamming_count]:
                standard_output.append(float(jamming_type))

                # 添加对应的参数
                params = self._get_jamming_params(jamming_type, outputs)
                standard_output.extend(params)
        
        return standard_output
    
    def _get_jamming_params(self, jamming_type: int, outputs: Dict[str, np.ndarray]) -> List[float]:
        """
        获取指定干扰类型的参数
        
        Args:
            jamming_type: 干扰类型 (0-4)
            outputs: 输出字典
            
        Returns:
            参数列表
        """
        if jamming_type == 0:  # 无干扰
            return []
        elif jamming_type == 1:  # 间歇采样转发
            params = outputs['isrj_params']
            # 确保参数在合理范围内并转换为实际物理参数
            clipped_params = np.clip(params, -1, 1)  # 限制在[-1,1]
            return [
                float((clipped_params[0] + 1) * 95 + 10),        # 重复转发时间间隔 (10-200 μs)
                float(int((clipped_params[1] + 1) * 0.5)),       # 间歇采样开关 (0/1)
                float((clipped_params[2] + 1) * 2.25 + 0.5),     # 间歇采样周期 (0.5-5.0 μs)
                float((clipped_params[3] + 1) * 0.95 + 0.1),     # 间歇采样宽度 (0.1-2.0 μs)
                float((clipped_params[4] + 1) * 45 + 10),        # 干扰覆盖距离 (10-100 km)
                float((clipped_params[5] + 1) * 1.25 + 0.5)      # 脉冲采样长度 (0.5-3.0 μs)
            ]
        elif jamming_type == 2:  # 宽带阻塞噪声
            params = outputs['broadband_params']
            # 确保参数在合理范围内
            bandwidth_param = np.clip(params[0], -1, 1)  # 限制在[-1,1]
            bandwidth_selection = int((bandwidth_param + 1) * 10)  # 转换为0-20
            return [
                float(bandwidth_selection)  # 噪声带宽选择 (0-20)
            ]
        elif jamming_type == 3:  # 灵巧噪声
            params = outputs['smart_noise_params']
            clipped_params = np.clip(params, -1, 1)  # 限制在[-1,1]
            return [
                float(clipped_params[0] * 100),              # 多普勒频移 (-100 to 100 Hz)
                float((clipped_params[1] + 1) * 25),         # 闪烁周期 (0-50 ms)
                float((clipped_params[2] + 1) * 5),          # 调制深度 (0-10)
                float((clipped_params[3] + 1) * 180),        # 相位调制 (0-360°)
                float((clipped_params[4] + 1) * 10),         # 幅度调制 (0-20 dB)
                float((clipped_params[5] + 1) * 500),        # 频率扫描速度 (0-1000 Hz/s)
                float((clipped_params[6] + 1) * 2.5)         # 噪声功率比 (0-5)
            ]
        elif jamming_type == 4:  # 拖引干扰
            params = outputs['deception_params']
            clipped_params = np.clip(params, -1, 1)  # 限制在[-1,1]
            return [
                float((clipped_params[0] + 1) * 500),        # 距离拖引 (0-1000 m)
                float((clipped_params[1] + 1) * 50),         # 速度拖引 (0-100 m/s)
                float((clipped_params[2] + 1) * 90)          # 角度拖引 (0-180°)
            ]
        else:
            return []
    
    def _generate_interpretation(self, outputs: Dict[str, np.ndarray], 
                               standard_output: List[float]) -> Dict[str, Any]:
        """
        生成结果解释
        
        Args:
            outputs: 原始输出
            standard_output: 标准格式输出
            
        Returns:
            解释字典
        """
        threat_level = int(standard_output[0])
        jamming_count = int(standard_output[1])
        
        interpretation = {
            'threat_assessment': {
                'level': threat_level,
                'description': self._get_threat_description(threat_level),
                'confidence': float(np.max(outputs['threat_level_probs']))
            },
            'jamming_strategy': {
                'count': jamming_count,
                'description': self._get_jamming_count_description(jamming_count),
                'confidence': float(np.max(outputs['jamming_count_probs']))
            },
            'recommended_actions': []
        }
        
        # 解析干扰动作
        if jamming_count > 0:
            param_index = 2  # 跳过威胁等级和干扰个数
            
            for i in range(jamming_count):
                if param_index < len(standard_output):
                    jamming_type = int(standard_output[param_index])
                    param_index += 1
                    
                    # 获取参数
                    param_count = self.param_counts.get(jamming_type, 0)
                    params = standard_output[param_index:param_index + param_count]
                    param_index += param_count
                    
                    action = {
                        'type': jamming_type,
                        'name': self.jamming_types.get(jamming_type, '未知'),
                        'parameters': params,
                        'description': self._get_jamming_description(jamming_type, params)
                    }
                    
                    interpretation['recommended_actions'].append(action)
        
        return interpretation
    
    def _get_threat_description(self, threat_level: int) -> str:
        """获取威胁等级描述（按照1-5威胁降低的标准定义）"""
        descriptions = {
            1: "极高威胁 - 全力干扰",    # 一级威胁（最高威胁）
            2: "高威胁 - 立即干扰",      # 二级威胁
            3: "中等威胁 - 准备干扰",    # 三级威胁
            4: "低威胁 - 监控即可",      # 四级威胁
            5: "极低威胁 - 可忽略"       # 五级威胁（最低威胁）
        }
        return descriptions.get(threat_level, "未知威胁等级")
    
    def _get_jamming_count_description(self, count: int) -> str:
        """获取干扰个数描述"""
        descriptions = {
            0: "不进行干扰",
            1: "单一干扰策略",
            2: "双重干扰策略", 
            3: "多重干扰策略"
        }
        return descriptions.get(count, "未知干扰策略")
    
    def _get_jamming_description(self, jamming_type: int, params: List[float]) -> str:
        """获取干扰类型描述"""
        if jamming_type == 0:
            return "无干扰"
        elif jamming_type == 1:
            return f"间歇采样转发干扰 - 间隔{params[0]:.1f}μs, 周期{params[2]:.1f}μs"
        elif jamming_type == 2:
            return f"宽带阻塞噪声 - 带宽选择{int(params[0])}"
        elif jamming_type == 3:
            return f"灵巧噪声 - 多普勒{params[0]:.1f}Hz, 调制深度{params[2]:.1f}"
        elif jamming_type == 4:
            return f"拖引干扰 - 距离{params[0]:.1f}m, 速度{params[1]:.1f}m/s"
        else:
            return "未知干扰类型"
    
    def format_standard_output(self, standard_output: List[float]) -> str:
        """
        格式化标准输出为可读字符串
        
        Args:
            standard_output: 标准格式输出
            
        Returns:
            格式化字符串
        """
        if len(standard_output) < 2:
            return "输出格式错误"
        
        threat_level = int(standard_output[0])
        jamming_count = int(standard_output[1])
        
        result = f"威胁等级: {threat_level}, 干扰个数: {jamming_count}"
        
        if jamming_count > 0:
            param_index = 2
            for i in range(jamming_count):
                if param_index < len(standard_output):
                    jamming_type = int(standard_output[param_index])
                    jamming_name = self.jamming_types.get(jamming_type, '未知')
                    param_count = self.param_counts.get(jamming_type, 0)
                    
                    result += f"\n  干扰{i+1}: {jamming_name}"
                    
                    if param_count > 0 and param_index + param_count < len(standard_output):
                        params = standard_output[param_index + 1:param_index + 1 + param_count]
                        result += f" (参数: {params})"
                    
                    param_index += 1 + param_count
        
        return result


def demo_postprocessor():
    """演示后处理器的使用"""
    print("ONNX后处理器演示")
    print("=" * 50)
    
    # 创建后处理器
    processor = ONNXPostProcessor()
    
    # 模拟ONNX模型输出
    mock_outputs = [
        np.array([[0.1, 0.2, 0.3, 0.3, 0.1]]),      # jamming_type_probs
        np.random.uniform(-0.5, 0.5, (1, 25)),       # comb_params
        np.random.uniform(-0.5, 0.5, (1, 6)),        # isrj_params
        np.array([[-0.3]]),                           # broadband_params
        np.random.uniform(-0.5, 0.5, (1, 7)),        # smart_noise_params
        np.random.uniform(-0.5, 0.5, (1, 3)),        # deception_params
        np.array([[0.1, 0.2, 0.3, 0.3, 0.1]]),      # threat_level_probs
        np.array([[0.2, 0.3, 0.4, 0.1]]),           # jamming_count_probs
        np.array([[0.1, 0.4, 0.2, 0.2, 0.1]]),      # jamming1_probs
        np.array([[0.2, 0.2, 0.3, 0.2, 0.1]]),      # jamming2_probs
        np.array([[0.3, 0.2, 0.2, 0.2, 0.1]])       # jamming3_probs
    ]
    
    # 处理输出
    result = processor.process_outputs(mock_outputs)
    
    print("标准格式输出:")
    print(result['standard_output'])
    
    print("\n格式化输出:")
    print(processor.format_standard_output(result['standard_output']))
    
    print("\n解释信息:")
    interpretation = result['interpretation']
    print(f"威胁评估: {interpretation['threat_assessment']['description']}")
    print(f"干扰策略: {interpretation['jamming_strategy']['description']}")
    
    if interpretation['recommended_actions']:
        print("推荐动作:")
        for i, action in enumerate(interpretation['recommended_actions']):
            print(f"  {i+1}. {action['description']}")


if __name__ == "__main__":
    demo_postprocessor()
