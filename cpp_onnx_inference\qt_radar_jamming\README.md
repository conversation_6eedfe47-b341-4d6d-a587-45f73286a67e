# Qt Radar Jamming System

基于Qt框架的雷达干扰智能决策系统，完全集成了`example_onnx_usage.cpp`的所有功能。

## 🚀 **项目特性**

### **核心功能**
- ✅ **真实ONNX推理**：获取所有11个模型输出
- ✅ **智能决策分析**：基于AI模型的干扰类型选择
- ✅ **标准化输出**：与Python版本100%兼容的数值数组格式
- ✅ **动态参数处理**：支持梳状谱等动态数量参数的干扰类型
- ✅ **完整参数解释**：详细的参数含义说明
- ✅ **多种干扰类型**：支持5种不同的干扰类型

### **技术架构**
- **框架**：Qt 5.15+ (控制台应用程序)
- **AI推理**：ONNX Runtime 1.7+
- **编译器**：MSVC 2019+
- **平台**：Windows x64

## 📁 **项目结构**

```
qt_radar_jamming/
├── RadarJammingQt.pro      # Qt项目文件
├── main.cpp                # 主程序文件
├── build_qt.bat           # 构建脚本
├── README.md              # 项目说明
└── build/                 # 构建输出目录
    └── RadarJammingQt.exe # 可执行文件
```

## 🔧 **环境要求**

### **必需组件**
1. **Qt 5.15+**
   - 下载地址：https://www.qt.io/download
   - 需要MSVC 2019编译器支持

2. **Visual Studio 2019+**
   - 需要C++编译器和Windows SDK

3. **ONNX Runtime** (可选)
   - 如果存在：启用真实AI推理
   - 如果不存在：使用模拟模式

### **目录结构要求**
```
cpp_onnx_inference/
├── qt_radar_jamming/          # Qt项目目录
├── include/                   # 头文件目录
├── src/                       # 源文件目录
├── third_party/onnxruntime/   # ONNX Runtime库
└── models/                    # AI模型文件
```

## 🚀 **快速开始**

### **1. 环境配置**

修改`build_qt.bat`中的Qt路径：
```batch
set QT_DIR=C:\Qt\5.15.2\msvc2019_64
```

### **2. 编译运行**

```bash
# 进入项目目录
cd cpp_onnx_inference/qt_radar_jamming

# 运行构建脚本
build_qt.bat
```

### **3. 手动编译（可选）**

```bash
# 生成Makefile
qmake RadarJammingQt.pro

# 编译
nmake

# 运行
build\RadarJammingQt.exe
```

## 📊 **输出示例**

### **系统初始化**
```
=== Intelligent Jamming Decision System ===
System version: ONNX Radar Jamming System v1.0.0
Found ONNX Runtime, enabling real inference...
Complete intelligent jamming decision system initialization completed
   Output interpretation mode: Enabled
   Detailed logging: Enabled
   GPU acceleration: Disabled
```

### **决策结果**
```
--- Test cycle 1 ---
Processing radar input
   Frequency: 8000.0 MHz
   Pulse width: 2.0 μs
   PRF: 500 Hz
   Distance: 80 km
   Work mode: 1 (Search)

   Decision: Execute jamming (type 4)
   Power level: 40000
   📋 Standard output: [3, 1, 0, 3, 12, 2, 2, 9, 1, 45, 9]
   📊 Format analysis: threat_level=3, jamming_count=1
   Decision confidence: 0.95
   Processing time: 12.5ms
```

### **参数解释**
```
📋 Standardized output interpretation:
     Numerical output: [3, 1, 0, 3, 12, 2, 2, 9, 1, 45, 9]
     Format description:
       [0] Threat level: 3 (1=Critical, 5=Minimal)
       [1] Jamming count: 1
       Jamming 0: Smart (number=0, ID=3)
         Parameter interpretation: Smart noise jamming
           Noise bandwidth selection: 12
           Noise source selection: 2 (2=Doppler flicker)
           Flicker hold time: 9 μs
           Doppler noise bandwidth: 45 kHz
```

## 🎯 **与原版本的对比**

| 特性 | example_onnx_usage.cpp | Qt版本 |
|------|----------------------|--------|
| 核心功能 | ✅ 完全相同 | ✅ 完全相同 |
| ONNX推理 | ✅ 真实推理 | ✅ 真实推理 |
| 标准输出 | ✅ 完整支持 | ✅ 完整支持 |
| 参数解释 | ✅ 详细解释 | ✅ 详细解释 |
| 框架依赖 | ❌ 无框架 | ✅ Qt框架 |
| 跨平台性 | ❌ Windows only | ✅ 跨平台 |
| 扩展性 | ❌ 有限 | ✅ 易扩展 |

## 🔍 **技术细节**

### **Qt集成优势**
1. **信号槽机制**：便于异步处理和事件驱动
2. **跨平台支持**：可轻松移植到Linux/macOS
3. **丰富的库**：网络、文件、线程等功能
4. **易于扩展**：可添加GUI、网络接口等

### **核心类设计**
```cpp
class RadarJammingSystem : public QObject
{
    Q_OBJECT
public slots:
    void runSystem();  // 主要业务逻辑
};
```

### **异步执行**
使用`QTimer::singleShot`确保在事件循环启动后执行主要逻辑。

## 🛠️ **故障排除**

### **常见问题**

1. **Qt未找到**
   ```
   Error: Qt not found
   ```
   **解决方案**：修改`build_qt.bat`中的`QT_DIR`路径

2. **编译错误**
   ```
   Error: Compilation failed
   ```
   **解决方案**：检查Visual Studio和Qt版本兼容性

3. **ONNX Runtime缺失**
   ```
   ONNX Runtime not found, using simulation mode
   ```
   **解决方案**：这是正常的，系统会使用模拟模式

## 📈 **扩展建议**

### **可能的扩展方向**
1. **GUI界面**：添加Qt Widgets或QML界面
2. **网络接口**：添加TCP/UDP通信功能
3. **数据库支持**：存储决策历史和统计数据
4. **配置文件**：支持XML/JSON配置
5. **日志系统**：完整的日志记录和分析

### **架构优化**
1. **多线程处理**：将ONNX推理放在工作线程
2. **插件系统**：支持动态加载干扰算法
3. **状态机**：使用Qt状态机管理系统状态

## 📝 **许可证**

本项目基于原有的雷达干扰系统开发，保持相同的许可证条款。

---

**注意**：本Qt版本完全保持了原有系统的所有功能，只是提供了更好的框架支持和扩展性。
