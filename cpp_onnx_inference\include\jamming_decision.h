#ifndef JAMMING_DECISION_H
#define JAMMING_DECISION_H

#include "data_structures.h"

#ifdef __cplusplus
extern "C" {
#endif

// 干扰决策函数
RadarErrorCode make_jamming_decision(const RadarParams* radar_params,
                                   const ThreatAssessment* threat_assessment,
                                   JammingDecision* decision);

// 选择干扰策略
int select_jamming_strategy(double threat_value, int threat_level);

// 计算干扰参数
void calculate_jamming_parameters(const RadarParams* radar_params,
                                int jamming_type,
                                double* params);

#ifdef __cplusplus
}
#endif

#endif // JAMMING_DECISION_H