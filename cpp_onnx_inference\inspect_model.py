#!/usr/bin/env python3
"""
检查ONNX模型的输入输出信息
"""
import onnx
import sys
import os

def inspect_onnx_model(model_path):
    """检查ONNX模型的详细信息"""
    try:
        # 加载模型
        model = onnx.load(model_path)
        
        print(f"=== ONNX Model Information ===")
        print(f"Model file: {model_path}")
        print(f"Model version: {model.model_version}")
        print(f"Producer: {model.producer_name}")
        print(f"ONNX version: {model.opset_import[0].version}")
        print()
        
        # 获取图信息
        graph = model.graph
        print(f"Graph name: {graph.name}")
        print()
        
        # 输入信息
        print("=== Input Information ===")
        for i, input_info in enumerate(graph.input):
            print(f"Input {i}:")
            print(f"  Name: {input_info.name}")
            print(f"  Type: {input_info.type}")
            
            # 获取形状信息
            if input_info.type.tensor_type.shape.dim:
                shape = []
                for dim in input_info.type.tensor_type.shape.dim:
                    if dim.dim_value:
                        shape.append(dim.dim_value)
                    elif dim.dim_param:
                        shape.append(dim.dim_param)
                    else:
                        shape.append("?")
                print(f"  Shape: {shape}")
            print()
        
        # 输出信息
        print("=== Output Information ===")
        for i, output_info in enumerate(graph.output):
            print(f"Output {i}:")
            print(f"  Name: {output_info.name}")
            print(f"  Type: {output_info.type}")
            
            # 获取形状信息
            if output_info.type.tensor_type.shape.dim:
                shape = []
                for dim in output_info.type.tensor_type.shape.dim:
                    if dim.dim_value:
                        shape.append(dim.dim_value)
                    elif dim.dim_param:
                        shape.append(dim.dim_param)
                    else:
                        shape.append("?")
                print(f"  Shape: {shape}")
            print()
        
        # 节点信息
        print("=== Node Information ===")
        print(f"Total nodes: {len(graph.node)}")
        
        # 显示前几个和最后几个节点
        for i, node in enumerate(graph.node[:3]):
            print(f"Node {i}: {node.op_type}")
        if len(graph.node) > 6:
            print("...")
        for i, node in enumerate(graph.node[-3:]):
            print(f"Node {len(graph.node)-3+i}: {node.op_type}")
        
        return True
        
    except Exception as e:
        print(f"Error loading model: {e}")
        return False

if __name__ == "__main__":
    model_path = "models/jamming_decision.onnx"
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        sys.exit(1)
    
    success = inspect_onnx_model(model_path)
    if not success:
        sys.exit(1)
