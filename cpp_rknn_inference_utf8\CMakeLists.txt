cmake_minimum_required(VERSION 3.16)

project(RKNNJammingInference VERSION 1.0.0 LANGUAGES C CXX)

# 设置C/C++标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core)

# 设置Qt自动处理
set(CMAKE_AUTOMOC ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/utf-8)  # MSVC UTF-8支持
else()
    add_compile_options(-fexec-charset=UTF-8)  # GCC UTF-8支持
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# 源文件
set(SOURCES
    src/threat_evaluator.c
    src/rknn_inference.c
    src/jamming_decision.c
    qt_main_c.cpp
)

# 头文件
set(HEADERS
    include/threat_evaluator.h
    include/rknn_inference.h
    include/jamming_decision.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接Qt库
target_link_libraries(${PROJECT_NAME} Qt6::Core)

# 编译选项
target_compile_options(${PROJECT_NAME} PRIVATE
    $<$<COMPILE_LANGUAGE:C>:-Wall -Wextra>
    $<$<COMPILE_LANGUAGE:CXX>:-Wall -Wextra>
)

# 如果是Debug模式，添加调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG=1)
    target_compile_options(${PROJECT_NAME} PRIVATE -g)
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

install(FILES ${HEADERS}
    DESTINATION include
)

# 创建测试目标
enable_testing()

add_test(NAME basic_test
    COMMAND ${PROJECT_NAME}
)

# 自定义目标
add_custom_target(run
    COMMAND ${PROJECT_NAME}
    DEPENDS ${PROJECT_NAME}
    COMMENT "运行RKNN雷达干扰推理程序"
)

# 清理目标
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}
    COMMENT "清理所有构建文件"
)

# 显示配置信息
message(STATUS "")
message(STATUS "RKNN雷达干扰推理系统配置:")
message(STATUS "  版本: ${PROJECT_VERSION}")
message(STATUS "  构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C标准: ${CMAKE_C_STANDARD}")
message(STATUS "  C++标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Qt版本: ${Qt6_VERSION}")
message(STATUS "  安装前缀: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
