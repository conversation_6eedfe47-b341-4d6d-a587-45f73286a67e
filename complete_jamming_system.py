"""
智能干扰决策系统
"""

import sys
import os
import argparse
import json
import time
import numpy as np
import warnings
# import matplotlib.pyplot as plt  # 暂时注释掉以避免NumPy兼容性问题
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

# 进度条支持
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    print("警告: tqdm未安装，将使用简单进度显示")

# 解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 抑制CuPy实验性功能警告
warnings.filterwarnings('ignore', category=FutureWarning, module='cupyx.jit._interface')

# GPU加速支持
try:
    import cupy as cp
    import cupyx.scipy.signal as cp_signal
    GPU_AVAILABLE = True
    CUPY_AVAILABLE = True
    print("检测到GPU支持，将使用CuPy加速计算")
except ImportError:
    cp = np
    cp_signal = None
    GPU_AVAILABLE = False
    CUPY_AVAILABLE = False
    cp = None
    cp_signal = None
    print("未检测到GPU支持，使用CPU计算")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    TORCH_AVAILABLE = True
    if torch.cuda.is_available():
        print(f"检测到PyTorch GPU支持，可用GPU数量: {torch.cuda.device_count()}")
    else:
        print("PyTorch可用但未检测到GPU")
except ImportError:
    torch = None
    TORCH_AVAILABLE = False
    print("未安装PyTorch")

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_modules.system_integration.intelligent_decision_module import IntelligentDecisionModule
from core_modules.jamming_decision_modules.simplified_threat_evaluator import SimplifiedThreatEvaluator
from core_modules.jamming_equipment_modules.jamming_signal_generator import JammingSignalGenerator
from core_modules.jamming_effectiveness_modules.jamming_effectiveness_evaluator import JammingEffectivenessEvaluator
from core_modules.radar_modules.radar_adaptive_response import RadarAdaptiveResponse
from core_modules.jamming_equipment_modules.frequency_tracking_system import FrequencyTrackingSystem
from core_modules.system_integration.data_structures import *

# GPU加速模块
try:
    from core_modules.jamming_decision_modules.gpu_threat_evaluator import GPUThreatEvaluator
    from core_modules.system_integration.gpu_rl_accelerator import ActorCriticAccelerator
    from core_modules.jamming_equipment_modules.gpu_signal_processor import GPUSignalProcessor
    from jamming_decision_core import JammingDecisionCore
    GPU_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"GPU模块导入失败: {e}")
    GPUThreatEvaluator = None
    ActorCriticAccelerator = None
    GPUSignalProcessor = None
    JammingDecisionCore = None
    GPU_MODULES_AVAILABLE = False


class SystemConfig:
    """PPO强化学习系统配置类"""
    def __init__(self,
                 enable_gpu: bool = True,
                 enable_explanation: bool = True,
                 enable_progress_bar: bool = True,
                 auto_save_interval: int = 1000,
                 gpu_device: int = 0):
        self.enable_gpu = enable_gpu
        self.enable_explanation = enable_explanation
        self.enable_progress_bar = enable_progress_bar
        self.auto_save_interval = auto_save_interval
        self.gpu_device = gpu_device



def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='智能干扰决策系统')

    # 基本操作模式 (default=True/False控制)
    parser.add_argument('--enable-train', default=False, type=lambda x: x.lower() == 'true',
                       help='启用训练模式 (default=true)')
    parser.add_argument('--enable-test', default=True, type=lambda x: x.lower() == 'true',
                       help='启用测试模式 (default=false)')

    # PPO训练参数
    parser.add_argument('--episodes', type=int, default=100000, help='训练轮次（PPO推荐更多轮次）')
    parser.add_argument('--actor-lr', type=float, default=0.0003, help='Actor网络学习率')
    parser.add_argument('--critic-lr', type=float, default=0.001, help='Critic网络学习率')
    parser.add_argument('--gamma', type=float, default=0.99, help='折扣因子')

    # PPO专用参数
    parser.add_argument('--ppo-epochs', type=int, default=4, help='PPO更新轮数')
    parser.add_argument('--ppo-clip', type=float, default=0.2, help='PPO裁剪参数')
    parser.add_argument('--entropy-coef', type=float, default=0.01, help='熵系数（探索鼓励）')
    parser.add_argument('--value-loss-coef', type=float, default=0.5, help='价值损失系数')
    parser.add_argument('--max-grad-norm', type=float, default=0.5, help='梯度裁剪阈值')

    # 模型参数
    parser.add_argument('--model-path', type=str, default='models/jamming_model.pkl', help='模型路径')
    parser.add_argument('--load-model', type=str, help='加载指定模型')

    # 输出控制
    parser.add_argument('--no-explanation', action='store_true', help='关闭输出解释')
    parser.add_argument('--no-detailed-log', action='store_true', help='关闭详细日志')
    parser.add_argument('--no-progress', action='store_true', help='关闭进度条')

    # GPU加速参数
    parser.add_argument('--enable-gpu', default=True, type=lambda x: x.lower() == 'true',
                       help='启用GPU加速 (default=false)')
    parser.add_argument('--gpu-device', type=int, default=0, help='GPU设备ID (default=0)')
    parser.add_argument('--batch-size', type=int, default=64, help='GPU批处理大小 (default=32)')
    parser.add_argument('--use-mixed-precision', default=False, type=lambda x: x.lower() == 'true',
                       help='使用混合精度训练 (default=false)')

    # 测试参数
    parser.add_argument('--test-count', type=int, default=10, help='测试场景数量')

    # 算法参数
    parser.add_argument('--algorithm', type=str, choices=['ppo'], default='ppo', help='强化学习算法选择')
    parser.add_argument('--state-dim', type=int, default=12, help='状态空间维度')
    parser.add_argument('--action-dim', type=int, default=71, help='动作空间维度')

    args = parser.parse_args()

    # 创建系统配置
    config = SystemConfig(
        training_episodes=args.episodes,

        actor_lr=args.actor_lr,
        critic_lr=args.critic_lr,

        gamma=args.gamma,

        model_save_path=args.model_path,
        enable_explanation=not args.no_explanation,
        enable_detailed_log=not args.no_detailed_log,
        enable_progress_bar=not args.no_progress,
        enable_gpu=args.enable_gpu,
        gpu_device=args.gpu_device,
        batch_size=args.batch_size,
        use_mixed_precision=args.use_mixed_precision,
        algorithm=args.algorithm,
        state_dim=args.state_dim,
        action_dim=args.action_dim,
        ppo_epochs=args.ppo_epochs,
        ppo_clip=args.ppo_clip,
        entropy_coef=args.entropy_coef,
        value_loss_coef=args.value_loss_coef
    )

    # 初始化系统
    system = CompleteJammingSystem(config)

    try:
        if args.enable_train:
            # 训练模式
            print(f"启动训练模式")
            training_stats = system.train_model()

            # 保存训练统计
            stats_path = args.model_path.replace('.pkl', '_stats.json')
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(training_stats, f, indent=2, ensure_ascii=False)
            print(f"训练统计已保存: {stats_path}")

        if args.enable_test:
            # 测试模式 - 增强版本，支持多种测试场景
            print(f"启动测试模式")

            # 加载模型
            model_path = args.load_model or args.model_path
            if not system.load_model(model_path):
                print(f"无法加载模型，退出测试")
                return

            # 执行多种测试场景
            test_summary = system.test_model_enhanced(num_tests=args.test_count)

            # 保存测试结果
            test_path = model_path.replace('.pkl', '_test_results.json')
            with open(test_path, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, indent=2, ensure_ascii=False)
            print(f"测试结果已保存: {test_path}")

        # 如果没有启用任何模式，显示帮助
        if not any([args.enable_train, args.enable_test]):
            print("未启用任何模式，显示帮助信息:")
            parser.print_help()

    except KeyboardInterrupt:
        print(f"\n用户中断操作")
    except Exception as e:
        print(f" 系统运行异常: {e}")
        import traceback
        traceback.print_exc()


@dataclass
class SystemConfig:
    """PPO强化学习系统配置"""
    # 核心训练参数
    training_episodes: int = 2000        # 训练轮次（PPO推荐更多轮次）
    actor_lr: float = 0.0003            # Actor网络学习率（适中，稳定收敛）
    critic_lr: float = 0.001             # Critic学习率（略高于Actor）
    gamma: float = 0.99                  # 折扣因子（标准值）

    # PPO专用参数（核心算法参数）
    ppo_epochs: int = 4                  # PPO更新轮数（平衡效率与稳定性）
    ppo_clip: float = 0.2               # PPO裁剪参数（防止过大更新）
    entropy_coef: float = 0.01           # 熵系数（鼓励探索）
    value_loss_coef: float = 0.5         # 价值损失系数
    max_grad_norm: float = 0.5           # 梯度裁剪（防止梯度爆炸）

    # 输出控制
    enable_explanation: bool = True      # 启用输出解释
    enable_detailed_log: bool = True     # 启用详细日志
    enable_progress_bar: bool = True     # 启用进度条

    # GPU加速参数
    enable_gpu: bool = True              # 启用GPU加速（推荐）
    gpu_device: int = 0                  # GPU设备ID
    batch_size: int = 64                 # 批处理大小（PPO推荐较大批次）
    use_mixed_precision: bool = False    # 混合精度训练

    # 算法选择
    algorithm: str = 'ppo'               # 强化学习算法
    state_dim: int = 12                  # 状态空间维度
    action_dim: int = 71                 # 动作空间维度 (兼容5维 + 标准格式24维 + 参数42维)

    # 批处理和内存参数
    memory_size: int = 10000             # 经验回放池大小
    min_memory_size: int = 1000          # 开始训练的最小经验数

    # 模型参数
    model_save_path: str = "models/jamming_model.pkl"
    auto_save_interval: int = 100000         # 自动保存间隔

    # 系统参数
    max_jamming_types: int = 2           # 最大干扰类型数量
    output_format: str = "numerical"     # 输出格式: numerical/detailed

    # 神经网络参数（现在使用PPO算法）


class CompleteJammingSystem:
    """完整PPO智能干扰决策系统"""

    def __init__(self, config: SystemConfig):
        """初始化完整系统"""
        self.config = config
        self.explanation_mode = config.enable_explanation

        # GPU配置初始化
        self._initialize_gpu_config()

        # 初始化各个模块
        self._initialize_modules()

        # 干扰类型映射
        self.jamming_type_mapping = {
            0: "梳状谱",
            1: "间歇采样转发",
            2: "宽带阻塞噪声",
            3: "灵巧噪声",
            4: "拖引"
        }

        # 系统统计
        self.training_stats = {
            'episodes_completed': 0,
            'total_rewards': 0.0,
            'average_reward': 0.0,
            'best_reward': 0.0,
            'memory_size': 0  # PPO经验池大小
        }

        # 训练曲线记录
        self.training_curves = {
            'episode_rewards': [],
            'moving_average_rewards': [],
            'cumulative_rewards': [],
            'memory_sizes': [],  # PPO经验池大小变化
            'exploration_rates': [],  # 保留用于兼容性（PPO中始终为0）
            'threat_level_distributions': [],
            'decision_accuracy': []
        }

        print(f"完整智能干扰决策系统初始化完成")
        print(f"   输出解释模式: {'开启' if self.explanation_mode else '关闭'}")
        print(f"   详细日志: {'开启' if config.enable_detailed_log else '关闭'}")
        print(f"   GPU加速: {'开启' if self.gpu_enabled else '关闭'}")
        if self.gpu_enabled:
            print(f"   GPU设备: {self.gpu_device}")
            print(f"   批处理大小: {config.batch_size}")

    def _initialize_gpu_config(self):
        """初始化GPU配置"""
        self.gpu_enabled = self.config.enable_gpu and GPU_AVAILABLE
        self.gpu_device = self.config.gpu_device
        self.batch_size = self.config.batch_size
        self.use_mixed_precision = self.config.use_mixed_precision

        if self.gpu_enabled:
            try:
                # 设置GPU设备
                cp.cuda.Device(self.gpu_device).use()

                # 初始化GPU内存池
                mempool = cp.get_default_memory_pool()
                pinned_mempool = cp.get_default_pinned_memory_pool()

                # 预分配一些内存
                _ = cp.zeros((1000, 1000), dtype=cp.float32)

                print(f"GPU {self.gpu_device} 初始化成功")

                # 如果有PyTorch，也设置PyTorch的GPU
                if TORCH_AVAILABLE and torch.cuda.is_available():
                    torch.cuda.set_device(self.gpu_device)
                    if self.use_mixed_precision:
                        print("启用混合精度训练")

            except Exception as e:
                print(f"GPU初始化失败: {e}")
                self.gpu_enabled = False

        # 设置计算后端
        self.xp = cp if self.gpu_enabled else np

    def _initialize_modules(self):
        """初始化系统模块"""
        # 威胁评估模块 - GPU加速版本或标准版本
        if self.gpu_enabled and GPU_MODULES_AVAILABLE and GPUThreatEvaluator:
            self.threat_evaluator = GPUThreatEvaluator(
                enable_radar_platform=False,
                gpu_device=self.gpu_device
            )
            threat_evaluator_type = "GPUThreatEvaluator (GPU加速)"
        else:
            self.threat_evaluator = SimplifiedThreatEvaluator(enable_radar_platform=False)
            threat_evaluator_type = "SimplifiedThreatEvaluator (CPU)"

        # PPO决策模块配置
        decision_config = {
            'actor_lr': self.config.actor_lr,
            'critic_lr': self.config.critic_lr,
            'gamma': self.config.gamma,
            'enable_learning': True,
            'enable_gpu': self.gpu_enabled,
            'gpu_device': self.gpu_device,
            'batch_size': self.batch_size,
            'use_mixed_precision': self.use_mixed_precision,
            'state_dim': self.config.state_dim,
            'action_dim': self.config.action_dim,
            'ppo_epochs': self.config.ppo_epochs,
            'ppo_clip': self.config.ppo_clip,
            'entropy_coef': self.config.entropy_coef,
            'value_loss_coef': self.config.value_loss_coef,
            'max_grad_norm': self.config.max_grad_norm
        }

        # PPO算法初始化
        if self.gpu_enabled and GPU_MODULES_AVAILABLE and ActorCriticAccelerator:
            self.actor_critic_accelerator = ActorCriticAccelerator(decision_config)
            self.jamming_core = JammingDecisionCore() if JammingDecisionCore else None
            decision_module_type = "PPO + GPU加速"
            print("使用PPO + GPU加速")
        else:
            print("GPU或PPO模块不可用，无法初始化")
            raise RuntimeError("PPO需要GPU支持，请启用GPU或安装CUDA")

        # 干扰信号生成模块 - GPU加速版本或标准版本
        if self.gpu_enabled and GPU_MODULES_AVAILABLE and GPUSignalProcessor:
            self.gpu_signal_processor = GPUSignalProcessor(
                gpu_device=self.gpu_device,
                enable_gpu=True
            )
            signal_generator_type = "JammingSignalGenerator + GPU加速"
        else:
            self.gpu_signal_processor = None
            signal_generator_type = "JammingSignalGenerator (CPU)"

        self.signal_generator = JammingSignalGenerator()

        # 干扰效果评估模块
        self.effectiveness_evaluator = JammingEffectivenessEvaluator()

        # 雷达自适应响应模块
        self.radar_response = RadarAdaptiveResponse()

        # 频率同步跟踪系统 - 训练模式下不显示详细信息
        self.frequency_tracker = FrequencyTrackingSystem(verbose=False)

        if self.config.enable_detailed_log:
            print(f"系统模块初始化完成")
            print(f"   威胁评估器: {threat_evaluator_type}")
            print(f"   决策模块: {decision_module_type}")
            print(f"   信号生成器: {signal_generator_type}")
            print(f"   效果评估器: JammingEffectivenessEvaluator")
            print(f"   雷达响应器: RadarAdaptiveResponse")
            print(f"   频率跟踪器: FrequencyTrackingSystem")

    def train_model(self, episodes: Optional[int] = None) -> Dict:
        """训练强化学习模型（支持GPU批处理）"""
        if episodes is None:
            episodes = self.config.training_episodes

        print(f"\n开始训练强化学习模型")
        print(f"   训练轮次: {episodes}")
        print(f"   Actor学习率: {self.config.actor_lr}")
        print(f"   Critic学习率: {self.config.critic_lr}")
        if self.gpu_enabled:
            print(f"   GPU批处理: 开启 (批大小: {self.batch_size})")

        episode_rewards = []
        start_time = time.time()

        # PPO训练
        print("\n" + "=" * 80)
        print(">> 开始PPO + GPU加速智能训练")
        print("=" * 80)
        episode_rewards = self._train_with_ppo_gpu(episodes)

        # 训练完成
        training_time = time.time() - start_time
        final_stats = self._finalize_training(episodes, episode_rewards, training_time)

        # 保存最终模型
        self._save_model(self.config.model_save_path)

        return final_stats



    def _train_with_ppo_gpu(self, episodes: int) -> List[float]:
        """PPO + GPU加速训练方法"""
        episode_rewards = []

        if not hasattr(self, 'actor_critic_accelerator'):
            print("PPO加速器未初始化")
            return episode_rewards

        print(f">> PPO GPU训练配置:")
        print(f"   批处理大小: {self.batch_size}")
        print(f"   Actor学习率: {self.config.actor_lr}")
        print(f"   Critic学习率: {self.config.critic_lr}")
        print(f"   PPO轮数: {self.config.ppo_epochs}")
        print(f"   PPO裁剪: {self.config.ppo_clip}")
        print(f"   熵系数: {self.config.entropy_coef}")
        print("   " + "-" * 60)

        # 创建进度条
        pbar = self._create_progress_bar(episodes)

        for episode in range(episodes):
            # 生成随机雷达场景
            radar_scenario = self._generate_random_scenario()

            # 威胁评估
            threat_data = self._assess_threat(radar_scenario)

            # 转换为状态字典
            state_dict = self._scenario_to_state_dict_ac(radar_scenario, threat_data)

            # PPO决策
            action = self.actor_critic_accelerator.select_action(state_dict, training=True)

            # 计算奖励 - 使用优化的GPU奖励函数
            reward = self._calculate_gpu_reward(action, threat_data, radar_scenario)

            # 计算下一状态
            next_state_dict = self._calculate_next_state_ac(state_dict, action, threat_data, radar_scenario)

            # 存储经验
            self.actor_critic_accelerator.store_experience(
                state_dict, action, reward, next_state_dict, False
            )

            episode_rewards.append(reward)

            # PPO训练（当收集足够经验时）
            train_result = None
            if len(self.actor_critic_accelerator.memory) >= self.batch_size:
                train_result = self.actor_critic_accelerator.train_ppo()

            # 更新统计
            cycle_result = {
                'reward': reward,
                'threat_data': threat_data,
                'decision': action
            }
            self._update_training_stats(episode, cycle_result)

            # 更新进度条
            self._update_progress_bar(pbar, episode, reward, episode_rewards, train_result)

            # 备用进度显示（当没有tqdm时）
            if not TQDM_AVAILABLE and self.config.enable_progress_bar and (episode + 1) % 100 == 0:
                self._display_training_progress(episode + 1, episodes, episode_rewards)

            # 自动保存
            if hasattr(self.config, 'auto_save_interval') and self.config.auto_save_interval > 0:
                if (episode + 1) % self.config.auto_save_interval == 0:
                    model_path = self.config.model_save_path.replace('.pkl', '_ppo.pth')
                    self.actor_critic_accelerator.save_model(model_path)

        # 关闭进度条
        if pbar is not None:
            pbar.close()

        return episode_rewards
    def _create_progress_bar(self, total_episodes: int):
        """创建美化的训练进度条"""
        if TQDM_AVAILABLE and self.config.enable_progress_bar:
            return tqdm(
                total=total_episodes,
                desc="PPO智能训练",
                unit="轮",
                ncols=140,
                bar_format='{desc}: {percentage:3.0f}%|{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] {postfix}',
                colour='cyan',
                ascii=True,
                dynamic_ncols=True
            )
        return None

    def _update_progress_bar(self, pbar, episode: int, reward: float, episode_rewards: List[float], train_result: dict = None):
        """更新美化的进度条信息"""
        if pbar is None:
            return

        # 计算统计信息
        recent_rewards = episode_rewards[-100:] if len(episode_rewards) >= 100 else episode_rewards
        avg_reward = np.mean(recent_rewards) if recent_rewards else 0
        max_reward = max(episode_rewards) if episode_rewards else 0

        # 计算奖励趋势
        trend_symbol = "↗"
        if len(episode_rewards) >= 10:
            recent_10 = np.mean(episode_rewards[-10:])
            previous_10 = np.mean(episode_rewards[-20:-10]) if len(episode_rewards) >= 20 else recent_10
            if recent_10 > previous_10:
                trend_symbol = "↗"  # 上升
            elif recent_10 < previous_10:
                trend_symbol = "↘"  # 下降
            else:
                trend_symbol = "→"  # 平稳

        # 奖励等级评估
        reward_level = "LOW"
        if avg_reward > 0.8:
            reward_level = "HIGH"
        elif avg_reward > 0.5:
            reward_level = "MID"

        postfix_dict = {
            f'{reward_level}当前': f'{reward:.3f}',
            f'{trend_symbol}平均': f'{avg_reward:.3f}',
            '最佳': f'{max_reward:.3f}',
            '内存': f'{len(self.actor_critic_accelerator.memory) if hasattr(self, "actor_critic_accelerator") else 0}'
        }

        # 添加训练损失信息
        if train_result:
            postfix_dict.update({
                'A_Loss': f'{train_result.get("actor_loss", 0):.4f}',
                'C_Loss': f'{train_result.get("critic_loss", 0):.4f}'
            })

            # 添加熵信息（如果有）
            if 'entropy' in train_result:
                postfix_dict['熵'] = f'{train_result["entropy"]:.3f}'

        pbar.set_postfix(postfix_dict)
        pbar.update(1)





    def _batch_threat_assessment(self, scenarios: List[Dict]) -> List:
        """批量威胁评估"""
        # 转换为威胁评估器需要的格式 - 使用统一的7个基础参数
        radar_data_batch = []
        for scenario in scenarios:
            # 处理距离字段
            if 'distance' in scenario:
                distance = scenario['distance']  # km
            elif 'target_range' in scenario:
                distance = scenario['target_range'] / 1000  # 转换为km
            else:
                distance = 50.0  # 默认距离

            # 处理工作模式
            work_mode = scenario.get('work_mode', scenario.get('operating_mode', 1))
            if isinstance(work_mode, str):
                work_mode = self._convert_work_mode(work_mode)

            radar_dict = {
                'speed': scenario.get('speed', 0),                    # 速度 (m/s)
                'distance': distance,                                 # 距离 (km)
                'direction': scenario.get('direction', scenario.get('beam_direction', 0.0)),  # 方向 (度)
                'pulse_repetition_freq': scenario['prt'],            # 重复频率 (Hz)
                'frequency': scenario['frequency'],                   # 载频 (Hz)
                'pw': scenario['pulse_width'],                       # 脉宽 (s)
                'work_mode': work_mode                               # 工作模式 (0-4)
            }
            radar_data_batch.append(radar_dict)

        # 批量评估
        if hasattr(self.threat_evaluator, 'evaluate_threat_batch'):
            batch_results = self.threat_evaluator.evaluate_threat_batch(radar_data_batch)

            # 转换为ThreatAssessmentData对象
            threat_data_list = []
            for threat_value, threat_level, details in batch_results:
                threat_data = ThreatAssessmentData(
                    threat_value=threat_value,
                    threat_level=threat_level.value,
                    threat_level_name=self._get_threat_level_name(threat_level.value),
                    radar_type_threat=details['membership_values'].get('雷达类型威胁度', 0.5),
                    platform_type_threat=details['membership_values'].get('平台类型威胁度', 0.5),
                    distance_threat=details['membership_values'].get('距离威胁度', 0.5),
                    frequency_threat=details['membership_values'].get('载频威胁度', 0.5),
                    mode_threat=details['membership_values'].get('工作模式威胁度', 0.5),
                    confidence=details['confidence']
                )
                threat_data_list.append(threat_data)

            return threat_data_list
        else:
            # 回退到单个评估
            return [self._assess_threat(scenario) for scenario in scenarios]

    def _scenario_to_state_dict(self, scenario: Dict, threat_data) -> Dict:
        """将场景转换为状态字典 - 使用标准单位格式，兼容12维输入"""
        return {
            'frequency': scenario['frequency'],           # 载频 (MHz)
            'pw': scenario['pw'],                        # 脉宽 (μs)
            'prt': scenario['prt'],                      # 脉冲重复周期 (μs)
            'power': scenario.get('power', 1e6),         # 功率 (W)
            'distance': scenario['distance'],            # 距离 (km)
            'speed': scenario.get('speed', 0),           # 速度 (m/s)
            'direction': scenario['direction'],          # 航向角 (度)
            'work_mode': scenario['work_mode'],          # 工作模式 (0-4)
            'threat_level': threat_data.threat_level,    # 威胁等级
            'threat_value': threat_data.threat_value,    # 威胁值
            'threat_confidence': 0.8,                    # 威胁评估置信度
            'threat_urgency': min(1.0, threat_data.threat_level / 5.0)  # 威胁紧急程度
        }

    def _calculate_next_state(self, current_state: Dict, action_dict: Dict, threat_data, scenario: Dict) -> Dict:
        """计算干扰后的下一状态"""
        # 复制当前状态
        next_state = current_state.copy()

        if action_dict['should_jam']:
            # 模拟干扰后的威胁等级变化
            post_jamming_threat_level = self._simulate_post_jamming_threat_level(
                threat_data.threat_level, action_dict, scenario
            )

            # 更新威胁相关状态
            next_state['threat_level'] = post_jamming_threat_level

            # 模拟威胁值的变化（基于威胁等级变化）
            threat_reduction = post_jamming_threat_level - threat_data.threat_level
            threat_value_change = threat_reduction * 0.2  # 每级威胁变化对应0.2的威胁值变化
            next_state['threat_value'] = max(0.0, min(1.0, threat_data.threat_value + threat_value_change))

            # 模拟雷达可能的频率跳变（对抗干扰）
            if threat_reduction >= 2:  # 干扰效果显著时，雷达可能跳频
                freq_change_prob = 0.3  # 30%概率跳频
                if np.random.random() < freq_change_prob:
                    # 频率变化±5%
                    freq_change = np.random.uniform(-0.05, 0.05)
                    next_state['frequency'] = current_state['frequency'] * (1 + freq_change)

            # 模拟雷达可能的PRT调整
            if threat_reduction >= 1:
                prt_change_prob = 0.2  # 20%概率调整PRT
                if np.random.random() < prt_change_prob:
                    # PRT变化±10%
                    prt_change = np.random.uniform(-0.1, 0.1)
                    next_state['prt'] = current_state['prt'] * (1 + prt_change)

        return next_state

    def _scenario_to_state_dict_ac(self, scenario: Dict, threat_data) -> Dict:
        """将场景转换为Actor-Critic状态字典"""
        return {
            'frequency': scenario['frequency'],           # 载频 (MHz)
            'pw': scenario['pw'],                        # 脉宽 (μs)
            'prt': scenario['prt'],                      # 脉冲重复周期 (μs)
            'power': scenario.get('power', 1e6),         # 功率 (W)
            'distance': scenario['distance'],            # 距离 (km)
            'speed': scenario.get('speed', 0),           # 速度 (m/s)
            'direction': scenario['direction'],          # 航向角 (度)
            'work_mode': scenario['work_mode'],          # 工作模式 (0-4)
            'threat_level': threat_data.threat_level,    # 威胁等级
            'threat_value': threat_data.threat_value,    # 威胁值
            'threat_confidence': 0.8,                    # 威胁评估置信度
            'threat_urgency': min(1.0, threat_data.threat_level / 5.0)  # 威胁紧急程度
        }

    def _calculate_next_state_ac(self, current_state: Dict, action: Dict, threat_data, scenario: Dict) -> Dict:
        """计算Actor-Critic的下一状态"""
        next_state = current_state.copy()

        # 根据干扰决策更新状态
        threat_level = action.get('threat_level', threat_data.threat_level)
        jamming_type = action.get('jamming_type', 0)

        # 模拟干扰效果
        if jamming_type > 0:  # 有干扰
            # 威胁等级可能降低
            threat_reduction = np.random.uniform(0.5, 2.0)
            new_threat_level = max(1, threat_level - threat_reduction)
            next_state['threat_level'] = new_threat_level
            next_state['threat_value'] = new_threat_level / 5.0

            # 模拟雷达对抗响应
            if threat_reduction > 1.5:  # 干扰效果显著
                # 频率跳变
                if np.random.random() < 0.3:
                    freq_change = np.random.uniform(-0.05, 0.05)
                    next_state['frequency'] = current_state['frequency'] * (1 + freq_change)

                # PRT调整
                if np.random.random() < 0.2:
                    prt_change = np.random.uniform(-0.1, 0.1)
                    next_state['prt'] = current_state['prt'] * (1 + prt_change)

        return next_state

    def _calculate_ppo_reward(self, action: Dict, threat_data, scenario: Dict) -> float:
        """计算Actor-Critic奖励"""
        base_reward = 0.0

        # 威胁等级评估奖励
        predicted_threat = action.get('threat_level', 3)
        actual_threat = threat_data.threat_level
        threat_accuracy = 1.0 - abs(predicted_threat - actual_threat) / 5.0
        threat_reward = threat_accuracy * 0.3

        # 干扰类型适配奖励
        jamming_type = action.get('jamming_type', 0)
        work_mode = scenario.get('work_mode', 1)

        type_bonus = 0.0
        if work_mode == 4 and jamming_type in [1, 4]:  # 制导雷达适合间歇采样和拖引
            type_bonus = 0.4
        elif work_mode == 1 and jamming_type in [2, 3]:  # 搜索雷达适合噪声干扰
            type_bonus = 0.3
        elif work_mode == 2 and jamming_type in [0, 1]:  # 跟踪雷达适合梳状谱和间歇采样
            type_bonus = 0.35
        elif work_mode == 3 and jamming_type in [0, 3]:  # 成像雷达适合梳状谱和灵巧噪声
            type_bonus = 0.3

        # 模拟干扰效果
        effectiveness = self._simulate_jamming_effectiveness(scenario, action)
        effectiveness_reward = effectiveness * 0.5

        # 威胁等级匹配奖励
        if predicted_threat >= 4 and jamming_type in [1, 4]:  # 高威胁选择强干扰
            threat_match_bonus = 0.2
        elif predicted_threat <= 2 and jamming_type in [2, 3]:  # 低威胁选择温和干扰
            threat_match_bonus = 0.1
        else:
            threat_match_bonus = 0.0

        base_reward = threat_reward + type_bonus + effectiveness_reward + threat_match_bonus

        return np.clip(base_reward, -1.0, 1.0)

    def _simulate_jamming_effectiveness(self, scenario: Dict, action: Dict) -> float:
        """模拟干扰效果"""
        base_effectiveness = 0.5

        # 频率匹配影响
        radar_freq = scenario.get('frequency', 10000)
        freq_match = 1.0 - abs(radar_freq - 10000) / 20000

        # 干扰类型效果
        jamming_type = action.get('jamming_type', 0)
        work_mode = scenario.get('work_mode', 1)

        type_effectiveness = {0: 0.7, 1: 0.8, 2: 0.6, 3: 0.75, 4: 0.85}

        # 工作模式匹配加成
        mode_bonus = 1.0
        if work_mode == 4 and jamming_type in [1, 4]:
            mode_bonus = 1.3
        elif work_mode == 1 and jamming_type in [2, 3]:
            mode_bonus = 1.2
        elif work_mode == 2 and jamming_type in [0, 1]:
            mode_bonus = 1.25

        # 威胁等级影响
        threat_level = action.get('threat_level', 3)
        threat_factor = 1.2 - (threat_level - 1) * 0.1

        effectiveness = (base_effectiveness *
                        freq_match *
                        type_effectiveness.get(jamming_type, 0.5) *
                        mode_bonus *
                        threat_factor)

        # 添加随机性
        effectiveness += np.random.normal(0, 0.1)

        return np.clip(effectiveness, 0.0, 1.0)

    def _calculate_gpu_reward(self, action_dict: Dict, threat_data, scenario: Dict) -> float:
        """改进的奖励函数 - 使用连续化和奖励塑形"""
        original_threat_level = threat_data.threat_level

        # 处理不同格式的action_dict
        if 'should_jam' in action_dict:
            should_jam = action_dict['should_jam']
        else:
            # 从jamming_type推断should_jam（jamming_type=0表示不干扰）
            should_jam = action_dict.get('jamming_type', 0) > 0

        # 1. 基础决策奖励 (连续化)
        base_reward = self._calculate_base_decision_reward(original_threat_level, should_jam)

        # 2. 干扰效果奖励 (仅在选择干扰时计算)
        effectiveness_reward = 0.0
        if should_jam:
            effectiveness_reward = self._calculate_jamming_effectiveness_reward(
                action_dict, threat_data, scenario
            )

        # 3. 连续性奖励 (避免频繁切换)
        consistency_reward = self._calculate_consistency_reward(action_dict)

        # 4. 组合干扰奖励 (新增)
        combination_reward = self._calculate_combination_reward(action_dict, threat_data, scenario)

        # 5. 自适应奖励 (根据训练阶段调整)
        adaptive_multiplier = self._get_adaptive_reward_multiplier()

        # 根据威胁等级调整权重 - 解决威胁等级5不合理干扰问题
        if original_threat_level >= 4:  # 低威胁(4)和极低威胁(5)
            # 对于低威胁目标，大幅提高基础决策权重，降低干扰效果权重
            total_reward = (
                base_reward * 0.8 +             # 基础决策80%
                effectiveness_reward * 0.1 +    # 干扰效果10%
                consistency_reward * 0.05 +     # 连续性5%
                combination_reward * 0.05       # 组合奖励5%
            ) * adaptive_multiplier
        else:  # 高威胁目标(1-3)
            # 对于高威胁目标，增加组合奖励权重
            total_reward = (
                base_reward * 0.7 +             # 基础决策70%
                effectiveness_reward * 0.15 +   # 干扰效果15%
                consistency_reward * 0.05 +     # 连续性5%
                combination_reward * 0.1        # 组合奖励10%（高威胁更重要）
            ) * adaptive_multiplier

        # 添加威胁等级惩罚机制
        total_reward = self._add_threat_level_penalty(total_reward, original_threat_level, should_jam)

        # 限制奖励范围 - 扩大上限以允许更高奖励
        final_reward = np.clip(total_reward, 0.0, 2.0)  # 允许更高的奖励上限

        # 记录奖励统计
        self._record_reward_statistics(final_reward, original_threat_level, should_jam)

        return final_reward

    def _calculate_combination_reward(self, action_dict: Dict, threat_data, scenario: Dict) -> float:
        """计算组合干扰奖励"""
        combination_reward = 0.0

        # 检查是否有组合干扰
        if 'jamming_combinations' in action_dict and action_dict['jamming_combinations']:
            combinations = action_dict['jamming_combinations']
            threat_level = threat_data.threat_level

            # 基础组合奖励
            if len(combinations) >= 2:
                combination_reward += 0.2  # 基础组合奖励

                # 威胁等级匹配奖励
                if threat_level <= 2:  # 高威胁应该使用组合
                    combination_reward += 0.3
                elif threat_level == 3:  # 中威胁可以使用组合
                    combination_reward += 0.1

                # 协同效应奖励（基于组合类型）
                if len(combinations) == 2:
                    type1, type2 = combinations[0][0], combinations[1][0]
                    synergy_bonus = self._get_combination_synergy_bonus(type1, type2, scenario)
                    combination_reward += synergy_bonus

        return min(1.0, combination_reward)

    def _get_combination_synergy_bonus(self, type1: int, type2: int, scenario: Dict) -> float:
        """获取组合协同效应奖励"""
        work_mode = scenario.get('work_mode', 1)

        # 基于工作模式的协同效应奖励
        synergy_bonuses = {
            # 制导雷达 (work_mode=4)
            4: {
                (1, 3): 0.4,  # 间歇采样 + 灵巧噪声
                (1, 4): 0.3,  # 间歇采样 + 拖引
                (3, 4): 0.5,  # 灵巧噪声 + 拖引（最佳）
            },
            # 跟踪雷达 (work_mode=2)
            2: {
                (1, 3): 0.3,  # 间歇采样 + 灵巧噪声
                (1, 4): 0.4,  # 间歇采样 + 拖引
                (2, 4): 0.2,  # 宽带噪声 + 拖引
            },
            # 成像雷达 (work_mode=3)
            3: {
                (1, 3): 0.5,  # 间歇采样 + 灵巧噪声（最佳）
                (2, 3): 0.3,  # 宽带噪声 + 灵巧噪声
            },
            # 搜索雷达 (work_mode=1)
            1: {
                (2, 3): 0.3,  # 宽带噪声 + 灵巧噪声
                (2, 4): 0.2,  # 宽带噪声 + 拖引
            }
        }

        mode_bonuses = synergy_bonuses.get(work_mode, {})
        combination_key = tuple(sorted([type1, type2]))

        return mode_bonuses.get(combination_key, 0.1)  # 默认小奖励

    def _calculate_base_decision_reward(self, threat_level: int, should_jam: bool) -> float:
        """计算基础决策奖励 - 优化奖励范围和分布"""

        # 对极端威胁等级进行特殊处理，但提高整体奖励水平
        if threat_level == 5:  # 极低威胁特殊处理
            if should_jam:
                return 0.2   # 提高最低奖励，避免过度惩罚
            else:
                return 1.5   # 提高正确决策奖励
        elif threat_level == 1:  # 极高威胁特殊处理
            if should_jam:
                return 1.5   # 提高正确决策奖励
            else:
                return 0.2   # 提高最低奖励，避免过度惩罚

        # 其他威胁等级使用优化的连续化函数
        threat_intensity = (6 - threat_level) / 5.0  # 归一化到[0.2, 1.0]

        if should_jam:
            # 干扰决策：威胁越高奖励越大，提高基础奖励
            reward = 0.6 + 0.9 * threat_intensity  # [0.78, 1.5]
        else:
            # 不干扰决策：威胁越低奖励越大，提高基础奖励
            reward = 0.6 + 0.9 * (1.0 - threat_intensity)  # [0.78, 1.5]

        return reward

    def _calculate_jamming_effectiveness_reward(self, action_dict: Dict, threat_data, scenario: Dict) -> float:
        """计算干扰效果奖励"""
        jamming_power = action_dict.get('jamming_power', 0.5)
        jamming_type = action_dict.get('jamming_type', 0)
        threat_level = threat_data.threat_level

        # 1. 功率适配奖励
        optimal_power = self._get_optimal_jamming_power(threat_level)
        power_diff = abs(jamming_power - optimal_power)
        power_reward = max(0.0, 1.0 - power_diff * 2.0)  # 功率偏差惩罚

        # 2. 类型适配奖励
        type_effectiveness = self._get_jamming_type_effectiveness(jamming_type, scenario)

        # 3. 威胁-干扰匹配奖励
        threat_match_reward = self._calculate_threat_jamming_match(threat_level, jamming_type, jamming_power)

        # 综合效果奖励
        effectiveness_reward = (
            power_reward * 0.4 +
            type_effectiveness * 0.4 +
            threat_match_reward * 0.2
        )

        return effectiveness_reward

    def _calculate_consistency_reward(self, action_dict: Dict) -> float:
        """计算动作连续性奖励"""
        if not hasattr(self, '_last_action_dict'):
            self._last_action_dict = action_dict
            return 0.0

        last_action = self._last_action_dict
        current_action = action_dict

        consistency_reward = 0.0

        # 干扰决策一致性
        if last_action['should_jam'] == current_action['should_jam']:
            consistency_reward += 0.3

        # 如果都选择干扰，检查参数一致性
        if last_action['should_jam'] and current_action['should_jam']:
            # 干扰类型一致性
            if last_action['jamming_type'] == current_action['jamming_type']:
                consistency_reward += 0.2

            # 功率变化平滑性
            power_diff = abs(last_action['jamming_power'] - current_action['jamming_power'])
            if power_diff < 0.2:  # 功率变化小于20%
                consistency_reward += 0.1

        # 更新历史动作
        self._last_action_dict = action_dict.copy()

        return consistency_reward

    def _add_threat_level_penalty(self, reward: float, threat_level: int, should_jam: bool) -> float:
        """添加威胁等级惩罚机制 - 轻度惩罚不合理决策"""

        # 减少惩罚强度，避免训练不稳定
        if threat_level == 5 and should_jam:
            # 极低威胁却选择干扰，轻度惩罚
            penalty = -0.1
            reward += penalty
        elif threat_level == 1 and not should_jam:
            # 极高威胁却不干扰，轻度惩罚
            penalty = -0.1
            reward += penalty
        elif threat_level == 4 and should_jam:
            # 低威胁选择干扰，微小惩罚
            penalty = -0.05
            reward += penalty
        elif threat_level == 2 and not should_jam:
            # 高威胁不干扰，微小惩罚
            penalty = -0.05
            reward += penalty

        return reward

    def _get_optimal_jamming_power(self, threat_level: int) -> float:
        """获取针对特定威胁等级的最优干扰功率"""
        # 威胁等级越高(数值越小)，需要的干扰功率越大
        power_mapping = {
            1: 0.9,  # 极高威胁需要最大功率
            2: 0.7,  # 高威胁需要高功率
            3: 0.5,  # 中等威胁需要中等功率
            4: 0.3,  # 低威胁需要低功率
            5: 0.1   # 极低威胁需要最小功率
        }
        return power_mapping.get(threat_level, 0.5)

    def _get_jamming_type_effectiveness(self, jamming_type: int, scenario: Dict) -> float:
        """计算干扰类型对特定场景的有效性"""
        frequency = scenario.get('frequency', 10000)  # MHz
        work_mode = scenario.get('work_mode', 'search')

        # 基于频率的类型有效性
        freq_effectiveness = {
            0: 0.7,  # 梳状谱 - 通用有效性
            1: 0.8,  # 间歇采样转发 - 高有效性
            2: 0.6,  # 宽带阻塞噪声 - 中等有效性
            3: 0.9,  # 灵巧噪声 - 最高有效性
            4: 0.85  # 拖引 - 高有效性
        }

        base_effectiveness = freq_effectiveness.get(jamming_type, 0.5)

        # 频率适配调整
        if frequency > 8000:  # X波段
            if jamming_type in [1, 3, 4]:  # 间歇采样、灵巧噪声、拖引
                base_effectiveness *= 1.2
        elif frequency >= 4000:  # C波段
            if jamming_type in [0, 1, 2, 3]:  # 大部分类型有效
                base_effectiveness *= 1.1

        # 工作模式适配调整
        mode_multipliers = {
            'search': 1.0,
            'track': 1.1,
            'guidance': 1.2,
            'imaging': 1.05
        }

        mode_key = work_mode.lower() if isinstance(work_mode, str) else 'search'
        mode_multiplier = mode_multipliers.get(mode_key, 1.0)

        return min(1.0, base_effectiveness * mode_multiplier)

    def _calculate_threat_jamming_match(self, threat_level: int, jamming_type: int, jamming_power: float) -> float:
        """计算威胁等级与干扰参数的匹配度"""
        # 高威胁目标需要更强的干扰
        threat_intensity = (6 - threat_level) / 5.0

        # 干扰类型的强度评级
        type_intensity = {
            0: 0.6,  # 梳状谱 - 中等强度
            1: 0.8,  # 间歇采样转发 - 高强度
            2: 0.5,  # 宽带阻塞噪声 - 中低强度
            3: 0.9,  # 灵巧噪声 - 最高强度
            4: 0.85  # 拖引 - 高强度
        }.get(jamming_type, 0.5)

        # 计算匹配度：威胁强度与干扰强度的匹配
        intensity_match = 1.0 - abs(threat_intensity - type_intensity)

        # 功率匹配度：功率应该与威胁强度相匹配
        power_match = 1.0 - abs(threat_intensity - jamming_power)

        # 综合匹配度
        match_score = (intensity_match * 0.6 + power_match * 0.4)

        return max(0.0, match_score)

    def _get_adaptive_reward_multiplier(self) -> float:
        """获取自适应奖励乘数"""
        if not hasattr(self, 'training_stats') or self.training_stats['episodes_completed'] == 0:
            return 1.0

        episodes_completed = self.training_stats['episodes_completed']

        # 训练早期：正常奖励，PPO通过熵系数自动平衡探索
        if episodes_completed < 1000:
            return 1.0  # 不再降低早期奖励
        # 训练中期：正常奖励
        elif episodes_completed < 10000:
            return 1.0
        # 训练后期：略微提高奖励，鼓励精细调优
        else:
            return 1.1

    def _record_reward_statistics(self, reward: float, threat_level: int, should_jam: bool):
        """记录奖励统计信息"""
        if not hasattr(self, '_reward_stats'):
            self._reward_stats = {
                'total_rewards': 0.0,
                'reward_count': 0,
                'threat_level_rewards': {i: [] for i in range(1, 6)},
                'jam_rewards': [],
                'no_jam_rewards': []
            }

        stats = self._reward_stats
        stats['total_rewards'] += reward
        stats['reward_count'] += 1
        stats['threat_level_rewards'][threat_level].append(reward)

        if should_jam:
            stats['jam_rewards'].append(reward)
        else:
            stats['no_jam_rewards'].append(reward)

    def _apply_reward_smoothing(self, raw_reward: float, expected_threat_level: int) -> float:
        """应用奖励平滑机制（减少平滑，增强学习信号）"""
        # 降低基础奖励，增加学习动机
        base_rewards = {1: 0.5, 2: 0.45, 3: 0.4, 4: 0.35, 5: 0.3}
        base_reward = base_rewards.get(expected_threat_level, 0.4)

        # 进一步减少平滑因子，保留更多原始奖励信号
        smoothing_factor = 0.02  # 仅2%基础奖励，98%原始奖励
        smoothed = raw_reward * (1 - smoothing_factor) + base_reward * smoothing_factor

        # 减少奖励变化限制，允许更大的学习信号
        if hasattr(self, '_last_reward'):
            max_change = 0.8  # 最大变化80%，允许更大的学习信号
            change = smoothed - self._last_reward
            if abs(change) > max_change:
                smoothed = self._last_reward + np.sign(change) * max_change

        self._last_reward = smoothed
        return smoothed

    def _apply_training_stage_reward_adjustment(self, raw_reward: float, threat_reduction: int) -> float:
        """PPO奖励调整 - 简化版本"""
        # PPO使用熵正则化自动平衡探索和利用，不需要复杂的训练阶段调整
        return max(0.05, min(1.0, raw_reward))

    def _determine_training_stage(self) -> str:
        """PPO训练阶段判断 - 简化版本"""
        # PPO不需要复杂的训练阶段划分，统一返回学习阶段
        return 'learning'

    def _calculate_learning_progress(self) -> float:
        """计算学习进度 (0-1)"""
        if not hasattr(self, 'learning_curve_data') or len(self.learning_curve_data['rewards']) < 10:
            return 0.0

        rewards = self.learning_curve_data['rewards']

        # 计算早期和最近的平均奖励
        early_window = min(20, len(rewards) // 4)
        recent_window = min(20, len(rewards) // 4)

        early_avg = sum(rewards[:early_window]) / early_window
        recent_avg = sum(rewards[-recent_window:]) / recent_window

        # 计算改善程度
        if early_avg > 0:
            improvement_ratio = (recent_avg - early_avg) / early_avg
            # 将改善比例转换为0-1的进度
            progress = min(1.0, max(0.0, improvement_ratio * 2))  # 50%改善对应100%进度
        else:
            progress = 0.0

        return progress

    def _get_recent_performance(self) -> float:
        """获取最近的性能表现"""
        if not hasattr(self, 'learning_curve_data') or len(self.learning_curve_data['rewards']) < 5:
            return 0.3  # 默认较低性能

        # 取最近20%的奖励计算平均值
        rewards = self.learning_curve_data['rewards']
        recent_window = max(5, len(rewards) // 5)
        recent_rewards = rewards[-recent_window:]

        return sum(recent_rewards) / len(recent_rewards)

    def _record_reward_to_learning_curve(self, reward: float, threat_reduction: int):
        """记录奖励到学习曲线"""
        if not hasattr(self, 'learning_curve_data'):
            self.learning_curve_data = {
                'rewards': [],
                'threat_reductions': [],
                'episode_rewards': [],
                'moving_average_window': 50
            }

        # 记录当前奖励
        self.learning_curve_data['rewards'].append(reward)
        self.learning_curve_data['threat_reductions'].append(threat_reduction)

        # 计算移动平均奖励
        if len(self.learning_curve_data['rewards']) >= self.learning_curve_data['moving_average_window']:
            recent_rewards = self.learning_curve_data['rewards'][-self.learning_curve_data['moving_average_window']:]
            moving_avg = sum(recent_rewards) / len(recent_rewards)
            self.learning_curve_data['episode_rewards'].append(moving_avg)

    def get_learning_curve_analysis(self) -> Dict:
        """获取学习曲线分析"""
        if not hasattr(self, 'learning_curve_data'):
            return {'status': 'no_data'}

        data = self.learning_curve_data
        if len(data['rewards']) < 10:
            return {'status': 'insufficient_data', 'total_samples': len(data['rewards'])}

        # 计算统计指标
        recent_100 = data['rewards'][-100:] if len(data['rewards']) >= 100 else data['rewards']
        early_100 = data['rewards'][:100] if len(data['rewards']) >= 100 else data['rewards'][:len(data['rewards'])//2]

        early_avg = sum(early_100) / len(early_100) if early_100 else 0
        recent_avg = sum(recent_100) / len(recent_100) if recent_100 else 0
        improvement = recent_avg - early_avg

        # 计算奖励趋势
        if len(data['episode_rewards']) >= 10:
            first_half = data['episode_rewards'][:len(data['episode_rewards'])//2]
            second_half = data['episode_rewards'][len(data['episode_rewards'])//2:]
            trend = (sum(second_half)/len(second_half)) - (sum(first_half)/len(first_half))
        else:
            trend = 0

        return {
            'status': 'analyzed',
            'total_samples': len(data['rewards']),
            'early_average': early_avg,
            'recent_average': recent_avg,
            'improvement': improvement,
            'trend': trend,
            'max_reward': max(data['rewards']),
            'min_reward': min(data['rewards']),
            'reward_variance': self._calculate_variance(data['rewards']),
            'learning_progress': 'improving' if improvement > 0.05 else 'stable' if abs(improvement) <= 0.05 else 'declining'
        }

    def _calculate_variance(self, values: list) -> float:
        """计算方差"""
        if len(values) < 2:
            return 0
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance

    def _simulate_post_jamming_threat_level(self, original_level: int, action_dict: Dict, scenario: Dict) -> int:
        """模拟干扰后的威胁等级"""
        # 基于干扰参数估算威胁等级降低程度
        jamming_power = action_dict['jamming_power']
        jamming_type = action_dict['jamming_type']

        # 不同干扰类型的威胁降低能力（提高基础效果）
        type_effectiveness = {
            0: 1.5,  # 梳状谱 - 较好效果
            1: 2.0,  # 间歇采样转发 - 很好效果
            2: 1.2,  # 宽带阻塞噪声 - 中等效果
            3: 2.2,  # 灵巧噪声 - 优秀效果
            4: 2.5   # 拖引 - 卓越效果
        }

        base_effectiveness = type_effectiveness.get(jamming_type, 1.0)

        # 功率影响因子
        power_factor = jamming_power  # 0.0-1.0

        # 场景适应性因子
        scenario_factor = self._calculate_scenario_effectiveness_factor(scenario, jamming_type)

        # 综合干扰效果
        total_effectiveness = base_effectiveness * power_factor * scenario_factor

        # 根据干扰效果计算威胁等级变化（提高难度，降低成功率）
        if total_effectiveness >= 3.5:
            level_change = 3  # 威胁降低3级（数值增加3）- 更难达到
        elif total_effectiveness >= 2.5:
            level_change = 2  # 威胁降低2级（数值增加2）- 提高阈值
        elif total_effectiveness >= 1.5:
            level_change = 1  # 威胁降低1级（数值增加1）- 提高阈值
        elif total_effectiveness >= 0.8:
            level_change = 0  # 威胁不变 - 提高阈值
        else:
            level_change = -1  # 威胁升高1级（数值减少1，干扰失效）

        # 计算干扰后威胁等级（威胁降低 = 数值增加）
        post_jamming_level = original_level + level_change

        # 确保威胁等级在合理范围内 (1-5)
        return max(1, min(5, post_jamming_level))

    def _calculate_threat_reduction_reward(self, threat_level_reduction: int) -> float:
        """根据威胁等级变化计算主要奖励（增强差异化版本）"""
        if threat_level_reduction >= 3:
            # 威胁等级降低3级或更多 - 卓越表现
            return 2.0
        elif threat_level_reduction == 2:
            # 威胁等级降低2级 - 优秀表现
            return 1.5
        elif threat_level_reduction == 1:
            # 威胁等级降低1级 - 良好表现
            return 1.0
        elif threat_level_reduction == 0:
            # 威胁等级不变 - 一般表现
            return 0.2
        elif threat_level_reduction == -1:
            # 威胁等级升高1级 - 较差表现
            return -0.2
        elif threat_level_reduction == -2:
            # 威胁等级升高2级 - 差表现
            return -0.5
        else:
            # 威胁等级升高3级或更多 - 很差表现
            return -1.0

    def _calculate_scenario_effectiveness_factor(self, scenario: Dict, jamming_type: int) -> float:
        """计算场景对干扰效果的影响因子 - 使用标准单位格式"""
        factor = 1.0

        # 雷达频率影响 - frequency 现在是MHz（大幅提高适应性）
        frequency = scenario.get('frequency', 10000.0)  # 默认10GHz = 10000MHz
        if frequency > 8000:  # X波段 (>8GHz)
            if jamming_type in [1, 3, 4]:  # 间歇采样、灵巧噪声、拖引对高频效果好
                factor *= 1.8
            else:
                factor *= 1.3  # 其他干扰类型也有一定效果
        elif frequency >= 4000:  # C波段 (4-8GHz) - 包含测试场景4000MHz
            if jamming_type in [0, 1, 2, 3]:  # 大部分干扰对C波段都有效
                factor *= 1.6
            else:
                factor *= 1.4
        else:  # L/S波段 (<4GHz)
            if jamming_type in [0, 2]:  # 梳状谱、宽带噪声对低频效果相对好
                factor *= 1.5
            else:
                factor *= 1.2

        # 雷达功率影响
        power = scenario.get('peak_power', 1e6)
        if power > 2e6:  # 高功率雷达
            if jamming_type in [3, 4]:  # 灵巧噪声和拖引对高功率雷达效果好
                factor *= 1.15

        # 工作模式影响（大幅提高适应性）
        mode = scenario.get('work_mode', 1)  # 使用标准字段名
        if isinstance(mode, int):
            # 数字模式：0=静默, 1=搜索, 2=跟踪, 3=成像, 4=制导
            if mode in [2, 4]:  # 跟踪、制导
                if jamming_type in [1, 3, 4]:  # 精确干扰对制导跟踪效果好
                    factor *= 1.4
                else:
                    factor *= 1.2
            elif mode in [1, 3]:  # 搜索、成像 - 包含测试场景
                if jamming_type in [0, 2]:  # 覆盖性干扰对搜索效果好
                    factor *= 1.3
                else:
                    factor *= 1.2  # 其他干扰也有效果
            else:  # 静默模式
                factor *= 1.1  # 静默模式相对容易干扰
        else:
            # 字符串模式（兼容性）
            if mode in ['制导', '跟踪']:
                if jamming_type in [1, 3, 4]:  # 精确干扰对制导跟踪效果好
                    factor *= 1.1
            elif mode in ['搜索', '预警']:
                if jamming_type in [0, 2]:  # 覆盖性干扰对搜索预警效果好
                    factor *= 1.05

        # PRT影响 - prt 现在是μs，需要计算PRF
        prt = scenario.get('prt', 1000.0)  # 默认1000μs = 1kHz
        prf_hz = 1e6 / prt  # μs -> Hz
        if prf_hz > 5000:  # 高PRF
            if jamming_type in [1, 4]:  # 间歇采样和拖引对高PRF效果好
                factor *= 1.1

        return factor

    def _calculate_auxiliary_reward_factors(self, action_dict: Dict, scenario: Dict, original_threat_level: int) -> float:
        """计算辅助奖励因子"""
        auxiliary_reward = 0.0

        # 1. 功率效率奖励 (30%)
        power_efficiency = self._calculate_power_efficiency_by_threat(
            action_dict['jamming_power'], original_threat_level
        )
        auxiliary_reward += power_efficiency * 0.3

        # 2. 干扰类型适配奖励 (40%)
        type_adaptation = self._calculate_type_adaptation_score(
            action_dict['jamming_type'], scenario
        )
        auxiliary_reward += type_adaptation * 0.4

        # 3. 距离适应性奖励 (20%)
        distance_adaptation = self._calculate_distance_adaptation_score(
            action_dict, scenario
        )
        auxiliary_reward += distance_adaptation * 0.2

        # 4. 决策一致性奖励 (10%)
        consistency_bonus = self._calculate_decision_consistency_bonus(
            action_dict, original_threat_level
        )
        auxiliary_reward += consistency_bonus * 0.1

        return auxiliary_reward

    def _calculate_power_efficiency_by_threat(self, jamming_power: float, threat_level: int) -> float:
        """根据威胁等级计算功率效率"""
        # 不同威胁等级的推荐功率范围
        recommended_power_ranges = {
            1: (0.8, 1.0),   # 极高威胁：需要高功率
            2: (0.6, 0.9),   # 高威胁：需要较高功率
            3: (0.4, 0.7),   # 中等威胁：中等功率
            4: (0.2, 0.5),   # 低威胁：低功率
            5: (0.1, 0.3)    # 极低威胁：很低功率
        }

        min_power, max_power = recommended_power_ranges.get(threat_level, (0.3, 0.7))

        if min_power <= jamming_power <= max_power:
            return 1.0  # 功率在推荐范围内
        elif jamming_power < min_power:
            return jamming_power / min_power  # 功率不足
        else:
            # 功率过高，有浪费
            excess = jamming_power - max_power
            return max(0.2, 1.0 - excess * 1.5)

    def _calculate_type_adaptation_score(self, jamming_type: int, scenario: Dict) -> float:
        """计算干扰类型适配得分 - 使用标准单位格式"""
        # 基础得分
        base_scores = {0: 0.6, 1: 0.8, 2: 0.5, 3: 0.9, 4: 0.95}
        base_score = base_scores.get(jamming_type, 0.6)

        # 根据场景调整 - 使用标准单位格式
        frequency = scenario.get('frequency', 10000.0)  # 默认10GHz = 10000MHz
        mode = scenario.get('work_mode', 1)  # 使用标准字段名

        adaptation_bonus = 0.0

        # 频率适配 - frequency 现在是MHz
        if frequency > 8000 and jamming_type in [1, 3, 4]:  # >8GHz
            adaptation_bonus += 0.1
        elif frequency < 4000 and jamming_type in [0, 2]:  # <4GHz
            adaptation_bonus += 0.05

        # 模式适配 - 支持数字和字符串模式
        if isinstance(mode, int):
            # 数字模式：0=静默, 1=搜索, 2=跟踪, 3=成像, 4=制导
            if mode in [2, 4] and jamming_type in [1, 3, 4]:  # 跟踪、制导
                adaptation_bonus += 0.1
            elif mode in [1, 3] and jamming_type in [0, 2]:  # 搜索、成像
                adaptation_bonus += 0.05
        else:
            # 字符串模式（兼容性）
            if mode in ['制导', '跟踪'] and jamming_type in [1, 3, 4]:
                adaptation_bonus += 0.1
            elif mode in ['搜索', '预警'] and jamming_type in [0, 2]:
                adaptation_bonus += 0.05

        return min(1.0, base_score + adaptation_bonus)

    def _calculate_distance_adaptation_score(self, action_dict: Dict, scenario: Dict) -> float:
        """计算距离适应性得分"""
        target_range = scenario.get('target_range', 50000)
        jamming_power = action_dict['jamming_power']

        if target_range < 30000:  # 近距离
            # 近距离可以用中等功率
            if 0.4 <= jamming_power <= 0.8:
                return 1.0
            else:
                return 0.6
        elif target_range > 100000:  # 远距离
            # 远距离需要高功率
            if jamming_power >= 0.6:
                return 1.0
            else:
                return jamming_power / 0.6
        else:  # 中等距离
            # 中等距离功率适中即可
            if 0.3 <= jamming_power <= 0.7:
                return 1.0
            else:
                return 0.8

    def _calculate_decision_consistency_bonus(self, action_dict: Dict, threat_level: int) -> float:
        """计算决策一致性奖励"""
        jamming_power = action_dict['jamming_power']
        jamming_type = action_dict['jamming_type']

        # 检查决策是否与威胁等级一致
        consistency_score = 0.0

        # 威胁等级与干扰强度一致性
        if threat_level <= 2:  # 高威胁
            if jamming_power >= 0.6 and jamming_type in [1, 3, 4]:
                consistency_score += 0.5
        elif threat_level == 3:  # 中等威胁
            if 0.3 <= jamming_power <= 0.7:
                consistency_score += 0.5
        else:  # 低威胁
            if jamming_power <= 0.5:
                consistency_score += 0.5

        # 干扰类型与功率一致性
        if jamming_type in [3, 4] and jamming_power >= 0.5:  # 高级干扰配高功率
            consistency_score += 0.3
        elif jamming_type in [0, 2] and jamming_power <= 0.6:  # 基础干扰配中低功率
            consistency_score += 0.3

        return min(1.0, consistency_score)

    # 已删除：_update_traditional_q_table 方法（PPO不需要Q表）

    # 已删除：_sync_to_traditional_q_table 方法（PPO不需要Q表）

    def _display_batch_progress(self, completed: int, total: int, batch_rewards: List[float]):
        """显示批次训练进度"""
        progress = completed / total * 100
        avg_reward = sum(batch_rewards) / len(batch_rewards) if batch_rewards else 0

        print(f"批次进度: {progress:5.1f}% ({completed:4d}/{total}) | "
              f"批次平均奖励: {avg_reward:.3f}")

    def load_model(self, model_path: str) -> bool:
        """加载训练好的模型"""
        try:
            # 根据算法类型和文件扩展名加载不同的模型
            if self.config.algorithm == 'ppo' and hasattr(self, 'actor_critic_accelerator'):
                # 尝试加载PPO模型
                ppo_model_path = model_path
                if model_path.endswith('.pkl'):
                    ppo_model_path = model_path.replace('.pkl', '_ppo.pth')
                elif not model_path.endswith('.pth'):
                    ppo_model_path = model_path + '_ppo.pth'

                if os.path.exists(ppo_model_path):
                    success = self.actor_critic_accelerator.load_model(ppo_model_path)
                    if success:
                        # 验证模型权重是否正常
                        if self._validate_model_weights():
                            print(f"成功加载PPO模型: {ppo_model_path}")
                            self._model_loaded = True
                            return True
                        else:
                            print(f"PPO模型权重异常，将使用智能规则决策")
                            self._model_loaded = False
                            # 清除损坏的模型
                            self.actor_critic_accelerator = None
                            return False
                else:
                    print(f"PPO模型文件不存在: {ppo_model_path}")
                    self._model_loaded = False
                    return False

            print(f"加载模型失败: {model_path}")
            return False

        except Exception as e:
            print(f"加载模型异常: {e}")
            return False

    def _validate_model_weights(self) -> bool:
        """验证模型权重是否正常"""
        try:
            if not hasattr(self, 'actor_critic_accelerator'):
                return False

            # 检查Actor网络权重
            for name, param in self.actor_critic_accelerator.actor.named_parameters():
                if torch.isnan(param).any() or torch.isinf(param).any():
                    print(f"检测到异常权重: {name}")
                    return False

            return True
        except Exception as e:
            print(f"权重验证失败: {e}")
            return False

    def test_model(self, test_scenarios: Optional[List[Dict]] = None, num_tests: int = 10) -> Dict:
        """测试训练好的模型"""
        print(f"\n开始测试模型性能")

        if test_scenarios is None:
            test_scenarios = [self._generate_random_scenario() for _ in range(num_tests)]

        test_results = []

        for i, scenario in enumerate(test_scenarios):
            print(f"\n📡 测试场景 {i+1}/{len(test_scenarios)}")

            # 执行测试
            result = self._execute_test_cycle(scenario, i)
            test_results.append(result)

            # 显示结果
            if self.explanation_mode:
                self._explain_test_result(result, i+1)

        # 汇总测试结果
        summary = self._summarize_test_results(test_results)

        return summary

    def test_model_enhanced(self, num_tests: int = 10) -> Dict:
        """增强版测试模型 - 支持多种测试场景"""
        print(f"\n开始增强版模型测试")
        print(f"测试场景数量: {num_tests}")

        # 创建多种测试场景
        test_scenarios = []

        # 1. 基础随机场景
        basic_scenarios = [self._generate_random_scenario() for _ in range(num_tests // 3)]
        test_scenarios.extend(basic_scenarios)

        # 2. 极端威胁场景
        extreme_scenarios = self._create_extreme_threat_scenarios(num_tests // 3)
        test_scenarios.extend(extreme_scenarios)

        # 3. 边界条件场景
        boundary_scenarios = self._create_boundary_test_scenarios(num_tests - len(test_scenarios))
        test_scenarios.extend(boundary_scenarios)

        test_results = []
        scenario_types = ['基础随机'] * len(basic_scenarios) + ['极端威胁'] * len(extreme_scenarios) + ['边界条件'] * len(boundary_scenarios)

        for i, (scenario, scenario_type) in enumerate(zip(test_scenarios, scenario_types)):
            print(f"\n📡 测试场景 {i+1}/{len(test_scenarios)} ({scenario_type})")

            # 执行测试
            result = self._execute_test_cycle(scenario, i)
            result['scenario_type'] = scenario_type
            test_results.append(result)

            # 显示结果
            if self.explanation_mode:
                self._explain_test_result(result, i+1)

        # 汇总增强测试结果
        summary = self._summarize_enhanced_test_results(test_results)

        return summary

    def _create_extreme_threat_scenarios(self, num_scenarios: int) -> List[Dict]:
        """创建极端威胁测试场景"""
        scenarios = []

        for i in range(num_scenarios):
            if i % 3 == 0:
                # 极高威胁制导雷达
                scenario = {
                    'frequency': 15000.0,
                    'pw': 0.5,
                    'prt': 1e6/12000.0,
                    'distance': 10.0,
                    'speed': 500.0,
                    'direction': 0.0,
                    'work_mode': 4
                }
            elif i % 3 == 1:
                # 高威胁跟踪雷达
                scenario = {
                    'frequency': 12000.0,
                    'pw': 1.0,
                    'prt': 1e6/8000.0,
                    'distance': 20.0,
                    'speed': 300.0,
                    'direction': 45.0,
                    'work_mode': 2
                }
            else:
                # 极低威胁搜索雷达
                scenario = {
                    'frequency': 3000.0,
                    'pw': 10.0,
                    'prt': 1e6/500.0,
                    'distance': 100.0,
                    'speed': 50.0,
                    'direction': 90.0,
                    'work_mode': 1
                }

            scenarios.append(scenario)

        return scenarios

    def _create_boundary_test_scenarios(self, num_scenarios: int) -> List[Dict]:
        """创建边界条件测试场景"""
        scenarios = []

        for i in range(num_scenarios):
            if i % 4 == 0:
                # 最小频率边界
                scenario = {
                    'frequency': 1000.0,
                    'pw': 0.1,
                    'prt': 1e6/20000.0,
                    'distance': 5.0,
                    'speed': 1000.0,
                    'direction': 0.0,
                    'work_mode': 4
                }
            elif i % 4 == 1:
                # 最大频率边界
                scenario = {
                    'frequency': 20000.0,
                    'pw': 50.0,
                    'prt': 1e6/100.0,
                    'distance': 200.0,
                    'speed': 10.0,
                    'direction': 180.0,
                    'work_mode': 3
                }
            elif i % 4 == 2:
                # 最小距离边界
                scenario = {
                    'frequency': 10000.0,
                    'pw': 2.0,
                    'prt': 1e6/5000.0,
                    'distance': 1.0,
                    'speed': 800.0,
                    'direction': 270.0,
                    'work_mode': 4
                }
            else:
                # 最大距离边界
                scenario = {
                    'frequency': 8000.0,
                    'pw': 20.0,
                    'prt': 1e6/200.0,
                    'distance': 500.0,
                    'speed': 20.0,
                    'direction': 90.0,
                    'work_mode': 1
                }

            scenarios.append(scenario)

        return scenarios

    def _summarize_enhanced_test_results(self, test_results: List[Dict]) -> Dict:
        """汇总增强测试结果"""
        summary = self._summarize_test_results(test_results)

        # 按场景类型分组统计
        scenario_stats = {}
        for result in test_results:
            scenario_type = result.get('scenario_type', '未知')
            if scenario_type not in scenario_stats:
                scenario_stats[scenario_type] = {
                    'count': 0,
                    'total_reward': 0.0,
                    'correct_decisions': 0,
                    'processing_times': []
                }

            stats = scenario_stats[scenario_type]
            stats['count'] += 1
            stats['total_reward'] += result.get('reward', 0.0)
            if result.get('decision_correct', False):
                stats['correct_decisions'] += 1
            stats['processing_times'].append(result.get('processing_time', 0.0))

        # 计算各场景类型的平均性能
        for scenario_type, stats in scenario_stats.items():
            stats['avg_reward'] = stats['total_reward'] / stats['count']
            stats['avg_processing_time'] = sum(stats['processing_times']) / len(stats['processing_times'])

        summary['scenario_statistics'] = scenario_stats

        print(f"\n增强测试结果汇总:")
        print(f"总体平均处理时间: {summary['average_processing_time']*1000:.1f}ms")

        print(f"\n各场景类型性能:")
        for scenario_type, stats in scenario_stats.items():
            print(f"  {scenario_type}:")
            print(f"    平均奖励: {stats['avg_reward']:.3f}")
            print(f"    平均处理时间: {stats['avg_processing_time']*1000:.1f}ms")

        return summary

    def process_single_input(self, radar_input: Dict, explain: bool = None, training_mode: bool = False) -> Tuple[List, Dict]:
        """处理单个雷达输入，返回标准化输出"""
        if explain is None:
            explain = self.explanation_mode

        if explain:
            print(f"\n处理雷达输入")
            print(f"   频率: {radar_input['frequency']:.1f} MHz")
            print(f"   脉宽: {radar_input['pw']:.1f} μs")
            prf = 1e6 / radar_input['prt']  # 计算PRF
            print(f"   PRF: {prf:.0f} Hz")
            print(f"   PRT: {radar_input['prt']:.1f} μs")
            print(f"   距离: {radar_input['distance']:.0f} km")
            print(f"   速度: {radar_input['speed']:.0f} m/s")
            print(f"   方向: {radar_input['direction']:.1f}°")
            work_mode = radar_input.get('work_mode', 1)
            mode_names = {0: "静默", 1: "搜索", 2: "跟踪", 3: "成像", 4: "制导"}
            print(f"   工作模式: {work_mode} ({mode_names.get(work_mode, '未知')})")

        # 使用PPO处理流程
        cycle_result = self._execute_system_cycle(radar_input, 0, training_mode=training_mode)
        numerical_output = self._generate_numerical_output(cycle_result)

        if explain:
            self._explain_output(numerical_output, cycle_result)

            return numerical_output, cycle_result

    # 已删除：_explain_neural_output 方法（统一神经网络相关）


    def set_explanation_mode(self, enabled: bool):
        """设置输出解释模式"""
        self.explanation_mode = enabled
        print(f" 输出解释模式: {'开启' if enabled else '关闭'}")

    def get_system_status(self) -> Dict:
        """获取系统状态"""
        # PPO统计信息
        ppo_stats = {}
        if hasattr(self, 'actor_critic_accelerator'):
            ppo_stats = {
                'memory_size': len(self.actor_critic_accelerator.memory),
                'training_stats': self.actor_critic_accelerator.training_stats
            }

        return {
            'training_stats': self.training_stats,
            'ppo_stats': ppo_stats,
            'explanation_mode': self.explanation_mode,
            'model_path': self.config.model_save_path,
            'system_ready': True
        }

    def _generate_random_scenario(self) -> Dict:
        """生成随机雷达场景 - 高级优化版本，目标实现均匀分布"""
        # 进一步优化威胁等级权重以接近均匀分布
        # 当前分析：等级1(25.2%)，等级3(29.6%)过多，等级5(8.3%)仍然过少
        threat_weights = [0.16, 0.19, 0.1, 0.3, 0.25]  # 进一步减少等级1和3，大幅增加等级5
        threat_level_target = np.random.choice([1, 2, 3, 4, 5], p=threat_weights)

        # 使用多次尝试机制确保生成正确的威胁等级
        max_attempts = 10
        for attempt in range(max_attempts):
            scenario = self._generate_scenario_for_level(threat_level_target)

            # 快速验证威胁等级（每10次验证一次）
            if attempt == 0 or np.random.random() < 0.1:
                _, threat_level, _ = self.threat_evaluator.evaluate_threat(scenario)
                if threat_level.value == threat_level_target:
                    scenario['expected_threat_level'] = threat_level_target
                    return scenario
            else:
                scenario['expected_threat_level'] = threat_level_target
                return scenario

        # 如果多次尝试失败，返回最后一次生成的场景
        scenario['expected_threat_level'] = threat_level_target
        return scenario

    def _generate_scenario_for_level(self, threat_level_target: int) -> Dict:
        """为指定威胁等级生成优化参数"""
        if threat_level_target == 1:  # 极高威胁 (威胁度值 > 0.792)
            # 精确调整参数以减少等级1的生成频率
            distance = np.random.uniform(8, 35)  # km - 稍微增加距离下限
            operating_mode = np.random.choice([3, 4], p=[0.3, 0.7])  # 不全是制导模式
            prf_hz = np.random.uniform(6000, 15000)  # 稍微降低PRF下限
            prt = 1e6 / prf_hz  # 转换为PRT (μs)
            pulse_width = np.random.uniform(0.2, 2.5)  # 稍微增加脉宽上限
            frequency = np.random.uniform(7000, 16000)  # 稍微降低频率范围
            speed = np.random.uniform(180, 450)  # 稍微降低速度下限

        elif threat_level_target == 2:  # 高威胁 (威胁度值 0.655-0.792)
            # 稍微调整参数以增加等级2的生成频率
            distance = np.random.uniform(25, 75)  # km - 稍微减少距离范围
            operating_mode = np.random.choice([2, 3], p=[0.6, 0.4])  # 跟踪或成像模式
            prf_hz = np.random.uniform(4000, 10000)  # 中高PRF (Hz)
            prt = 1e6 / prf_hz  # 转换为PRT (μs)
            pulse_width = np.random.uniform(1.2, 4.5)  # 较短脉宽 (μs)
            frequency = np.random.uniform(4500, 11000)  # 中高频 (MHz)
            speed = np.random.uniform(90, 320)  # m/s

        elif threat_level_target == 3:  # 中等威胁 (威胁度值 0.421-0.655)
            # 大幅调整参数，使其更容易偏向等级2或等级4
            # 策略：生成更接近边界的参数，减少"稳定"的等级3参数
            if np.random.random() < 0.5:
                # 50%概率生成接近等级2的参数（威胁值接近0.655）
                distance = np.random.uniform(45, 90)  # 较近距离
                operating_mode = np.random.choice([2, 3], p=[0.7, 0.3])  # 更多跟踪模式
                prf_hz = np.random.uniform(3000, 6000)  # 中高PRF
                pulse_width = np.random.uniform(2.0, 5.0)  # 较短脉宽
                frequency = np.random.uniform(4000, 8000)  # 中高频
                speed = np.random.uniform(80, 250)  # 中高速
            else:
                # 50%概率生成接近等级4的参数（威胁值接近0.421）
                distance = np.random.uniform(100, 180)  # 较远距离
                operating_mode = np.random.choice([0, 1, 2], p=[0.4, 0.5, 0.1])  # 更多搜索模式
                prf_hz = np.random.uniform(1000, 3500)  # 较低PRF
                pulse_width = np.random.uniform(5.0, 12.0)  # 较长脉宽
                frequency = np.random.uniform(1500, 5000)  # 较低频
                speed = np.random.uniform(30, 150)  # 较低速
            prt = 1e6 / prf_hz  # 转换为PRT (μs)

        elif threat_level_target == 4:  # 低威胁 (威胁度值 0.210-0.421)
            # 调整参数以增加等级4的生成频率
            distance = np.random.uniform(100, 240)  # km - 远距离
            operating_mode = np.random.choice([0, 1, 2], p=[0.4, 0.45, 0.15])  # 主要是搜索模式
            prf_hz = np.random.uniform(600, 2800)  # 低PRF (Hz)
            prt = 1e6 / prf_hz  # 转换为PRT (μs)
            pulse_width = np.random.uniform(6.0, 18.0)  # 长脉宽 (μs)
            frequency = np.random.uniform(800, 4500)  # 低频 (MHz)
            speed = np.random.uniform(15, 130)  # m/s

        else:  # threat_level_target == 5: 极低威胁 (威胁度值 0.000-0.210)
            # 极端调整参数以最大化等级5的生成频率
            distance = np.random.uniform(220, 300)  # km - 极远距离
            operating_mode = np.random.choice([0, 1], p=[0.8, 0.2])  # 主要是静默模式
            prf_hz = np.random.uniform(50, 600)  # 极低PRF (Hz)
            prt = 1e6 / prf_hz  # 转换为PRT (μs)
            pulse_width = np.random.uniform(20.0, 40.0)  # 极长脉宽 (μs)
            frequency = np.random.uniform(200, 1500)  # 极低频 (MHz)
            speed = np.random.uniform(1, 50)  # m/s - 极低速

        return {
            # 统一使用威胁评估器期望的标准单位格式
            'frequency': frequency,           # 载频 (MHz) - 标准单位
            'pw': pulse_width,               # 脉宽 (μs) - 标准单位
            'prt': prt,                      # 脉冲重复周期 (μs) - 标准单位
            'distance': distance,            # 距离 (km) - 标准单位
            'speed': speed,                  # 速度 (m/s) - 标准单位
            'direction': np.random.uniform(-45, 45),  # 方向 (度) - 标准单位
            'work_mode': operating_mode,     # 工作模式 (0-4) - 标准单位

            # 训练专用的额外参数
            'expected_threat_level': threat_level_target  # 期望威胁等级
        }

    def _execute_system_cycle(self, radar_input: Dict, episode: int, training_mode: bool = True) -> Dict:
        """执行完整的系统循环"""
        cycle_start = time.time()

        # 步骤1: 威胁评估
        threat_data = self._assess_threat(radar_input)

        # 步骤2: 智能决策
        decision = self._make_decision(threat_data, radar_input, episode, training_mode)

        # 步骤3: 干扰信号生成
        jamming_signals = self._generate_jamming_signals(decision, threat_data)

        # 步骤4: 雷达自适应响应
        radar_response = self._simulate_radar_response(radar_input, jamming_signals)

        # 步骤5: 干扰效果评估和强化学习更新（在强化学习模块内部）
        reward = 0.0
        if training_mode:
            # 使用强化学习模块内部的效果评估和学习更新（已移除）
            reward = 0.5  # 默认奖励，decision_module已移除
            # 为了兼容性，仍然计算外部效果评估
            effectiveness = self._evaluate_effectiveness_for_display(decision, radar_response)
        else:
            # 非训练模式仍使用外部效果评估（用于显示）
            effectiveness = self._evaluate_effectiveness_for_display(decision, radar_response)

        cycle_time = time.time() - cycle_start

        return {
            'radar_input': radar_input,
            'threat_data': threat_data,
            'decision': decision,
            'jamming_signals': jamming_signals,
            'radar_response': radar_response,
            'effectiveness': effectiveness,
            'reward': reward,
            'cycle_time': cycle_time,
            'episode': episode
        }

    def _assess_threat(self, radar_input: Dict) -> ThreatAssessmentData:
        """威胁评估 - 使用标准单位格式"""
        radar_dict = {
            'speed': radar_input.get('speed', 0),                    # 速度 (m/s)
            'distance': radar_input.get('distance', 50.0),          # 距离 (km)
            'direction': radar_input.get('direction', 0.0),         # 航向角 (度)
            'prt': radar_input.get('prt', 1000.0),                  # 脉冲重复周期 (μs)
            'frequency': radar_input.get('frequency', 10000.0),     # 载频 (MHz)
            'pw': radar_input.get('pw', 1.0),                       # 脉宽 (μs)
            'work_mode': radar_input.get('work_mode', 1)            # 工作模式 (0-4)
        }

        threat_value, threat_level, details = self.threat_evaluator.evaluate_threat(radar_dict)

        # 返回ThreatAssessmentData对象
        return ThreatAssessmentData(
            threat_value=threat_value,
            threat_level=threat_level.value,
            threat_level_name=self._get_threat_level_name(threat_level.value),
            radar_type_threat=details['membership_values'].get('雷达类型威胁度', 0.5),
            platform_type_threat=details['membership_values'].get('平台类型威胁度', 0.5),
            distance_threat=details['membership_values'].get('距离威胁度', 0.5),
            frequency_threat=details['membership_values'].get('载频威胁度', 0.5),
            mode_threat=details['membership_values'].get('工作模式威胁度', 0.5),
            confidence=0.8
        )

    def _convert_work_mode(self, mode) -> int:
        """转换工作模式"""
        if isinstance(mode, int):
            return mode  # 已经是数字编码

        # 字符串到数字的映射 - 更新为5个模式
        mapping = {'静默': 0, '搜索': 1, '跟踪': 2, '成像': 3, '制导': 4, '预警': 1}  # 预警映射到搜索
        return mapping.get(mode, 1)

    def _get_threat_level_name(self, level: int) -> str:
        """获取威胁等级名称"""
        names = {1: "极高威胁", 2: "高威胁", 3: "中等威胁", 4: "低威胁", 5: "极低威胁"}
        return names.get(level, "未知威胁")

    def _get_mode_name(self, mode_code: int) -> str:
        """获取工作模式名称"""
        mode_names = {0: "静默", 1: "搜索", 2: "跟踪", 3: "成像", 4: "制导"}
        return mode_names.get(mode_code, "搜索")

    def _make_decision(self, threat_data: ThreatAssessmentData, radar_input: Dict, episode: int, training_mode: bool = True) -> Dict:
        """智能决策 - 使用标准单位格式"""
        # 获取工作模式
        work_mode = radar_input.get('work_mode', 1)
        if isinstance(work_mode, str):
            work_mode = self._convert_work_mode(work_mode)

        # 创建简化的雷达数据结构 - 使用标准单位格式
        radar_data = SimplifiedRadarData(
            frequency=radar_input.get('frequency', 10000.0),         # 载频 (MHz)
            pulse_width=radar_input.get('pw', 1.0),                  # 脉宽 (μs)
            prf=radar_input.get('prt', 1000.0),                      # 脉冲重复周期 (μs)
            operating_mode=work_mode,                                 # 工作模式 (0-4)
            target_range=radar_input.get('distance', 50.0),          # 目标距离 (km)
            target_velocity=radar_input.get('speed', 0.0)            # 目标速度 (m/s)
        )

        # 调用决策模块
        if not training_mode:
            # 验证模式：使用利用策略（PPO自动处理）
            pass
            if (hasattr(self, 'actor_critic_accelerator') and
                self.actor_critic_accelerator and
                hasattr(self.actor_critic_accelerator, 'epsilon')):
                original_gpu_epsilon = self.actor_critic_accelerator.epsilon
                self.actor_critic_accelerator.epsilon = 0.0

        # 使用PPO决策模式
        if (hasattr(self, 'actor_critic_accelerator') and
            self.actor_critic_accelerator):

            # 检查是否使用直接参数输出模式
            if getattr(self.actor_critic_accelerator, 'direct_params', False):
                # 直接参数输出模式
                decision = self._make_direct_params_decision(threat_data, radar_data, training_mode)
            else:
                # 使用PPO决策
                decision = self._make_ppo_decision(threat_data, radar_data, training_mode)
        else:
            # 如果没有PPO加速器，使用智能规则决策
            decision = self._make_intelligent_rule_decision(threat_data, radar_data)

        if not training_mode:
            # 恢复原始探索率（已移除）
            pass  # decision_module已移除
            # 恢复GPU加速器的探索率（仅适用于DQN类型的加速器）
            if (hasattr(self, 'actor_critic_accelerator') and
                self.actor_critic_accelerator and
                hasattr(self.actor_critic_accelerator, 'epsilon')):
                self.actor_critic_accelerator.epsilon = original_gpu_epsilon

        # 构建基础决策结果
        decision_result = {
            'should_jam': decision.should_jam,
            'jamming_type': decision.jamming_type,
            'jamming_power': decision.jamming_power,
            'jamming_frequency': decision.jamming_frequency,
            'jamming_params': decision.jamming_params,
            'confidence': decision.confidence,
            'decision_reason': decision.decision_reason,
            'decision_object': decision
        }

        # 集成频率同步跟踪 - 使用标准单位格式
        if decision.should_jam:
            jamming_types = decision.jamming_params.get('combination', [])
            current_radar_freq_mhz = radar_input.get('frequency', 10500.0)  # MHz
            current_radar_freq_hz = current_radar_freq_mhz * 1e6  # MHz -> Hz

            # 更新频率跟踪系统
            tracking_result = self.frequency_tracker.update_radar_frequency(
                new_radar_freq=current_radar_freq_hz,
                jamming_types=jamming_types
            )

            # 将频率跟踪信息添加到决策中
            decision_result.update({
                'current_radar_frequency': current_radar_freq_mhz,  # 存储MHz值
                'frequency_tracking_enabled': True,  # 始终启用基于载频的跟踪
                'radar_frequency_ghz': current_radar_freq_mhz / 1000.0,  # 雷达频率(GHz)
                'synchronized_jamming_frequencies': tracking_result['new_jamming_frequencies']
            })

            # 只在部署模式下显示频率跟踪信息
            if (self.config.enable_detailed_log and tracking_result['frequency_changed'] and
                hasattr(self, '_is_deployment_mode') and self._is_deployment_mode):
                print(f"频率同步跟踪激活:")
                print(f"   频率变化: {tracking_result['frequency_delta']/1e6:+.1f} MHz")
                print(f"   同步干扰频点: {len(tracking_result['new_jamming_frequencies'])} 个")

        return decision_result

    def _make_ppo_decision(self, threat_data, radar_data, training_mode: bool = True):
        """使用PPO算法进行决策"""
        try:
            # 构建状态字典（PPO期望字典格式）
            state_dict = self._build_state_dict(threat_data, radar_data)

            # 调试信息：显示状态字典
            if self.explanation_mode:
                print(f"   状态字典: {state_dict}")

            # 使用PPO加速器进行决策，传入威胁等级
            action_dict = self.actor_critic_accelerator.select_action(state_dict, training_mode, threat_data.threat_level)

            # 调试信息：显示PPO决策过程
            if self.explanation_mode:
                print(f"   PPO决策详情:")
                print(f"     使用威胁等级: {threat_data.threat_level} (来自威胁评估模块)")
                print(f"     干扰类型概率: {[f'{i}:{p:.3f}' for i, p in enumerate(action_dict['jamming_type_probs'])]}")
                print(f"     选择的干扰类型: {action_dict['jamming_type']}")
                print(f"     是否干扰: {action_dict['should_jam']}")
                print(f"     干扰功率: {action_dict['jamming_power']:.3f}")

            # 转换为决策对象格式
            from jamming_decision_core import JammingDecisionData

            # 处理多干扰组合
            jamming_combinations = []
            if action_dict['should_jam']:
                # 检查是否有多干扰组合
                if 'jamming_combinations' in action_dict and action_dict['jamming_combinations']:
                    # 使用多干扰组合
                    for jamming_type_id, _ in action_dict['jamming_combinations']:
                        jamming_type_name = self.jamming_type_mapping.get(jamming_type_id, "未知干扰")
                        jamming_combinations.append(jamming_type_name)
                else:
                    # 单一干扰
                    jamming_type_name = self.jamming_type_mapping.get(action_dict['jamming_type'], "未知干扰")
                    jamming_combinations.append(jamming_type_name)

            decision = JammingDecisionData(
                should_jam=action_dict['should_jam'],
                jamming_type=action_dict['jamming_type'],
                jamming_power=action_dict['jamming_power'],
                jamming_frequency=action_dict.get('jamming_frequency', getattr(radar_data, 'frequency', 10000.0)),
                jamming_params={'combination': jamming_combinations},
                confidence=0.8,
                decision_reason="PPO算法决策",
                priority=threat_data.threat_level,  # 使用威胁评估模块的威胁等级
                duration=5.0
            )

            return decision

        except Exception as e:
            print(f"PPO决策失败: {e}")
            return self._make_default_decision(threat_data, radar_data)

    def _make_default_decision(self, threat_data, radar_data):
        """默认决策（当PPO不可用时）"""
        from jamming_decision_core import JammingDecisionData

        # 基于威胁等级的简单决策
        should_jam = threat_data.threat_level <= 3  # 威胁等级1-3需要干扰
        jamming_type = 1 if should_jam else 0  # 默认使用类型1干扰
        jamming_power = 0.7 if should_jam else 0.0

        # 转换干扰类型ID为名称
        jamming_type_name = self.jamming_type_mapping.get(jamming_type, "未知干扰") if should_jam else ""

        decision = JammingDecisionData(
            should_jam=should_jam,
            jamming_type=jamming_type,
            jamming_power=jamming_power,
            jamming_frequency=getattr(radar_data, 'frequency', 10000.0),
            jamming_params={'combination': [jamming_type_name] if should_jam else []},
            confidence=0.6,
            decision_reason="默认规则决策",
            priority=2,
            duration=3.0
        )

        return decision

    def _make_intelligent_rule_decision(self, threat_data, radar_data):
        """智能规则决策 - 基于专家知识的决策逻辑"""
        try:
            from jamming_decision_core import JammingDecisionData

            threat_level = threat_data.threat_level
            work_mode = getattr(radar_data, 'operating_mode', 1)
            frequency = getattr(radar_data, 'frequency', 10000.0)

            # 基于威胁等级的基础决策
            if threat_level <= 2:  # 极高威胁(1)和高威胁(2)
                should_jam = True
                # 根据工作模式选择最优干扰类型
                if work_mode == 1:  # 搜索雷达
                    jamming_type = 2  # 噪声干扰
                    jamming_power = 0.8
                elif work_mode == 2:  # 跟踪雷达
                    jamming_type = 1  # 间歇采样
                    jamming_power = 0.9
                elif work_mode == 3:  # 成像雷达
                    jamming_type = 3  # 灵巧噪声
                    jamming_power = 0.7
                elif work_mode == 4:  # 制导雷达
                    jamming_type = 4  # 拖引干扰
                    jamming_power = 0.9
                else:
                    jamming_type = 2  # 默认噪声干扰
                    jamming_power = 0.8

            elif threat_level == 3:  # 中等威胁
                should_jam = True
                # 中等威胁使用适中的干扰策略
                if work_mode == 2:  # 跟踪雷达优先干扰
                    jamming_type = 1  # 间歇采样
                    jamming_power = 0.6
                elif work_mode == 4:  # 制导雷达优先干扰
                    jamming_type = 4  # 拖引干扰
                    jamming_power = 0.7
                else:
                    jamming_type = 2  # 噪声干扰
                    jamming_power = 0.5

            else:  # 低威胁(4)和极低威胁(5)
                # 低威胁情况下，只对关键雷达进行轻度干扰
                if work_mode == 4:  # 制导雷达仍需干扰
                    should_jam = True
                    jamming_type = 2  # 轻度噪声干扰
                    jamming_power = 0.3
                else:
                    should_jam = False
                    jamming_type = 0
                    jamming_power = 0.0

            # 根据频率调整干扰参数
            if should_jam:
                if frequency < 2000:  # 低频段
                    jamming_power *= 1.2  # 增加功率
                elif frequency > 15000:  # 高频段
                    jamming_power *= 0.8  # 减少功率

                # 确保功率在合理范围内
                jamming_power = min(1.0, max(0.1, jamming_power))

            # 转换干扰类型ID为名称
            jamming_type_name = self.jamming_type_mapping.get(jamming_type, "未知干扰") if should_jam else ""

            # 创建决策对象
            decision = JammingDecisionData(
                should_jam=should_jam,
                jamming_type=jamming_type,
                jamming_power=jamming_power,
                jamming_frequency=frequency,
                jamming_params={'combination': [jamming_type_name] if should_jam else []},
                confidence=0.9,  # 规则决策置信度高
                decision_reason=f"智能规则决策: 威胁等级{threat_level}, 工作模式{work_mode}",
                priority=threat_level,
                duration=5.0
            )

            if self.explanation_mode:
                print(f"   智能规则决策:")
                print(f"     威胁等级: {threat_level}")
                print(f"     工作模式: {work_mode}")
                print(f"     是否干扰: {should_jam}")
                print(f"     干扰类型: {jamming_type}")
                print(f"     干扰功率: {jamming_power:.3f}")
                print(f"     决策理由: {decision.decision_reason}")

            return decision

        except Exception as e:
            print(f"智能规则决策失败: {e}")
            return self._make_default_decision(threat_data, radar_data)

    def _build_state_dict(self, threat_data, radar_data):
        """构建PPO算法的状态字典"""
        try:
            # 处理SimplifiedRadarData对象或字典格式
            if hasattr(radar_data, 'frequency'):
                # SimplifiedRadarData对象
                state_dict = {
                    'frequency': getattr(radar_data, 'frequency', 10000.0) * 1e6,  # MHz -> Hz
                    'pw': getattr(radar_data, 'pulse_width', 1.0) * 1e-6,          # μs -> s
                    'prt': getattr(radar_data, 'prf', 100.0),                      # 保持μs单位
                    'power': 1e6,  # 默认功率
                    'distance': getattr(radar_data, 'target_range', 50.0),         # km
                    'speed': getattr(radar_data, 'target_velocity', 0.0),          # m/s
                    'direction': 0.0,  # SimplifiedRadarData中没有此字段
                    'work_mode': getattr(radar_data, 'operating_mode', 1),         # 工作模式
                    'threat_level': threat_data.threat_level,
                    'threat_value': float(threat_data.threat_value),  # 确保是Python float
                    'threat_confidence': threat_data.confidence,
                    'threat_urgency': max(0.0, (6 - threat_data.threat_level) / 5.0)
                }
            else:
                # 字典格式（向后兼容）
                state_dict = {
                    'frequency': radar_data.get('frequency', 10000.0) * 1e6,  # MHz -> Hz
                    'pw': radar_data.get('pw', 1.0) * 1e-6,                   # μs -> s
                    'prt': radar_data.get('prt', 100.0),                      # 保持μs单位
                    'power': radar_data.get('power', 1e6),
                    'distance': radar_data.get('distance', 50.0),
                    'speed': radar_data.get('speed', 0.0),
                    'direction': radar_data.get('direction', 0.0),
                    'work_mode': radar_data.get('work_mode', 1),
                    'threat_level': threat_data.threat_level,
                    'threat_value': float(threat_data.threat_value),  # 确保是Python float
                    'threat_confidence': threat_data.confidence,
                    'threat_urgency': max(0.0, (6 - threat_data.threat_level) / 5.0)
                }

            return state_dict

        except Exception as e:
            print(f"构建状态字典失败: {e}")
            # 返回默认状态字典
            return {
                'frequency': 10e9, 'pw': 1e-6, 'prt': 100e-6, 'power': 1e6,
                'distance': 50.0, 'speed': 0.0, 'direction': 0.0, 'work_mode': 1,
                'threat_level': 3, 'threat_value': 0.5, 'threat_confidence': 0.8, 'threat_urgency': 0.5
            }

    def _build_state_vector(self, threat_data, radar_data):
        """构建PPO算法的状态向量"""
        try:
            # 基本威胁信息
            # 计算紧急度：威胁等级越低（数值越小）紧急度越高
            urgency = max(0.0, (6 - threat_data.threat_level) / 5.0)

            state = [
                threat_data.threat_level / 5.0,  # 归一化威胁等级
                threat_data.threat_value,
                threat_data.confidence,
                urgency,  # 计算得出的紧急度
            ]

            # 雷达参数（归一化）- 正确访问SimplifiedRadarData对象的属性
            if hasattr(radar_data, 'frequency'):
                # radar_data是SimplifiedRadarData对象
                state.extend([
                    getattr(radar_data, 'frequency', 10000.0) / 20000.0,        # 频率归一化
                    getattr(radar_data, 'pulse_width', 1.0) / 10.0,             # 脉宽归一化
                    getattr(radar_data, 'prf', 100.0) / 1000.0,                 # PRT归一化
                    1e6 / 1e7,                                                   # 默认功率归一化
                    getattr(radar_data, 'target_range', 50.0) / 300.0,          # 距离归一化
                    getattr(radar_data, 'target_velocity', 300.0) / 1000.0,     # 速度归一化
                    0.0,                                                         # 方向（SimplifiedRadarData中没有此字段）
                    getattr(radar_data, 'operating_mode', 1) / 4.0              # 工作模式归一化
                ])
            else:
                # radar_data是字典格式（向后兼容）
                state.extend([
                    radar_data.get('frequency', 10000.0) / 20000.0,  # 频率归一化
                    radar_data.get('pw', 1.0) / 10.0,                # 脉宽归一化
                    radar_data.get('prt', 100.0) / 1000.0,           # PRT归一化
                    radar_data.get('power', 1e6) / 1e7,              # 功率归一化
                    radar_data.get('distance', 50.0) / 300.0,        # 距离归一化
                    radar_data.get('speed', 300.0) / 1000.0,         # 速度归一化
                    radar_data.get('direction', 0.0) / 360.0,        # 方向归一化
                    radar_data.get('work_mode', 1) / 4.0             # 工作模式归一化
                ])

            # 确保状态向量长度为12
            while len(state) < 12:
                state.append(0.0)

            return state[:12]  # 截断到12维

        except Exception as e:
            print(f"构建状态向量失败: {e}")
            return [0.0] * 12  # 返回默认状态向量

    def _generate_jamming_signals(self, decision: Dict, threat_data) -> Dict:
        """生成干扰信号"""
        if not decision['should_jam']:
            return {'signals': [], 'total_power': 0.0}

        # 生成干扰信号（简化版本）
        # 创建简化的决策输出
        decision_output = []  # 简化版本，不需要复杂的输出格式

        # 创建模拟雷达信号
        radar_signal = np.random.randn(1000) + 1j * np.random.randn(1000)
        radar_params = {'frequency': decision['jamming_frequency'], 'target_range': 30000}

        # 生成干扰信号
        jamming_result = self.signal_generator.generate_jamming_signals(
            decision_output=decision_output,
            radar_signal=radar_signal,
            radar_params=radar_params
        )

        jamming_combination = decision['jamming_params'].get('combination', [])

        return {
            'signals': jamming_result.get('jamming_signals', []),
            'jamming_types': jamming_result.get('jamming_types', jamming_combination),
            'total_power': decision['jamming_power'],
            'frequency': decision['jamming_frequency'],
            'jamming_result': jamming_result
        }

    def _simulate_radar_response(self, radar_input: Dict, jamming_signals: Dict) -> Dict:
        """模拟雷达自适应响应"""
        if not jamming_signals['signals']:
            return {'adapted': False, 'original_params': radar_input}

        # 创建雷达参数对象 - 转换标准单位格式到RadarEmissionData期望的格式
        frequency_hz = radar_input.get('frequency', 10000.0) * 1e6  # MHz -> Hz
        pulse_width_s = radar_input.get('pw', 1.0) * 1e-6  # μs -> s
        prf_hz = 1e6 / radar_input.get('prt', 1000.0)  # μs -> Hz

        work_mode = radar_input.get('work_mode', 1)
        operating_mode_name = self._get_mode_name(work_mode) if isinstance(work_mode, int) else work_mode

        radar_params = RadarEmissionData(
            peak_power=radar_input.get('peak_power', 1.0e6),  # 默认功率
            frequency=frequency_hz,  # Hz
            pulse_width=pulse_width_s,  # s
            prf=prf_hz,  # Hz
            antenna_gain=radar_input.get('antenna_gain', 35.0),  # 默认天线增益
            operating_mode=operating_mode_name,
            beam_direction=radar_input.get('direction', 0.0),
            scan_pattern=radar_input.get('scan_pattern', "sector"),  # 默认扫描模式
            target_range=radar_input.get('distance', 50.0) * 1000,  # km -> m
            target_velocity=radar_input.get('speed', 0.0),
            target_rcs=1.0  # 默认RCS
        )

        # 创建模拟雷达信号
        radar_signal = np.random.randn(1000) + 1j * np.random.randn(1000)

        # 处理干扰信号数据格式
        if jamming_signals['signals']:
            # 如果signals包含字典（测试数据格式），需要生成实际信号
            if isinstance(jamming_signals['signals'][0], dict):
                # 生成实际的干扰信号数组
                actual_jamming_signals = []
                jamming_types_list = []

                for signal_info in jamming_signals['signals']:
                    # 创建简单的模拟干扰信号
                    signal_type = signal_info.get('type', '未知')
                    power = signal_info.get('power', 0.5)

                    # 生成对应类型的模拟信号
                    if signal_type == '梳状谱':
                        # 生成梳状谱信号
                        t = np.arange(1000) / 1e9
                        freq_offsets = signal_info.get('parameters', {}).get('frequency_offsets', [100e3])
                        signal = np.zeros(1000, dtype=complex)
                        for offset in freq_offsets:
                            signal += np.sqrt(power) * np.exp(1j * 2 * np.pi * offset * t)
                        actual_jamming_signals.append(signal)
                    else:
                        # 默认生成噪声信号
                        signal = np.sqrt(power) * (np.random.randn(1000) + 1j * np.random.randn(1000))
                        actual_jamming_signals.append(signal)

                    jamming_types_list.append(signal_type)
            else:
                # 如果signals已经是信号数组，直接使用
                actual_jamming_signals = jamming_signals['signals']
                jamming_types_list = jamming_signals.get('jamming_types', [])
        else:
            actual_jamming_signals = []
            jamming_types_list = []

        # 检测干扰
        interference_info = self.radar_response.detect_interference(
            received_signal=radar_signal,
            radar_signal=radar_signal,
            jamming_signals=actual_jamming_signals,
            jamming_types=jamming_types_list
        )

        # 自适应调整参数
        adapted_params = self.radar_response.adapt_radar_parameters(
            radar_params=radar_params,
            interference_info=interference_info
        )

        # 检查是否发生了自适应
        adapted = (adapted_params.frequency != radar_params.frequency or
                  adapted_params.prf != radar_params.prf or
                  adapted_params.pulse_width != radar_params.pulse_width)

        return {
            'adapted': adapted,
            'original_params': radar_input,
            'adapted_params': {
                'peak_power': adapted_params.peak_power,
                'frequency': adapted_params.frequency,
                'pulse_width': adapted_params.pulse_width,
                'prf': adapted_params.prf,
                'antenna_gain': adapted_params.antenna_gain,
                'operating_mode': adapted_params.operating_mode
            },
            'interference_info': interference_info
        }

    def _evaluate_effectiveness_for_display(self, decision: Dict, radar_response: Dict) -> Dict:
        """直接基于雷达参数评估干扰效果（仅用于显示）"""
        if not decision['should_jam']:
            return {
                'overall_effectiveness': 0.0,
                'threat_reduction': 0.0,
                'snr_degradation': 0.0,
                'detection_probability': 1.0,
                'radar_adaptation': radar_response
            }

        # 获取雷达参数 - 使用标准单位格式
        radar_params = radar_response.get('radar_params', {})
        peak_power = radar_params.get('peak_power', 1e6)  # 峰值功率 (W)
        frequency = radar_params.get('frequency', 10000.0)   # 载波频率 (MHz)
        pulse_width = radar_params.get('pw', 1.0)  # 脉冲宽度 (μs)
        prt = radar_params.get('prt', 1000.0)  # 脉冲重复周期 (μs)
        operating_mode = radar_params.get('operating_mode', '搜索')  # 工作模式

        # 计算信号带宽 (基于脉冲宽度)
        signal_bandwidth = 1.0 / (pulse_width * 1e-6)  # Hz (从μs转换)

        # 获取干扰参数
        jamming_power = decision.get('jamming_power', 0.5)
        jamming_combination = decision.get('jamming_params', {}).get('combination', [])

        # 直接基于雷达参数评估干扰效果
        effectiveness_result = self._evaluate_jamming_effectiveness_by_radar_parameters(
            peak_power, frequency, pulse_width, prt, signal_bandwidth, operating_mode,
            jamming_power, jamming_combination
        )

        return {
            'overall_effectiveness': effectiveness_result['overall_effectiveness'],
            'threat_reduction': effectiveness_result['threat_reduction'],
            'snr_degradation': effectiveness_result['estimated_snr_degradation'],
            'detection_probability': effectiveness_result['estimated_detection_probability'],
            'radar_adaptation': radar_response
        }

    def _evaluate_jamming_effectiveness_by_radar_parameters(self, peak_power: float, frequency: float,
                                                          pulse_width: float, prt: float, signal_bandwidth: float,
                                                          operating_mode: str, jamming_power: float,
                                                          jamming_combination: List[str]) -> Dict:
        """直接基于雷达参数评估干扰效果 - 使用标准单位格式"""

        # 1. 基于雷达参数特征评估干扰效果
        effectiveness_score = self._calculate_effectiveness_from_radar_parameters(
            peak_power, frequency, pulse_width, prt, signal_bandwidth, operating_mode,
            jamming_power, jamming_combination
        )

        # 2. 计算威胁降低程度
        threat_reduction = self._calculate_threat_reduction_from_parameters(
            effectiveness_score, operating_mode, jamming_combination, peak_power, frequency
        )

        # 3. 估算干扰效果指标（用于反馈）
        estimated_metrics = self._estimate_jamming_metrics(
            effectiveness_score, peak_power, frequency, pulse_width, prt, operating_mode
        )

        return {
            'overall_effectiveness': effectiveness_score,
            'threat_reduction': threat_reduction,
            'estimated_snr_degradation': estimated_metrics['snr_degradation'],
            'estimated_detection_probability': estimated_metrics['detection_probability']
        }





    def _calculate_effectiveness_from_radar_parameters(self, peak_power: float, frequency: float,
                                                     pulse_width: float, prt: float, signal_bandwidth: float,
                                                     operating_mode: str, jamming_power: float,
                                                     jamming_combination: List[str]) -> float:
        """直接基于雷达参数计算干扰效果 - 使用标准单位格式"""

        # 1. 雷达参数特征分析
        radar_characteristics = self._analyze_radar_characteristics(
            peak_power, frequency, pulse_width, prt, signal_bandwidth, operating_mode
        )

        # 2. 干扰类型与雷达参数的匹配度分析
        jamming_radar_match = self._analyze_jamming_radar_match(
            jamming_combination, radar_characteristics
        )

        # 3. 基于参数的干扰效果计算
        parameter_based_effectiveness = self._calculate_parameter_based_jamming_effect(
            radar_characteristics, jamming_radar_match, jamming_power
        )

        return parameter_based_effectiveness

    def _analyze_radar_characteristics(self, peak_power: float, frequency: float,
                                     pulse_width: float, prt: float, signal_bandwidth: float,
                                     operating_mode: str) -> Dict:
        """分析雷达参数特征 - 使用标准单位格式"""

        # 1. 功率特征分析
        power_level = 'high' if peak_power > 1e6 else 'medium' if peak_power > 1e5 else 'low'

        # 2. 频率特征分析 - frequency 现在是MHz
        freq_mhz = frequency
        if freq_mhz > 8000:  # >8GHz
            freq_band = 'X'  # X波段
        elif freq_mhz > 4000:  # >4GHz
            freq_band = 'C'  # C波段
        elif freq_mhz > 2000:  # >2GHz
            freq_band = 'S'  # S波段
        else:
            freq_band = 'L'  # L波段及以下

        # 3. 脉冲特征分析 - pulse_width 现在是μs
        pw_us = pulse_width
        if pw_us < 1:
            pulse_type = 'short'    # 短脉冲
        elif pw_us < 5:
            pulse_type = 'medium'   # 中等脉冲
        else:
            pulse_type = 'long'     # 长脉冲

        # 4. PRT特征分析 - prt 现在是μs，计算PRF
        prf_hz = 1e6 / prt  # μs -> Hz
        if prf_hz > 5000:
            prt_type = 'high'       # 高PRF
        elif prf_hz > 1000:
            prt_type = 'medium'     # 中等PRF
        else:
            prt_type = 'low'        # 低PRF

        # 5. 带宽特征分析
        if signal_bandwidth > 10e6:
            bandwidth_type = 'wide'     # 宽带
        elif signal_bandwidth > 1e6:
            bandwidth_type = 'medium'   # 中等带宽
        else:
            bandwidth_type = 'narrow'   # 窄带

        # 6. 工作模式特征
        mode_priority = {
            '制导': 'critical',     # 关键模式
            '跟踪': 'high',        # 高优先级
            '搜索': 'medium',      # 中等优先级
            '预警': 'low',         # 低优先级
            '成像': 'high'         # 高优先级
        }

        return {
            'power_level': power_level,
            'freq_band': freq_band,
            'pulse_type': pulse_type,
            'prf_type': prt_type,
            'bandwidth_type': bandwidth_type,
            'mode_priority': mode_priority.get(operating_mode, 'medium'),
            'operating_mode': operating_mode,
            'peak_power': peak_power,
            'frequency': frequency,
            'pulse_width': pulse_width,
            'prf': prf_hz,
            'signal_bandwidth': signal_bandwidth
        }

    def _analyze_jamming_radar_match(self, jamming_combination: List[str],
                                   radar_characteristics: Dict) -> Dict:
        """分析干扰类型与雷达参数的匹配度"""

        # 干扰类型对不同雷达特征的效果矩阵
        jamming_effectiveness_matrix = {
            '梳状谱': {
                'power_level': {'high': 0.6, 'medium': 0.7, 'low': 0.8},
                'freq_band': {'X': 0.8, 'C': 0.7, 'S': 0.6, 'L': 0.5},
                'pulse_type': {'short': 0.5, 'medium': 0.7, 'long': 0.8},
                'prf_type': {'high': 0.6, 'medium': 0.8, 'low': 0.7},
                'bandwidth_type': {'wide': 0.4, 'medium': 0.7, 'narrow': 0.9},
                'mode_priority': {'critical': 0.8, 'high': 0.7, 'medium': 0.6, 'low': 0.5}
            },
            '间歇采样转发': {
                'power_level': {'high': 0.8, 'medium': 0.7, 'low': 0.6},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.9, 'medium': 0.8, 'long': 0.6},
                'prf_type': {'high': 0.8, 'medium': 0.7, 'low': 0.6},
                'bandwidth_type': {'wide': 0.6, 'medium': 0.8, 'narrow': 0.9},
                'mode_priority': {'critical': 0.9, 'high': 0.8, 'medium': 0.7, 'low': 0.6}
            },
            '宽带阻塞噪声': {
                'power_level': {'high': 0.5, 'medium': 0.6, 'low': 0.8},
                'freq_band': {'X': 0.7, 'C': 0.7, 'S': 0.7, 'L': 0.7},
                'pulse_type': {'short': 0.7, 'medium': 0.7, 'long': 0.7},
                'prf_type': {'high': 0.7, 'medium': 0.7, 'low': 0.7},
                'bandwidth_type': {'wide': 0.9, 'medium': 0.7, 'narrow': 0.5},
                'mode_priority': {'critical': 0.6, 'high': 0.7, 'medium': 0.8, 'low': 0.9}
            },
            '灵巧噪声': {
                'power_level': {'high': 0.7, 'medium': 0.8, 'low': 0.9},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.8, 'medium': 0.8, 'long': 0.7},
                'prf_type': {'high': 0.8, 'medium': 0.8, 'low': 0.7},
                'bandwidth_type': {'wide': 0.7, 'medium': 0.8, 'narrow': 0.9},
                'mode_priority': {'critical': 0.9, 'high': 0.8, 'medium': 0.7, 'low': 0.6}
            },
            '拖引': {
                'power_level': {'high': 0.9, 'medium': 0.8, 'low': 0.7},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.8, 'medium': 0.9, 'long': 0.8},
                'prf_type': {'high': 0.9, 'medium': 0.8, 'low': 0.7},
                'bandwidth_type': {'wide': 0.8, 'medium': 0.8, 'narrow': 0.8},
                'mode_priority': {'critical': 0.95, 'high': 0.9, 'medium': 0.8, 'low': 0.7}
            }
        }

        # 计算每种干扰类型的匹配度
        jamming_match_scores = {}
        for jamming_type in jamming_combination:
            if jamming_type in jamming_effectiveness_matrix:
                effectiveness_factors = jamming_effectiveness_matrix[jamming_type]

                # 计算各特征的匹配得分
                match_score = 1.0
                for characteristic, value in radar_characteristics.items():
                    if characteristic in effectiveness_factors and value in effectiveness_factors[characteristic]:
                        factor = effectiveness_factors[characteristic][value]
                        match_score *= factor

                jamming_match_scores[jamming_type] = match_score
            else:
                jamming_match_scores[jamming_type] = 0.5  # 默认匹配度

        return jamming_match_scores

    def _calculate_parameter_based_jamming_effect(self, radar_characteristics: Dict,
                                                jamming_match_scores: Dict, jamming_power: float) -> float:
        """基于参数计算干扰效果"""

        # 1. 计算干扰类型综合匹配度
        if jamming_match_scores:
            avg_match_score = sum(jamming_match_scores.values()) / len(jamming_match_scores)
        else:
            avg_match_score = 0.0

        # 2. 雷达抗干扰能力评估
        resistance_factors = self._calculate_radar_resistance_factors(radar_characteristics)

        # 3. 基础干扰效果计算
        base_effectiveness = avg_match_score * jamming_power

        # 4. 应用抗干扰能力修正
        resistance_factor = (
            resistance_factors['power_resistance'] * 0.3 +
            resistance_factors['frequency_resistance'] * 0.2 +
            resistance_factors['pulse_resistance'] * 0.2 +
            resistance_factors['bandwidth_resistance'] * 0.3
        )

        # 抗干扰能力越强，干扰效果越差
        final_effectiveness = base_effectiveness * (2.0 - resistance_factor)

        return min(1.0, max(0.0, final_effectiveness))

    def _calculate_radar_resistance_factors(self, radar_characteristics: Dict) -> Dict:
        """计算雷达抗干扰能力因子"""

        # 1. 功率抗干扰因子（功率越高，抗干扰能力越强）
        power_resistance = {
            'high': 1.2,    # 高功率雷达抗干扰能力强
            'medium': 1.0,  # 中等功率雷达
            'low': 0.8      # 低功率雷达抗干扰能力弱
        }.get(radar_characteristics['power_level'], 1.0)

        # 2. 频率抗干扰因子（高频雷达某些方面抗干扰能力强）
        frequency_resistance = {
            'X': 1.1,       # X波段
            'C': 1.0,       # C波段
            'S': 0.9,       # S波段
            'L': 0.8        # L波段
        }.get(radar_characteristics['freq_band'], 1.0)

        # 3. 脉冲抗干扰因子（短脉冲抗干扰能力强）
        pulse_resistance = {
            'short': 1.3,   # 短脉冲抗干扰能力强
            'medium': 1.0,  # 中等脉冲
            'long': 0.7     # 长脉冲抗干扰能力弱
        }.get(radar_characteristics['pulse_type'], 1.0)

        # 4. 带宽抗干扰因子（宽带信号抗干扰能力强）
        bandwidth_resistance = {
            'wide': 1.4,    # 宽带信号抗干扰能力强
            'medium': 1.0,  # 中等带宽
            'narrow': 0.6   # 窄带信号抗干扰能力弱
        }.get(radar_characteristics['bandwidth_type'], 1.0)

        return {
            'power_resistance': power_resistance,
            'frequency_resistance': frequency_resistance,
            'pulse_resistance': pulse_resistance,
            'bandwidth_resistance': bandwidth_resistance
        }

    def _calculate_threat_reduction_from_parameters(self, effectiveness_score: float, operating_mode: str,
                                                  jamming_combination: List[str], peak_power: float,
                                                  frequency: float) -> float:
        """基于参数计算威胁降低程度"""

        # 基础威胁降低 = 干扰效果得分
        base_threat_reduction = effectiveness_score

        # 工作模式威胁系数（不同模式的威胁程度不同）
        mode_threat_coefficients = {
            '制导': 1.0,    # 制导模式威胁最高，干扰效果最显著
            '跟踪': 0.9,    # 跟踪模式威胁较高
            '搜索': 0.7,    # 搜索模式威胁中等
            '预警': 0.6,    # 预警模式威胁较低
            '成像': 0.8     # 成像模式威胁较高
        }

        mode_coefficient = mode_threat_coefficients.get(operating_mode, 0.7)

        # 雷达威胁程度评估（基于功率和频率） - frequency 现在是MHz
        power_threat_factor = min(1.5, peak_power / 1e6)  # 功率越高威胁越大
        freq_threat_factor = min(1.2, frequency / 10000.0)   # 频率越高威胁越大 (MHz)
        radar_threat_level = (power_threat_factor + freq_threat_factor) / 2.0

        # 干扰类型威胁降低贡献
        jamming_threat_reduction_factors = {
            '梳状谱': 0.6,           # 梳状谱威胁降低贡献
            '间歇采样转发': 0.8,      # 间歇采样威胁降低贡献
            '宽带阻塞噪声': 0.5,      # 宽带噪声威胁降低贡献
            '灵巧噪声': 0.9,         # 灵巧噪声威胁降低贡献
            '拖引': 0.95             # 拖引威胁降低贡献最大
        }

        jamming_factor = 0.0
        for jamming_type in jamming_combination:
            factor = jamming_threat_reduction_factors.get(jamming_type, 0.5)
            jamming_factor += factor

        if jamming_combination:
            jamming_factor = jamming_factor / len(jamming_combination)
        else:
            jamming_factor = 0.0

        # 综合威胁降低程度
        threat_reduction = (
            base_threat_reduction *
            mode_coefficient *
            radar_threat_level *
            (0.5 + 0.5 * jamming_factor)
        )

        return min(1.0, max(0.0, threat_reduction))

    def _estimate_jamming_metrics(self, effectiveness_score: float, peak_power: float,
                                frequency: float, pulse_width: float, prt: float,
                                operating_mode: str) -> Dict:
        """基于效果得分估算干扰指标（用于反馈） - 使用标准单位格式"""

        # 1. 估算SNR退化
        # 基于效果得分和雷达参数估算可能的SNR退化
        base_snr_degradation = effectiveness_score * 20.0  # 最大20dB退化

        # 根据雷达参数调整估算 - frequency 现在是MHz
        power_factor = min(1.2, peak_power / 1e6)  # 功率越高，SNR退化相对较小
        freq_factor = max(0.8, frequency / 10000.0)   # 频率越高，某些干扰效果越好 (MHz)

        estimated_snr_degradation = base_snr_degradation / power_factor * freq_factor

        # 2. 估算检测概率
        # 基于工作模式的基线检测概率
        baseline_detection_probs = {
            '制导': 0.95,
            '跟踪': 0.92,
            '搜索': 0.85,
            '预警': 0.80,
            '成像': 0.90
        }

        baseline_detection = baseline_detection_probs.get(operating_mode, 0.85)

        # 基于效果得分估算检测概率下降
        detection_degradation = effectiveness_score * baseline_detection * 0.7
        estimated_detection_probability = max(0.05, baseline_detection - detection_degradation)

        return {
            'snr_degradation': estimated_snr_degradation,
            'detection_probability': estimated_detection_probability
        }











    def _generate_numerical_output(self, cycle_result: Dict) -> List:
        """生成标准化数值输出 - 使用Actor网络的标准格式"""
        decision = cycle_result['decision']
        threat_data = cycle_result['threat_data']

        # 优先使用Actor网络的标准输出
        if 'standard_output' in decision and decision['standard_output'] is not None:
            return decision['standard_output']

        # 备用方案：传统输出格式
        output = [int(threat_data.threat_level)]  # 确保是Python int

        if decision['should_jam']:
            jamming_combination = decision['jamming_params'].get('combination', [])

            # 干扰数量（确保是Python int）
            output.append(int(len(jamming_combination)))

            # 为每个干扰类型添加编号、ID和对应参数
            for i, jamming_type in enumerate(jamming_combination):
                # 添加干扰编号（从0开始）
                output.append(int(i))

                # 添加干扰ID（确保是Python int）
                jamming_id = self._get_jamming_id(jamming_type)
                output.append(int(jamming_id))

                # 添加该干扰类型的特定参数
                params = self._get_jamming_specific_params(jamming_type, decision, threat_data)
                # 确保所有参数都是Python原生数值类型，并格式化精度
                converted_params = []
                for param in params:
                    if hasattr(param, 'item'):  # numpy类型
                        value = param.item()
                    else:
                        value = float(param)

                    # 格式化为合理精度（保留3位小数）
                    if isinstance(value, (int, float)):
                        if value == int(value):  # 整数
                            converted_params.append(int(value))
                        else:  # 浮点数，保留3位小数
                            converted_params.append(round(value, 3))
                    else:
                        converted_params.append(value)
                output.extend(converted_params)

            # 不再补充-1，有多少干扰就输出多少

        else:
            # 无干扰：只添加数量0（确保是Python int）
            output.append(int(0))

        return output

    def _get_jamming_id(self, jamming_type: str) -> int:
        """获取干扰类型ID"""
        for id_val, type_name in self.jamming_type_mapping.items():
            if type_name == jamming_type:
                return id_val
        return -1

    def _get_jamming_specific_params(self, jamming_type: str, decision: Dict, threat_data: ThreatAssessmentData) -> List:
        """获取特定干扰类型的参数"""
        if jamming_type == "梳状谱":
            return self._get_comb_spectrum_params(decision, threat_data)
        elif jamming_type == "间歇采样转发":
            return self._get_intermittent_sampling_params(decision, threat_data)
        elif jamming_type == "宽带阻塞噪声":
            return self._get_broadband_noise_params(decision, threat_data)
        elif jamming_type == "灵巧噪声":
            return self._get_smart_noise_params(decision, threat_data)
        elif jamming_type == "拖引":
            return self._get_deception_params(decision, threat_data)
        else:
            return []

    def _get_comb_spectrum_params(self, decision: Dict, threat_data: ThreatAssessmentData) -> List:
        """梳状谱参数：个数 + count个频偏序列(kHz) + count个闪烁周期序列(μs) + count个闪烁保持时间序列(μs)"""
        threat_level = threat_data.threat_level

        # 梳状谱个数：威胁越高，个数越多
        count = min(8, max(1, 6 - threat_level))

        params = [count]

        # 获取当前雷达频率进行同步跟踪 - 转换为Hz
        current_radar_freq_mhz = decision.get('current_radar_frequency', 10500.0)  # 默认10.5GHz = 10500MHz
        current_radar_freq_hz = current_radar_freq_mhz * 1e6  # MHz -> Hz

        # 频偏序列 (kHz) - 只生成count个
        frequency_offsets = self._calculate_frequency_offsets(current_radar_freq_hz, count)
        params.extend(frequency_offsets)

        # 闪烁周期序列 (μs) - 只生成count个
        base_period = 20.0  # 基础周期20μs
        for i in range(count):
            period = base_period + i * 5.0
            params.append(period)

        # 闪烁保持时间序列 (μs) - 只生成count个
        base_hold_time = 15.0  # 基础保持时间15μs
        for i in range(count):
            hold_time = base_hold_time + i * 3.0
            params.append(hold_time)

        return params

    def _calculate_frequency_offsets(self, radar_freq_hz: float, count: int) -> List[float]:
        """
        基于雷达载频计算频率偏移序列

        Args:
            radar_freq_hz: 雷达载频 (Hz)
            count: 需要的频偏数量

        Returns:
            频偏序列 (kHz)
        """
        radar_freq_ghz = radar_freq_hz / 1e9

        # 根据雷达频段选择合适的基础频偏
        if radar_freq_ghz < 2.0:  # L波段 (1-2 GHz)
            base_offset = 50.0   # 50kHz
        elif radar_freq_ghz < 8.0:  # S/C波段 (2-8 GHz)
            base_offset = 100.0  # 100kHz
        elif radar_freq_ghz < 18.0:  # X/Ku波段 (8-18 GHz)
            base_offset = 200.0  # 200kHz
        else:  # Ka波段及以上 (>18 GHz)
            base_offset = 500.0  # 500kHz

        offsets = []
        for i in range(count):
            # 生成对称的频偏：±base_offset, ±2*base_offset, ±3*base_offset...
            if i % 2 == 0:
                # 正偏移：+50, +100, +150...
                offset = base_offset * (i // 2 + 1)
            else:
                # 负偏移：-50, -100, -150...
                offset = -base_offset * (i // 2 + 1)
            offsets.append(offset)

        return offsets

    def _get_intermittent_sampling_params(self, decision: Dict, threat_data: ThreatAssessmentData) -> List:
        """间歇采样转发参数：重复转发时间间隔+间歇采样（切片）开关+间歇采样（切片）周期+间歇采样（切片）宽度+干扰覆盖距离范围+脉冲采样长度"""
        threat_level = threat_data.threat_level
        power = decision['jamming_power']

        # 根据威胁等级调整参数
        repeat_interval = 125.0 / threat_level  # 重复转发时间间隔(μs)
        sampling_switch = 1 if power > 0.5 else 0  # 间歇采样（切片）开关 (0:off, 1:on)
        sampling_period = 1.5 * power  # 间歇采样（切片）周期(μs)
        sampling_width = 0.8 + 0.4 * power  # 间歇采样（切片）宽度(μs)
        coverage_range = 60000.0 - threat_level * 10000  # 干扰覆盖距离范围(m)
        pulse_length = 1.2 * power  # 脉冲采样长度(μs)

        return [repeat_interval, sampling_switch, sampling_period, sampling_width, coverage_range, pulse_length]

    def _get_broadband_noise_params(self, decision: Dict, threat_data) -> List:
        """宽带阻塞噪声参数：带宽选择(0-20)"""
        threat_level = threat_data.threat_level

        # 威胁等级越高，选择更大的带宽
        # 带宽选择表：0=DC, 1=1MHz, 2=2MHz, ..., 20=1000MHz
        bandwidth_selection = min(20, max(0, (6 - threat_level) * 4))

        return [bandwidth_selection]

    def _get_smart_noise_params(self, decision: Dict, threat_data: ThreatAssessmentData) -> List:
        """灵巧噪声参数：噪声带宽选择+噪声源选择+多普勒闪烁模式+闪烁保持时间+闪烁消失时间+多普勒噪声带宽+多普勒噪声跳变周期"""
        threat_level = threat_data.threat_level
        power = decision['jamming_power']

        noise_bandwidth = min(20, max(0, (6 - threat_level) * 3))  # 噪声带宽选择(0-20)
        noise_source = 1 if threat_level >= 3 else (2 if threat_level >= 2 else 3)  # 噪声源选择(1:高斯噪声, 2:多普勒闪烁, 3:多普勒噪声)
        flicker_mode = 2 if threat_level <= 3 else 1  # 多普勒闪烁模式(1:固定闪烁, 2:随机闪烁)
        flicker_hold_time = 10.0 * power  # 闪烁保持时间(μs)
        flicker_disappear_time = 1.0 + power  # 闪烁消失时间(μs)
        doppler_noise_bandwidth = 50.0 * power  # 多普勒噪声带宽(kHz)
        doppler_jump_period = 10.0 * power  # 多普勒噪声跳变周期(kHz)

        return [noise_bandwidth, noise_source, flicker_mode, flicker_hold_time, flicker_disappear_time, doppler_noise_bandwidth, doppler_jump_period]

    def _get_deception_params(self, decision: Dict, threat_data: ThreatAssessmentData) -> List:
        """拖引参数：速拖速度+速拖加速度+距拖速度+距拖加速度+捕获时间+拖引时间+保持时间+消失时间"""
        threat_level = threat_data.threat_level
        power = decision['jamming_power']

        velocity_drag_speed = 450.0 + (5 - threat_level) * 50  # 速拖速度(m/s)
        velocity_drag_acceleration = 290.0 + power * 100  # 速拖加速度(m/s²)
        range_drag_speed = 20.0 * power  # 距拖速度(m/s)
        range_drag_acceleration = 15.0 + power * 10  # 距拖加速度(m/s²)
        capture_time = 1.6 * power  # 捕获时间(s)
        drag_time = 3.5 + power  # 拖引时间(s)
        hold_time = 2.4 * power  # 保持时间(s)
        disappear_time = 0.8 + power * 0.5  # 消失时间(s)

        return [velocity_drag_speed, velocity_drag_acceleration, range_drag_speed, range_drag_acceleration, capture_time, drag_time, hold_time, disappear_time]

    def _update_training_stats(self, episode: int, cycle_result: Dict):
        """更新训练统计和训练曲线数据"""
        reward = cycle_result['reward']
        self.training_stats['episodes_completed'] = episode + 1
        self.training_stats['total_rewards'] += reward
        self.training_stats['average_reward'] = self.training_stats['total_rewards'] / (episode + 1)

        if reward > self.training_stats['best_reward']:
            self.training_stats['best_reward'] = reward

        # 更新PPO统计
        if hasattr(self, 'actor_critic_accelerator'):
            ppo_stats = self.actor_critic_accelerator.training_stats
            self.training_stats['memory_size'] = len(self.actor_critic_accelerator.memory)
            self.training_stats['actor_loss'] = ppo_stats.get('actor_loss', 0.0)
            self.training_stats['critic_loss'] = ppo_stats.get('critic_loss', 0.0)
        else:
            self.training_stats['memory_size'] = 0
            self.training_stats['actor_loss'] = 0.0
            self.training_stats['critic_loss'] = 0.0

        # 记录训练曲线数据
        self.training_curves['episode_rewards'].append(reward)
        self.training_curves['cumulative_rewards'].append(self.training_stats['total_rewards'])
        self.training_curves['memory_sizes'].append(self.training_stats.get('memory_size', 0))
        self.training_curves['exploration_rates'].append(0.0)  # PPO不使用epsilon探索

        # 计算移动平均奖励（窗口大小为100）
        window_size = min(100, len(self.training_curves['episode_rewards']))
        if window_size > 0:
            recent_rewards = self.training_curves['episode_rewards'][-window_size:]
            moving_avg = np.mean(recent_rewards)
            self.training_curves['moving_average_rewards'].append(moving_avg)
        else:
            self.training_curves['moving_average_rewards'].append(reward)

        # 记录威胁等级分布（如果有威胁数据）
        if 'threat_data' in cycle_result:
            threat_level = cycle_result['threat_data'].threat_level
            self.training_curves['threat_level_distributions'].append(threat_level)
        else:
            self.training_curves['threat_level_distributions'].append(3)  # 默认中等威胁

        # 记录决策准确性（简化指标）
        decision_accuracy = 1.0 if reward > 0.6 else 0.0
        self.training_curves['decision_accuracy'].append(decision_accuracy)

        # 动态调整探索率
        self._update_exploration_rate(episode)

    def _update_exploration_rate(self, episode: int):
        """动态更新探索率 - 修复版本，使用线性衰减确保单调递减"""
        try:
            # 使用基于训练进度的线性衰减，确保单调递减
            total_episodes = self.config.training_episodes
            progress = min(1.0, episode / total_episodes)

            # PPO算法不使用传统的探索率，而是通过熵系数控制探索
            # 这里保留兼容性，但实际上PPO的探索由熵系数控制
            initial_rate = 0.1  # 默认初始探索率
            min_rate = 0.01     # 默认最小探索率

            new_exploration = initial_rate - (initial_rate - min_rate) * progress

            # 确保不低于最小探索率
            new_exploration = max(min_rate, new_exploration)

            # 强制更新所有相关模块的探索率，避免被其他逻辑覆盖（已移除）
            pass  # decision_module已移除，PPO使用熵正则化控制探索

            # 如果使用GPU加速器，也强制更新其探索率（仅适用于DQN类型的加速器）
            # PPO算法使用熵正则化控制探索，不需要epsilon参数
            try:
                if (hasattr(self, 'actor_critic_accelerator') and
                    self.actor_critic_accelerator and
                    hasattr(self.actor_critic_accelerator, 'epsilon')):
                    self.actor_critic_accelerator.epsilon = new_exploration
            except AttributeError:
                # PPO算法不使用epsilon探索，这是正常的
                pass

            # 记录到训练曲线
            if hasattr(self, 'training_curves'):
                self.training_curves['exploration_rates'].append(new_exploration)

        except Exception as e:
            # 如果更新失败，记录错误但不影响训练
            if self.config.enable_detailed_log:
                print(f"警告: 探索率更新失败: {e}")

    def _display_training_progress(self, episode: int, total_episodes: int, rewards: List[float]):
        """显示美化的训练进度"""
        recent_rewards = rewards[-10:] if len(rewards) >= 10 else rewards
        avg_recent = np.mean(recent_rewards)
        max_reward = max(rewards) if rewards else 0

        progress = episode / total_episodes * 100

        # 进度条可视化
        bar_length = 30
        filled_length = int(bar_length * progress / 100)
        bar = '=' * filled_length + '-' * (bar_length - filled_length)

        # 奖励等级评估
        if avg_recent > 0.8:
            reward_status = "[优秀]"
        elif avg_recent > 0.5:
            reward_status = "[良好]"
        elif avg_recent > 0.3:
            reward_status = "[一般]"
        else:
            reward_status = "[较低]"

        # 趋势分析
        trend = "→"
        if len(rewards) >= 20:
            recent_avg = np.mean(rewards[-10:])
            previous_avg = np.mean(rewards[-20:-10])
            if recent_avg > previous_avg * 1.05:
                trend = "↗ 上升"
            elif recent_avg < previous_avg * 0.95:
                trend = "↘ 下降"
            else:
                trend = "→ 平稳"

        # 获取GPU训练统计（如果可用）
        gpu_stats = ""
        if hasattr(self, 'actor_critic_accelerator') and self.actor_critic_accelerator:
            memory_size = len(self.actor_critic_accelerator.memory) if hasattr(self.actor_critic_accelerator, 'memory') else 0
            gpu_stats = f" | 经验池: {memory_size}"

        print(f"\n>> 训练进度: {progress:5.1f}% |{bar}| {episode:4d}/{total_episodes}")
        print(f"   {reward_status} 近期平均: {avg_recent:.3f} | 最佳: {max_reward:.3f} | {trend}{gpu_stats}")
        print("   " + "-" * 80)

    def _finalize_training(self, episodes: int, rewards: List[float], training_time: float) -> Dict:
        """完成训练统计并保存训练曲线"""
        final_stats = {
            'total_episodes': episodes,
            'total_training_time': training_time,
            'average_reward': np.mean(rewards),
            'best_reward': max(rewards),
            'final_memory_size': self.training_stats['memory_size'],
            'rewards_history': rewards,
            'training_curves': self.training_curves
        }

        # 美化训练完成显示
        print("\n" + "=" * 80)
        print(">> PPO智能训练完成！")
        print("=" * 80)

        # 计算训练效率
        episodes_per_second = episodes / training_time if training_time > 0 else 0

        # 奖励等级评估
        avg_reward = final_stats['average_reward']
        if avg_reward > 0.8:
            performance_level = "[优秀]"
        elif avg_reward > 0.6:
            performance_level = "[良好]"
        elif avg_reward > 0.4:
            performance_level = "[一般]"
        else:
            performance_level = "[需要改进]"

        print(f">> 训练统计:")
        print(f"   总轮次: {episodes:,}")
        print(f"   训练时间: {training_time:.1f}秒 ({training_time/60:.1f}分钟)")
        print(f"   训练效率: {episodes_per_second:.1f} 轮/秒")
        print(f"   {performance_level} 平均奖励: {avg_reward:.3f}")
        print(f"   最佳奖励: {final_stats['best_reward']:.3f}")
        print(f"   最终经验池: {final_stats['final_memory_size']}")
        print("=" * 80)

        # 保存训练曲线图（暂时禁用以避免matplotlib兼容性问题）
        # self._save_training_curves(episodes)

        return final_stats

    def _save_training_curves(self, episodes: int):
        """保存训练曲线图 - 修复数据长度不匹配问题"""
        try:
            # 设置matplotlib支持中文
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建子图
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('智能干扰系统训练曲线', fontsize=16, fontweight='bold')

            # 修复：确保所有数据长度一致
            base_length = len(self.training_curves['episode_rewards'])
            episodes_range = range(1, base_length + 1)

            # 截断或填充其他数据到相同长度
            def ensure_length(data_list, target_length):
                if len(data_list) > target_length:
                    return data_list[:target_length]
                elif len(data_list) < target_length:
                    # 用最后一个值填充
                    last_value = data_list[-1] if data_list else 0
                    return data_list + [last_value] * (target_length - len(data_list))
                return data_list

            # 1. 奖励曲线 - 使用平滑曲线显示
            rewards = self.training_curves['episode_rewards']

            # 计算移动平均奖励（多个窗口大小）
            window_sizes = [50, 200, 500]
            colors = ['lightblue', 'blue', 'darkblue']
            alphas = [0.6, 0.8, 1.0]

            for i, (window_size, color, alpha) in enumerate(zip(window_sizes, colors, alphas)):
                if len(rewards) >= window_size:
                    moving_avg = []
                    for j in range(len(rewards)):
                        start_idx = max(0, j - window_size + 1)
                        window_data = rewards[start_idx:j+1]
                        moving_avg.append(np.mean(window_data))

                    # 下采样显示点，避免过于密集
                    if len(moving_avg) > 1000:
                        step = len(moving_avg) // 1000
                        sample_episodes = episodes_range[::step]
                        sample_rewards = moving_avg[::step]
                    else:
                        sample_episodes = episodes_range
                        sample_rewards = moving_avg

                    axes[0, 0].plot(sample_episodes, sample_rewards,
                                  color=color, linewidth=2, alpha=alpha,
                                  label=f'{window_size}轮移动平均', marker='o', markersize=1)

            # 添加最终趋势线（线性拟合最后20%的数据）
            if len(rewards) > 100:
                last_20_percent = int(len(rewards) * 0.2)
                recent_episodes = episodes_range[-last_20_percent:]
                recent_rewards = rewards[-last_20_percent:]

                # 线性拟合
                z = np.polyfit(recent_episodes, recent_rewards, 1)
                p = np.poly1d(z)
                trend_line = p(recent_episodes)
                axes[0, 0].plot(recent_episodes, trend_line,
                              color='red', linewidth=3, linestyle='--',
                              label=f'最终趋势线 (斜率: {z[0]:.6f})')

            axes[0, 0].set_title('奖励收敛曲线 (平滑显示)')
            axes[0, 0].set_xlabel('训练轮次')
            axes[0, 0].set_ylabel('奖励值')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].set_ylim(-0.1, 1.1)  # 限制Y轴范围

            # 2. 累积奖励
            cumulative_rewards = ensure_length(self.training_curves['cumulative_rewards'], base_length)
            axes[0, 1].plot(episodes_range, cumulative_rewards, color='green', linewidth=2)
            axes[0, 1].set_title('累积奖励')
            axes[0, 1].set_xlabel('训练轮次')
            axes[0, 1].set_ylabel('累积奖励值')
            axes[0, 1].grid(True, alpha=0.3)

            # 3. 训练损失变化（PPO专用）
            if hasattr(self, 'actor_critic_accelerator') and hasattr(self.actor_critic_accelerator, 'training_losses'):
                losses = ensure_length(self.actor_critic_accelerator.training_losses, base_length)
                axes[0, 2].plot(episodes_range, losses, color='red', linewidth=2)
                axes[0, 2].set_title('训练损失')
                axes[0, 2].set_xlabel('训练轮次')
                axes[0, 2].set_ylabel('损失值')
            else:
                # 如果没有损失数据，显示奖励方差
                reward_variance = []
                window_size = 50
                for i in range(len(rewards)):
                    start_idx = max(0, i - window_size)
                    window_rewards = rewards[start_idx:i+1]
                    variance = np.var(window_rewards) if len(window_rewards) > 1 else 0
                    reward_variance.append(variance)

                variance_data = ensure_length(reward_variance, base_length)
                axes[0, 2].plot(episodes_range, variance_data, color='red', linewidth=2)
                axes[0, 2].set_title('奖励方差')
                axes[0, 2].set_xlabel('训练轮次')
                axes[0, 2].set_ylabel('方差值')
            axes[0, 2].grid(True, alpha=0.3)

            # 4. 探索率变化
            exploration_rates = ensure_length(self.training_curves['exploration_rates'], base_length)
            if any(rate > 0 for rate in exploration_rates):
                axes[1, 0].plot(episodes_range, exploration_rates, color='orange', linewidth=2)
                axes[1, 0].set_title('探索率衰减')
                axes[1, 0].set_xlabel('训练轮次')
                axes[1, 0].set_ylabel('探索率')
                axes[1, 0].grid(True, alpha=0.3)
            else:
                axes[1, 0].text(0.5, 0.5, '无探索数据',
                               ha='center', va='center', transform=axes[1, 0].transAxes)
                axes[1, 0].set_title('探索率衰减')

            # 5. 威胁等级分布
            threat_levels = ensure_length(self.training_curves['threat_level_distributions'], base_length)
            if threat_levels:
                unique_levels, counts = np.unique(threat_levels, return_counts=True)
                axes[1, 1].bar(unique_levels, counts, color='purple', alpha=0.7)
                axes[1, 1].set_title('威胁等级分布')
                axes[1, 1].set_xlabel('威胁等级')
                axes[1, 1].set_ylabel('频次')
                axes[1, 1].grid(True, alpha=0.3)

            # 6. 决策准确性
            decision_accuracy = ensure_length(self.training_curves['decision_accuracy'], base_length)
            if len(decision_accuracy) > 10:
                # 计算滑动窗口准确率
                window_size = 50
                accuracy_smooth = []
                for i in range(len(decision_accuracy)):
                    start_idx = max(0, i - window_size + 1)
                    window_data = decision_accuracy[start_idx:i+1]
                    accuracy_smooth.append(np.mean(window_data))

                # 确保accuracy_smooth长度与episodes_range一致
                accuracy_smooth = ensure_length(accuracy_smooth, base_length)
                axes[1, 2].plot(episodes_range, accuracy_smooth, color='darkgreen', linewidth=2)
                axes[1, 2].set_title('决策准确率 (50轮滑动窗口)')
                axes[1, 2].set_xlabel('训练轮次')
                axes[1, 2].set_ylabel('准确率')
                axes[1, 2].set_ylim(0, 1)
                axes[1, 2].grid(True, alpha=0.3)
            else:
                axes[1, 2].text(0.5, 0.5, '数据不足',
                               ha='center', va='center', transform=axes[1, 2].transAxes)
                axes[1, 2].set_title('决策准确率')

            plt.tight_layout()

            # 保存图片
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"training_curves_{episodes}episodes_{timestamp}.png"
            filepath = os.path.join("models", filename)

            # 确保models目录存在
            os.makedirs("models", exist_ok=True)

            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"   训练曲线已保存: {filepath}")

        except Exception as e:
            print(f"   警告: 保存训练曲线失败: {e}")

    def _execute_test_cycle(self, scenario: Dict, test_id: int) -> Dict:
        """执行测试循环"""
        return self._execute_system_cycle(scenario, test_id, training_mode=False)

    def _explain_test_result(self, result: Dict, test_num: int):
        """解释测试结果"""
        decision = result['decision']
        threat_data = result['threat_data']
        effectiveness = result['effectiveness']

        print(f"   威胁评估: 等级{threat_data.threat_level} ({threat_data.threat_level_name})")

        if decision['should_jam']:
            combination = decision['jamming_params'].get('combination', [])

            # 干扰类型名称映射
            jamming_type_names = {
                0: "梳状谱干扰",
                1: "间歇采样转发干扰",
                2: "宽带阻塞噪声干扰",
                3: "灵巧噪声干扰",
                4: "拖引干扰"
            }

            # 将干扰类型编号转换为名称
            if combination:
                jamming_names = []
                for jtype in combination:
                    if isinstance(jtype, str):
                        # 如果已经是字符串（中文名称），直接使用
                        jamming_names.append(jtype)
                    else:
                        # 如果是数字ID，转换为名称
                        try:
                            jamming_names.append(jamming_type_names.get(int(jtype), f"未知类型{jtype}"))
                        except (ValueError, TypeError):
                            jamming_names.append(f"未知类型{jtype}")

                print(f"   决策: 执行干扰 ({' + '.join(jamming_names)})")
            else:
                print(f"   决策: 执行干扰 (类型{decision.get('jamming_type', 0)})")

            print(f"   功率等级: {decision['jamming_power']:.1f}")

            print(f"   干扰效果: {effectiveness['overall_effectiveness']:.2f}")
        else:
            print(f"   决策: 不执行干扰")

        # 生成并显示标准输出格式
        numerical_output = self._generate_numerical_output(result)
        print(f"   📋 标准输出: {numerical_output}")

        # 解释标准输出格式
        self._explain_numerical_output_brief(numerical_output)

        print(f"   决策置信度: {decision['confidence']:.2f}")
        print(f"   处理时间: {result['cycle_time']*1000:.1f}ms")

    def _explain_numerical_output_brief(self, numerical_output: List):
        """简要解释标准输出格式"""
        if len(numerical_output) < 2:
            return

        threat_level = numerical_output[0]
        jamming_count = numerical_output[1]

        print(f"   📊 格式解析: 威胁等级={threat_level}, 干扰数量={jamming_count}", end="")

        if jamming_count > 0:
            idx = 2
            jamming_info = []
            for j in range(jamming_count):
                if idx + 1 < len(numerical_output):
                    jamming_number = numerical_output[idx]
                    jamming_id = numerical_output[idx + 1]
                    jamming_info.append(f"干扰{jamming_number}(ID={jamming_id})")
                    idx += 2

                    # 跳过参数部分（简化显示）
                    if jamming_id == 0:  # 梳状谱
                        # 动态跳过：1个count + count个频偏 + count个周期 + count个保持时间
                        if idx < len(numerical_output):
                            count = int(numerical_output[idx])
                            idx += 1 + count * 3  # 1个count + count*3个参数
                        else:
                            idx += 1  # 至少跳过count
                    elif jamming_id == 1:  # 间歇采样
                        idx += 6
                    elif jamming_id == 2:  # 宽带阻塞
                        idx += 1
                    elif jamming_id == 3:  # 灵巧噪声
                        idx += 7
                    elif jamming_id == 4:  # 拖引
                        idx += 3

            if jamming_info:
                print(f", {', '.join(jamming_info)}")
            else:
                print()
        else:
            print()

    def _summarize_test_results(self, results: List[Dict]) -> Dict:
        """汇总测试结果"""
        total_tests = len(results)
        jamming_decisions = sum(1 for r in results if r['decision']['should_jam'])
        avg_effectiveness = np.mean([r['effectiveness']['overall_effectiveness']
                                   for r in results if r['decision']['should_jam']])
        avg_processing_time = np.mean([r['cycle_time'] for r in results])

        summary = {
            'total_tests': total_tests,
            'jamming_rate': jamming_decisions / total_tests,
            'average_effectiveness': avg_effectiveness if jamming_decisions > 0 else 0.0,
            'average_processing_time': avg_processing_time,
            'success_rate': 1.0  # 假设所有测试都成功
        }

        print(f"\n测试结果汇总:")
        print(f"   总测试数: {summary['total_tests']}")
        print(f"   干扰决策率: {summary['jamming_rate']:.1%}")
        print(f"   平均干扰效果: {summary['average_effectiveness']:.3f}")
        print(f"   平均处理时间: {summary['average_processing_time']*1000:.1f}ms")

        return summary

    def _explain_output(self, numerical_output: List, cycle_result: Dict):
        """解释输出格式 - 包含每个干扰类型的特定参数"""
        print(f"\n📋 标准化输出解释:")
        print(f"   数值输出: {numerical_output}")
        print(f"   格式说明:")
        print(f"     [0] 威胁等级: {numerical_output[0]} (1=极高, 5=极低)")

        if len(numerical_output) > 1:
            print(f"     [1] 干扰数量: {numerical_output[1]}")

            if numerical_output[1] > 0:
                self._explain_jamming_details(numerical_output, cycle_result)
            else:
                print(f"     无干扰，输出结束")

        # 显示详细决策信息
        decision = cycle_result['decision']
        print(f"\n   决策详情:")
        print(f"     决策依据: {decision['decision_reason']}")
        print(f"     置信度: {decision['confidence']:.2f}")
        if decision['should_jam']:
            combination = decision['jamming_params'].get('combination', [])
            # 确保组合中的元素都是字符串
            combination_str = [str(item) for item in combination]
            print(f"     干扰组合: {' + '.join(combination_str)}")

    def _explain_jamming_details(self, numerical_output: List, cycle_result: Dict):
        """解释干扰详细参数"""
        decision = cycle_result['decision']
        jamming_combination = decision['jamming_params'].get('combination', [])

        print(f"     干扰类型ID映射: 0=梳状谱, 1=间歇采样, 2=宽带噪声, 3=灵巧噪声, 4=拖引")

        idx = 2  # 从第2个位置开始解析

        for i, jamming_type in enumerate(jamming_combination):
            if idx >= len(numerical_output):
                break

            # 新格式：编号 + ID + 参数
            jamming_number = numerical_output[idx]  # 干扰编号（从0开始）
            idx += 1

            if idx >= len(numerical_output):
                break

            jamming_id = numerical_output[idx]  # 干扰类型ID
            print(f"\n     干扰{jamming_number}: {jamming_type} (编号={jamming_number}, ID={jamming_id})")
            idx += 1

            # 解释该干扰类型的参数
            if jamming_type == "梳状谱":
                idx = self._explain_comb_spectrum_params(numerical_output, idx)
            elif jamming_type == "间歇采样转发":
                idx = self._explain_intermittent_sampling_params(numerical_output, idx)
            elif jamming_type == "宽带阻塞噪声":
                idx = self._explain_broadband_noise_params(numerical_output, idx)
            elif jamming_type == "灵巧噪声":
                idx = self._explain_smart_noise_params(numerical_output, idx)
            elif jamming_type == "拖引":
                idx = self._explain_deception_params(numerical_output, idx)

    def _explain_comb_spectrum_params(self, output: List, start_idx: int) -> int:
        """解释梳状谱参数"""
        if start_idx >= len(output):
            return start_idx

        count = int(output[start_idx])
        print(f"       梳状谱个数: {count}")
        idx = start_idx + 1

        # 频偏序列 - 只读取count个
        print(f"       频偏(kHz): ", end="")
        for i in range(count):
            if idx < len(output):
                print(f"{output[idx]:.1f}", end=" ")
                idx += 1
        print()

        # 闪烁周期序列 - 只读取count个
        print(f"       闪烁周期(μs): ", end="")
        for i in range(count):
            if idx < len(output):
                print(f"{output[idx]:.1f}", end=" ")
                idx += 1
        print()

        # 闪烁保持时间序列 - 只读取count个
        print(f"       闪烁保持时间(μs): ", end="")
        for i in range(count):
            if idx < len(output):
                print(f"{output[idx]:.1f}", end=" ")
                idx += 1
        print()

        return idx  # 返回实际读取到的位置

    def _explain_intermittent_sampling_params(self, output: List, start_idx: int) -> int:
        """解释间歇采样转发参数"""
        params = [
            "重复转发时间间隔(μs)",
            "间歇采样（切片）开关",
            "间歇采样（切片）周期(μs)",
            "间歇采样（切片）宽度(μs)",
            "干扰覆盖距离范围(m)",
            "脉冲采样长度(μs)"
        ]

        for i, param_name in enumerate(params):
            if start_idx + i < len(output):
                value = output[start_idx + i]
                if i == 1:  # 开关参数
                    switch_desc = "on" if value == 1 else "off"
                    print(f"       {param_name}: {int(value)} ({switch_desc})")
                else:
                    print(f"       {param_name}: {value}")

        return start_idx + 6

    def _explain_broadband_noise_params(self, output: List, start_idx: int) -> int:
        """解释宽带阻塞噪声参数"""
        if start_idx < len(output):
            bandwidth_selection = int(output[start_idx])
            # 带宽映射表
            bandwidth_map = {
                0: "DC", 1: "1MHz", 2: "2MHz", 3: "5MHz", 4: "10MHz", 5: "20MHz",
                6: "50MHz", 7: "100MHz", 8: "200MHz", 9: "300MHz", 10: "400MHz",
                11: "500MHz", 12: "600MHz", 13: "700MHz", 14: "800MHz", 15: "900MHz",
                16: "950MHz", 17: "980MHz", 18: "990MHz", 19: "995MHz", 20: "1000MHz"
            }
            bandwidth_desc = bandwidth_map.get(bandwidth_selection, f"{bandwidth_selection}")
            print(f"       带宽选择: {bandwidth_selection} ({bandwidth_desc})")

        return start_idx + 1

    def _explain_smart_noise_params(self, output: List, start_idx: int) -> int:
        """解释灵巧噪声参数"""
        params = [
            "噪声带宽选择",
            "噪声源选择",
            "多普勒闪烁模式",
            "闪烁保持时间(μs)",
            "闪烁消失时间(μs)",
            "多普勒噪声带宽(kHz)",
            "多普勒噪声跳变周期(kHz)"
        ]

        for i, param_name in enumerate(params):
            if start_idx + i < len(output):
                value = output[start_idx + i]
                if i == 1:  # 噪声源选择
                    source_desc = {1: "高斯噪声", 2: "多普勒闪烁", 3: "多普勒噪声"}.get(int(value), f"未知({int(value)})")
                    print(f"       {param_name}: {int(value)} ({source_desc})")
                elif i == 2:  # 多普勒闪烁模式
                    mode_desc = {1: "固定闪烁", 2: "随机闪烁"}.get(int(value), f"未知({int(value)})")
                    print(f"       {param_name}: {int(value)} ({mode_desc})")
                else:
                    print(f"       {param_name}: {value}")

        return start_idx + 7

    def _explain_deception_params(self, output: List, start_idx: int) -> int:
        """解释拖引参数"""
        params = [
            "速拖速度(m/s)",
            "速拖加速度(m/s²)",
            "距拖速度(m/s)",
            "距拖加速度(m/s²)",
            "捕获时间(s)",
            "拖引时间(s)",
            "保持时间(s)",
            "消失时间(s)"
        ]

        for i, param_name in enumerate(params):
            if start_idx + i < len(output):
                value = output[start_idx + i]
                print(f"       {param_name}: {value}")

        return start_idx + 8

    def _save_model(self, model_path: str):
        """保存模型"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(model_path), exist_ok=True)

            # 保存PPO模型
            if hasattr(self, 'actor_critic_accelerator'):
                ppo_model_path = model_path.replace('.pkl', '_ppo.pth')
                self.actor_critic_accelerator.save_model(ppo_model_path)
                print(f"PPO模型已保存: {ppo_model_path}")

                # 创建主模型文件的符号链接或复制
                import shutil
                shutil.copy2(ppo_model_path, model_path.replace('.pkl', '.pth'))
                print(f"主模型文件已保存: {model_path.replace('.pkl', '.pth')}")
            else:
                print("PPO加速器未初始化，无法保存模型")

        except Exception as e:
            print(f"保存模型异常: {e}")




























if __name__ == "__main__":
    main()
