function weights = shangquan_weights(data)
% 熵权法
% 输入:
%   data - 样本数据矩阵
% 输出:
%   weights - 各指标的权重向量

[m, n] = size(data);  

normalized_data = zeros(m, n);
for j = 1:n
    min_val = min(data(:, j));
    max_val = max(data(:, j));
    if max_val == min_val
        normalized_data(:, j) = ones(m, 1);
    else
        normalized_data(:, j) = (data(:, j) - min_val) / (max_val - min_val);
    end
end

% 计算指标比重
P = zeros(m, n);
for j = 1:n
    sum_col = sum(normalized_data(:, j));
    if sum_col == 0
        P(:, j) = ones(m, 1) / m; 
    else
        P(:, j) = normalized_data(:, j) / sum_col;
    end
end

% 计算熵值
e = zeros(1, n);
k = 1 / log(m);
for j = 1:n
    P_nonzero = P(:, j);
    P_nonzero(P_nonzero == 0) = 1e-10;
    e(j) = -k * sum(P_nonzero .* log(P_nonzero));
end

% 计算信息熵冗余度
d = 1 - e;  

%计算各指标权重
if sum(d) == 0
    weights = ones(1, n) / n; 
else
    weights = d / sum(d);
end

end
