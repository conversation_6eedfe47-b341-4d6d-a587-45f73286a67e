# 雷达干扰决策系统 - ONNX版本

这是一个基于ONNX Runtime的雷达干扰决策系统，可以在非RK3588平台上运行，与原RK3588版本具有相同的逻辑和输出格式。

## 特性

- ✅ **跨平台支持**: 支持Windows、Linux、macOS等多种平台
- ✅ **ONNX Runtime**: 使用ONNX Runtime进行模型推理
- ✅ **模拟模式**: 当ONNX Runtime不可用时，自动切换到模拟模式
- ✅ **标准输出格式**: 与RK3588版本完全一致的输出格式
- ✅ **威胁等级4逻辑**: 支持静默模式判断和工作模式区分
- ✅ **5种干扰类型**: 完整支持梳状谱、间歇采样、宽带阻塞、灵巧噪声、拖引
- ✅ **C API**: 提供简洁的C语言API接口

## 系统架构

```
cpp_onnx_inference/
├── include/                 # 头文件
│   ├── data_structures.h   # 数据结构定义
│   ├── external_api.h      # 外部API接口
│   ├── onnx_inference.h    # ONNX推理引擎
│   ├── jamming_decision.h  # 决策引擎
│   └── threat_evaluator.h  # 威胁评估器
├── src/                    # 源文件
│   ├── external_api.cpp    # 外部API实现
│   ├── onnx_inference.cpp  # ONNX推理实现
│   ├── jamming_decision.cpp # 决策引擎实现
│   ├── data_structures.cpp # 数据结构实现
│   └── threat_evaluator.cpp # 威胁评估实现
├── models/                 # 模型文件目录
│   └── jamming_model.onnx  # ONNX模型文件
├── third_party/           # 第三方库
│   └── onnxruntime/       # ONNX Runtime库
├── example_onnx_usage.cpp # 示例程序
├── CMakeLists.txt         # CMake构建文件
└── README.md              # 本文件
```

## 依赖要求

### 必需依赖
- **CMake** >= 3.10
- **C++14** 编译器 (GCC 5+, Clang 3.4+, MSVC 2017+)

### 可选依赖
- **ONNX Runtime** >= 1.8.0 (推荐使用最新版本)
  - 如果没有ONNX Runtime，系统会自动切换到模拟模式

## 安装和编译

### 1. 下载ONNX Runtime (可选)

如果要使用真实的ONNX模型推理，需要下载ONNX Runtime：

```bash
# 下载ONNX Runtime
wget https://github.com/microsoft/onnxruntime/releases/download/v1.16.3/onnxruntime-linux-x64-1.16.3.tgz
tar -xzf onnxruntime-linux-x64-1.16.3.tgz

# 创建third_party目录并复制
mkdir -p third_party
mv onnxruntime-linux-x64-1.16.3 third_party/onnxruntime
```

### 2. 编译项目

```bash
# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake ..

# 编译
make -j4

# 或者在Windows上使用
# cmake --build . --config Release
```

### 3. 运行示例

```bash
# 运行示例程序
./bin/example_onnx_usage

# 或者在Windows上
# bin\example_onnx_usage.exe
```

## API使用说明

### 基本使用流程

```cpp
#include "external_api.h"

int main() {
    // 1. 初始化系统
    DecisionEngineHandle handle = radar_jamming_init("models/jamming_model.onnx");
    if (!handle) {
        printf("初始化失败\n");
        return -1;
    }
    
    // 2. 设置雷达参数
    ExternalRadarParams radar = {
        10500.0,        // 频率 (MHz)
        1.0,            // 脉宽 (μs)
        100.0,          // PRT (μs)
        5000000.0,      // 功率 (W)
        30.0,           // 距离 (km)
        400.0,          // 速度 (m/s)
        45.0,           // 方向 (度)
        RADAR_MODE_GUIDANCE  // 工作模式
    };
    
    // 3. 执行决策
    ExternalDecisionResult result;
    int ret = radar_jamming_decide(handle, &radar, &result);
    
    if (ret == RADAR_SUCCESS && result.success) {
        printf("威胁等级: %d\n", result.threat_level);
        printf("干扰数量: %d\n", result.jamming_count);
        
        // 使用标准输出
        if (result.standard_output) {
            // 发送给硬件接口
            send_to_hardware(result.standard_output, result.output_length);
            
            // 释放内存
            radar_jamming_free_output(result.standard_output);
        }
    }
    
    // 4. 清理资源
    radar_jamming_destroy(handle);
    return 0;
}
```

### 主要API函数

| 函数 | 功能 | 说明 |
|------|------|------|
| `radar_jamming_init()` | 初始化系统 | 加载ONNX模型，创建决策引擎 |
| `radar_jamming_decide()` | 执行完整决策 | 威胁评估 + 模型推理 + 后处理 |
| `radar_threat_evaluate()` | 仅威胁评估 | 只进行威胁等级评估 |
| `radar_jamming_inference()` | 仅模型推理 | 给定威胁等级，进行干扰决策 |
| `radar_get_statistics()` | 获取统计信息 | 处理次数、成功率、平均时间 |
| `radar_jamming_destroy()` | 销毁系统 | 释放所有资源 |

## 标准输出格式

新的标准输出格式与RK3588版本完全一致：

```
[威胁等级, 干扰数量, 干扰1编号, 干扰1类型ID, 干扰1参数..., 干扰2编号, 干扰2类型ID, 干扰2参数...]
```

### 示例输出

```cpp
// 威胁等级1 + 梳状谱干扰
[1, 1, 0, 0, 5, 200, -400, 600, -800, 1000, 20, 25, 30, 35, 40, 15, 18, 21, 24, 27]

// 威胁等级4 + 静默模式
[4, 0]

// 威胁等级3 + 双重干扰
[3, 2, 0, 1, 125.0, 1, 1.5, 0.8, 50000.0, 2.0, 1, 2, 8]
```

## 干扰类型支持

| ID | 干扰类型 | 参数数量 | 说明 |
|----|----------|----------|------|
| 0 | 梳状谱 | 1+count*3 | 动态参数：个数+频偏+周期+保持时间 |
| 1 | 间歇采样转发 | 6 | 转发间隔、开关、周期、宽度、距离、长度 |
| 2 | 宽带阻塞噪声 | 1 | 带宽选择 |
| 3 | 灵巧噪声 | 7 | 类型、带宽、功率、调制深度、频率、相位、幅度 |
| 4 | 拖引 | 3 | 距离、速度、加速度 |

## 威胁等级4特殊逻辑

系统实现了与RK3588版本相同的威胁等级4逻辑：

- **静默模式 (work_mode=0)**: 不执行干扰
- **非静默模式 (work_mode=1-4)**: 必须选择一种干扰

## 模拟模式

当ONNX Runtime不可用时，系统自动切换到模拟模式：

- ✅ 提供合理的模拟输出
- ✅ 保持相同的API接口
- ✅ 支持所有功能测试
- ✅ 便于开发和调试

## 性能特点

- **推理速度**: 通常 < 5ms (取决于硬件)
- **内存占用**: 约 50MB (包含模型)
- **CPU使用**: 支持多线程推理
- **GPU加速**: 支持CUDA/OpenCL (需要相应版本的ONNX Runtime)

## 故障排除

### 常见问题

1. **编译错误**: 确保C++14支持和CMake版本
2. **模型加载失败**: 检查模型文件路径和格式
3. **推理错误**: 查看错误日志，可能切换到模拟模式
4. **内存泄漏**: 确保调用相应的释放函数

### 调试建议

```cpp
// 启用详细日志
radar_set_config(handle, 1, 1, 0.7);

// 检查统计信息
int total;
double success_rate, avg_time;
radar_get_statistics(handle, &total, &success_rate, &avg_time);
printf("处理%d次, 成功率%.1f%%, 平均%.2fms\n", total, success_rate*100, avg_time);
```

## 与RK3588版本的对比

| 特性 | RK3588版本 | ONNX版本 |
|------|------------|----------|
| 平台支持 | 仅RK3588 | 跨平台 |
| 推理引擎 | RKNN | ONNX Runtime |
| 输出格式 | ✅ | ✅ 完全一致 |
| 威胁等级4逻辑 | ✅ | ✅ 完全一致 |
| 5种干扰类型 | ✅ | ✅ 完全一致 |
| 性能 | 硬件加速 | CPU/GPU可选 |
| 部署难度 | 硬件绑定 | 灵活部署 |

## 许可证

本项目采用与原项目相同的许可证。
