"""
统一干扰信号生成器 - 简化版本
根据智能决策模块的输出参数，生成对应的干扰信号
"""

import numpy as np
from typing import Tuple, Dict, List, Union


class JammingSignalGenerator:
    """统一干扰信号生成器 - 简化版本"""

    def __init__(self):
        # 干扰类型映射
        self.jamming_types = {
            0: "梳状谱",
            1: "间歇采样转发", 
            2: "宽带阻塞噪声",
            3: "灵巧噪声",
            4: "拖引"
        }
        
    def generate_simple_jamming_signal(self, jamming_type: int, params: Dict, 
                                     fs: float = 1e9, duration: float = 1e-3) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成简化的干扰信号
        
        Args:
            jamming_type: 干扰类型 (0-4)
            params: 干扰参数字典
            fs: 采样频率 (Hz)
            duration: 信号持续时间 (s)
            
        Returns:
            (时间轴, 干扰信号)
        """
        t = np.linspace(0, duration, int(fs * duration))
        
        if jamming_type == 0:  # 梳状谱
            signal_data = self._generate_simple_comb(t, params)
        elif jamming_type == 1:  # 间歇采样转发
            signal_data = self._generate_simple_isrj(t, params)
        elif jamming_type == 2:  # 宽带阻塞噪声
            signal_data = self._generate_simple_noise(t, params)
        elif jamming_type == 3:  # 灵巧噪声
            signal_data = self._generate_simple_smart_noise(t, params)
        elif jamming_type == 4:  # 拖引
            signal_data = self._generate_simple_deception(t, params)
        else:
            signal_data = np.zeros_like(t, dtype=complex)
            
        return t, signal_data
    
    def generate_jamming_signals(self, decision_output: Union[str, List], radar_signal: np.ndarray,
                               radar_params: Dict, fs: float = 1e9, duration: float = 1e-3) -> Dict:
        """
        根据决策输出生成干扰信号 - 简化版本

        Args:
            decision_output: 标准化决策输出（字符串或列表格式）
            radar_signal: 雷达发射信号
            radar_params: 雷达参数字典
            fs: 采样频率(Hz)
            duration: 信号持续时间(s)

        Returns:
            包含所有干扰信号的字典
        """
        # 简化处理：直接返回基本干扰信号
        t = np.linspace(0, duration, int(fs * duration))
        
        # 生成基本噪声干扰
        noise_signal = np.random.randn(len(t)) + 1j * np.random.randn(len(t))
        
        return {
            'jamming_signals': [noise_signal],
            'jamming_types': ['宽带噪声'],
            'total_power': 1.0,
            'time_axis': t
        }
    
    def _generate_simple_comb(self, t: np.ndarray, params: Dict) -> np.ndarray:
        """生成简化梳状谱信号"""
        f0 = params.get('frequency', 10e9)
        amplitude = params.get('amplitude', 1.0)
        return amplitude * np.exp(1j * 2 * np.pi * f0 * t)
    
    def _generate_simple_isrj(self, t: np.ndarray, params: Dict) -> np.ndarray:
        """生成简化间歇采样转发信号"""
        f0 = params.get('frequency', 10e9)
        amplitude = params.get('amplitude', 1.0)
        return amplitude * np.exp(1j * 2 * np.pi * f0 * t)
    
    def _generate_simple_noise(self, t: np.ndarray, params: Dict) -> np.ndarray:
        """生成简化宽带噪声信号"""
        amplitude = params.get('amplitude', 1.0)
        return amplitude * (np.random.randn(len(t)) + 1j * np.random.randn(len(t)))
    
    def _generate_simple_smart_noise(self, t: np.ndarray, params: Dict) -> np.ndarray:
        """生成简化灵巧噪声信号"""
        f0 = params.get('frequency', 10e9)
        amplitude = params.get('amplitude', 1.0)
        noise = np.random.randn(len(t)) + 1j * np.random.randn(len(t))
        carrier = np.exp(1j * 2 * np.pi * f0 * t)
        return amplitude * noise * carrier
    
    def _generate_simple_deception(self, t: np.ndarray, params: Dict) -> np.ndarray:
        """生成简化拖引信号"""
        f0 = params.get('frequency', 10e9)
        amplitude = params.get('amplitude', 1.0)
        delay = params.get('delay', 1e-6)
        delayed_t = t - delay
        delayed_t[delayed_t < 0] = 0
        return amplitude * np.exp(1j * 2 * np.pi * f0 * delayed_t)
