@echo off
setlocal

echo Building Radar Jamming ONNX Version...

REM 设置代码页为GBK
chcp 936

REM Setup VS2015 environment
call "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" x64
if errorlevel 1 (
    echo Error: Cannot find Visual Studio 2015
    pause
    exit /b 1
)

echo Visual Studio 2015 environment setup successful

REM Check for ONNX Runtime
set ONNX_INCLUDE=
set ONNX_LIB=
set ONNX_DEFINES=

if exist "third_party\onnxruntime\include\onnxruntime_cxx_api.h" (
    echo Found ONNX Runtime, enabling real inference...
    set ONNX_INCLUDE=/I"..\third_party\onnxruntime\include"
    set ONNX_LIB="..\third_party\onnxruntime\lib\onnxruntime.lib"
    set ONNX_DEFINES=/DHAS_ONNXRUNTIME=1

    echo Copying ONNX Runtime DLLs...
) else (
    echo ONNX Runtime not found, using simulation mode...
    set ONNX_DEFINES=/DHAS_ONNXRUNTIME=0
)

REM Create build directory
if exist build rmdir /s /q build
mkdir build
cd build

REM Compile
echo Compiling...
cl /EHsc /MD /O2 /W3 /I..\include %ONNX_INCLUDE% /DONNX_SIMULATION_MODE=1 /D_CRT_SECURE_NO_WARNINGS /DWIN32 /DNDEBUG /D_CONSOLE %ONNX_DEFINES% ..\example_onnx_usage.cpp ..\src\external_api.cpp ..\src\data_structures.cpp ..\src\jamming_decision.cpp ..\src\onnx_inference.cpp ..\src\threat_evaluator.cpp /Fe:RadarJammingONNX.exe /link /SUBSYSTEM:CONSOLE %ONNX_LIB%

if errorlevel 1 (
    echo Compilation failed
    pause
    exit /b 1
)

if exist "RadarJammingONNX.exe" (
    REM Copy ONNX Runtime DLLs to build directory
    if exist "..\third_party\onnxruntime\lib\onnxruntime.dll" (
        copy "..\third_party\onnxruntime\lib\onnxruntime.dll" .
        copy "..\third_party\onnxruntime\lib\onnxruntime_providers_shared.dll" .
    )

    echo.
    echo Build successful!
    echo Executable: build\RadarJammingONNX.exe
    echo.
    echo Running test...
    RadarJammingONNX.exe
    echo.
    echo Test completed
) else (
    echo Build failed - executable not found
    pause
    exit /b 1
)

echo Build completed successfully!
pause
