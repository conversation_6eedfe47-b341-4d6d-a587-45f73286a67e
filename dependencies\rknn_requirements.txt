# RKNN模型转换和部署依赖包
# 用于ONNX模型转换为RKNN格式和瑞芯微NPU部署

# 核心RKNN工具包 (需要手动下载)
# rknn-toolkit2>=1.4.0  # 瑞芯微官方工具包
# rknn-toolkit-lite2>=1.4.0  # 轻量版工具包

# ONNX相关(用于输入模型)
onnx>=1.10.0
onnxruntime>=1.8.0

# 数值计算
numpy>=1.19.0

# 图像处理
opencv-python>=4.5.0
Pillow>=8.0.0

# 数据处理
pandas>=1.2.0

# 配置文件
pyyaml>=5.4.0

# 模型量化 (包含在onnxruntime中)
# onnx-quantization

# 性能分析
psutil>=5.8.0

# 进度显示
tqdm>=4.60.0

# 日志记录
loguru>=0.5.0

# 类型提示
typing-extensions>=3.7.0

# 数据结构
dataclasses

# 注意: 以下是Python内置模块，无需安装
# typing, argparse, pathlib, os, sys, time, math, random
# collections, shutil, glob, re, warnings, gc, copy
# functools, itertools, pickle, json, threading, concurrent.futures
# multiprocessing, queue, subprocess, signal, platform, socket
# datetime, string, base64, urllib, hashlib, tempfile
# zipfile, tarfile, csv, traceback, unittest

# 环境变量
python-dotenv>=0.17.0

# HTTP请求
requests>=2.25.0

# 数据验证
cerberus>=1.3.0

# 开发工具
pytest>=6.0.0
pytest-cov>=2.12.0

# 代码格式化
black>=21.0.0
isort>=5.9.0

# 静态分析
flake8>=3.9.0
mypy>=0.910

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 性能监控
nvidia-ml-py3>=7.352.0  # GPU监控
py3nvml>=0.2.6

# 内存分析
memory-profiler>=0.58.0

# 模型可视化
netron>=5.0.0

# 数据可视化
matplotlib>=3.3.0

# 科学计算
scipy>=1.6.0

# 机器学习
scikit-learn>=0.24.0

# 统计分析
statsmodels>=0.12.0

# 优化算法
optuna>=2.7.0

# 并行计算
joblib>=1.0.0

# 数据存储
h5py>=3.1.0

# 配置管理
hydra-core>=1.1.0

# 实验跟踪
wandb>=0.12.0  # 可选
tensorboard>=2.5.0  # 可选

# API服务
fastapi>=0.65.0  # 可选
uvicorn>=0.14.0  # 可选

# 加密
cryptography>=3.4.0

# 压缩算法
lz4>=3.1.0
zstandard>=0.15.0

# 注意: 以下是Python内置模块，无需安装
# mmap, struct, array, bytes, bytearray, codecs, locale
# ctypes, importlib, pkgutil, resource, gc, weakref
# contextlib, abc, enum, dataclasses, types, inspect
# pdb, code, frame, trace, profile, cProfile, pstats
# cmake, ninja, setuptools, wheel, docker, gitpython
