﻿#ifndef RKNN_INFERENCE_H
#define RKNN_INFERENCE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "threat_evaluator.h"

/**
 * RKNN推理模块
 * 负责加载RKNN模型并执行推理
 */

// RKNN推理结果数据结构
typedef struct {
    // 推理输出
    float threat_level_probs[5];    // 威胁等级概率分布
    float jamming_type_probs[5];    // 干扰类型概率分布
    
    // 推理统计
    double inference_time_ms;       // 推理耗时 (毫秒)
    int success;                    // 推理是否成功
    
    // 预测结果
    int predicted_threat_level;     // 预测威胁等级
    int predicted_jamming_type;     // 预测干扰类型
} RKNNInferenceData;

// RKNN推理器句柄
typedef void* RKNNInference;

// 核心函数
RKNNInference* rknn_inference_create(void);
void rknn_inference_destroy(RKNNInference* inference);

// 模型管理
int rknn_inference_load_model(RKNNInference* inference, const char* model_path);
int rknn_inference_is_model_loaded(RKNNInference* inference);

// 推理执行
int rknn_inference_predict(RKNNInference* inference, 
                          const RadarParameters* radar_params,
                          const ThreatAssessmentData* threat_data,
                          RKNNInferenceData* result);

// 批量推理
int rknn_inference_batch_predict(RKNNInference* inference,
                                 const RadarParameters* radar_params_array,
                                 const ThreatAssessmentData* threat_data_array,
                                 int batch_size,
                                 RKNNInferenceData* results);

// 辅助函数
const char* rknn_inference_get_stats(RKNNInference* inference);
const char* rknn_get_error_string(int error_code);
int rknn_inference_get_model_info(RKNNInference* inference, char* info_buffer, int buffer_size);

// 配置函数
int rknn_inference_set_config(RKNNInference* inference, 
                             const char* config_key, 
                             const char* config_value);

// 错误代码常量
#define RKNN_SUCCESS           0   // 成功
#define RKNN_ERROR_INVALID     -1  // 无效参数
#define RKNN_ERROR_MODEL       -2  // 模型错误
#define RKNN_ERROR_INFERENCE   -3  // 推理错误
#define RKNN_ERROR_MEMORY      -4  // 内存错误
#define RKNN_ERROR_NOT_LOADED  -5  // 模型未加载

// 配置常量
#define RKNN_CONFIG_THREADS    "threads"        // 线程数配置
#define RKNN_CONFIG_PRECISION  "precision"      // 精度配置
#define RKNN_CONFIG_BATCH_SIZE "batch_size"     // 批处理大小

// 输入维度常量
#define RKNN_INPUT_DIM         12  // 输入特征维度
#define RKNN_THREAT_LEVELS     5   // 威胁等级数量
#define RKNN_JAMMING_TYPES     5   // 干扰类型数量

#ifdef __cplusplus
}
#endif

#endif // RKNN_INFERENCE_H
