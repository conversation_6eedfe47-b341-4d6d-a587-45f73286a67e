"""
干扰效果评估模块
实现多维度、实时的雷达干扰效果评估系统
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from enum import Enum
from dataclasses import dataclass
import time
import math

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.types import RadarType, WorkMode, JammingType


class EffectivenessLevel(Enum):
    """干扰效果等级"""
    INEFFECTIVE = 0      # 无效 (0-20%)
    LOW = 1             # 低效 (20-40%)
    MODERATE = 2        # 中等 (40-60%)
    HIGH = 3            # 高效 (60-80%)
    VERY_HIGH = 4       # 极高 (80-100%)


@dataclass
class JammingEffectivenessMetrics:
    """干扰效果评估指标"""
    # 基础指标
    snr_degradation: float = 0.0          # 信噪比退化 (dB)
    detection_degradation: float = 0.0     # 检测性能退化 (%)
    tracking_degradation: float = 0.0      # 跟踪性能退化 (%)
    
    # 功能影响指标
    range_accuracy_loss: float = 0.0       # 距离精度损失 (%)
    velocity_accuracy_loss: float = 0.0    # 速度精度损失 (%)
    angular_accuracy_loss: float = 0.0     # 角度精度损失 (%)
    
    # 系统响应指标
    false_alarm_increase: float = 0.0      # 虚警率增加 (倍数)
    false_target_count: int = 0            # 虚假目标数量
    mode_degradation_level: int = 0        # 模式降级等级
    
    # 综合评估
    overall_effectiveness: float = 0.0      # 综合干扰有效性 (0-1)
    effectiveness_level: EffectivenessLevel = EffectivenessLevel.INEFFECTIVE
    
    # 时间相关
    response_time: float = 0.0             # 雷达响应时间 (s)
    adaptation_time: float = 0.0           # 雷达适应时间 (s)


@dataclass
class RadarPerformanceBaseline:
    """雷达性能基线"""
    baseline_snr: float = 20.0             # 基线信噪比 (dB)
    baseline_detection_prob: float = 0.9   # 基线检测概率
    baseline_false_alarm_rate: float = 1e-6 # 基线虚警率
    baseline_range_accuracy: float = 10.0  # 基线距离精度 (m)
    baseline_velocity_accuracy: float = 1.0 # 基线速度精度 (m/s)
    baseline_angular_accuracy: float = 0.1  # 基线角度精度 (度)


class JammingEffectivenessEvaluator:
    """干扰效果评估器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化干扰效果评估器
        
        Args:
            config: 配置参数
        """
        if config is None:
            config = {}
        
        # 评估权重配置
        self.evaluation_weights = config.get('evaluation_weights', {
            'snr_weight': 0.25,
            'detection_weight': 0.25,
            'tracking_weight': 0.20,
            'accuracy_weight': 0.15,
            'system_response_weight': 0.15
        })
        
        # 雷达类型特定的基线性能
        self.radar_baselines = {
            RadarType.GUIDANCE: RadarPerformanceBaseline(
                baseline_snr=25.0, baseline_detection_prob=0.95,
                baseline_range_accuracy=5.0, baseline_velocity_accuracy=0.5
            ),
            RadarType.FIRE_CONTROL: RadarPerformanceBaseline(
                baseline_snr=22.0, baseline_detection_prob=0.92,
                baseline_range_accuracy=8.0, baseline_velocity_accuracy=0.8
            ),
            RadarType.COMMAND_GUIDANCE: RadarPerformanceBaseline(
                baseline_snr=20.0, baseline_detection_prob=0.90,
                baseline_range_accuracy=10.0, baseline_velocity_accuracy=1.0
            ),
            RadarType.SEARCH: RadarPerformanceBaseline(
                baseline_snr=18.0, baseline_detection_prob=0.85,
                baseline_range_accuracy=15.0, baseline_velocity_accuracy=2.0
            ),
            RadarType.NAVIGATION: RadarPerformanceBaseline(
                baseline_snr=15.0, baseline_detection_prob=0.80,
                baseline_range_accuracy=20.0, baseline_velocity_accuracy=3.0
            )
        }
        
        # 干扰类型效果系数
        self.jamming_effectiveness_coefficients = {
            JammingType.RANGE_DECEPTION: {
                'range_impact': 0.8, 'velocity_impact': 0.2, 'detection_impact': 0.3
            },
            JammingType.VELOCITY_DECEPTION: {
                'range_impact': 0.2, 'velocity_impact': 0.8, 'detection_impact': 0.3
            },
            JammingType.RANGE_VELOCITY_DECEPTION: {
                'range_impact': 0.7, 'velocity_impact': 0.7, 'detection_impact': 0.4
            },
            JammingType.COMB_SPECTRUM: {
                'range_impact': 0.5, 'velocity_impact': 0.5, 'detection_impact': 0.6
            },
            JammingType.NOISE_AMPLITUDE: {
                'range_impact': 0.3, 'velocity_impact': 0.3, 'detection_impact': 0.8
            },
            JammingType.NOISE_FREQUENCY: {
                'range_impact': 0.4, 'velocity_impact': 0.6, 'detection_impact': 0.7
            },
            JammingType.NOISE_PHASE: {
                'range_impact': 0.3, 'velocity_impact': 0.4, 'detection_impact': 0.6
            },
            JammingType.SWEEP_FREQUENCY: {
                'range_impact': 0.6, 'velocity_impact': 0.4, 'detection_impact': 0.9
            }
        }
        
        # 历史评估记录
        self.evaluation_history = []
        self.performance_trends = {}
    
    def evaluate_jamming_effectiveness(self, 
                                     radar_type: RadarType,
                                     work_mode: WorkMode,
                                     jamming_type: JammingType,
                                     jamming_power: float,
                                     jamming_params: Dict,
                                     radar_response: Dict,
                                     previous_state: Optional[Dict] = None) -> JammingEffectivenessMetrics:
        """
        评估干扰效果
        
        Args:
            radar_type: 雷达类型
            work_mode: 工作模式
            jamming_type: 干扰类型
            jamming_power: 干扰功率 (0-1)
            jamming_params: 干扰参数
            radar_response: 雷达响应数据
            previous_state: 前一状态（用于计算变化）
            
        Returns:
            干扰效果评估指标
        """
        start_time = time.time()
        
        # 获取雷达基线性能
        baseline = self.radar_baselines.get(radar_type, RadarPerformanceBaseline())
        
        # 计算基础性能指标
        metrics = JammingEffectivenessMetrics()
        
        # 1. 计算信噪比退化
        metrics.snr_degradation = self._calculate_snr_degradation(
            radar_response, baseline, jamming_power
        )
        
        # 2. 计算检测性能退化
        metrics.detection_degradation = self._calculate_detection_degradation(
            radar_response, baseline, jamming_type, jamming_power
        )
        
        # 3. 计算跟踪性能退化
        metrics.tracking_degradation = self._calculate_tracking_degradation(
            radar_response, baseline, jamming_type, jamming_power
        )
        
        # 4. 计算精度损失
        metrics.range_accuracy_loss, metrics.velocity_accuracy_loss, metrics.angular_accuracy_loss = \
            self._calculate_accuracy_losses(radar_response, baseline, jamming_type, jamming_params)
        
        # 5. 计算系统响应指标
        metrics.false_alarm_increase = self._calculate_false_alarm_increase(
            radar_response, baseline
        )
        metrics.false_target_count = radar_response.get('false_targets', 0)
        metrics.mode_degradation_level = self._calculate_mode_degradation(
            work_mode, previous_state
        )
        
        # 6. 计算综合有效性
        metrics.overall_effectiveness = self._calculate_overall_effectiveness(metrics)
        metrics.effectiveness_level = self._determine_effectiveness_level(
            metrics.overall_effectiveness
        )
        
        # 7. 计算时间指标
        metrics.response_time = time.time() - start_time
        metrics.adaptation_time = self._estimate_adaptation_time(
            radar_type, jamming_type, metrics.overall_effectiveness
        )
        
        # 记录评估历史
        self._record_evaluation(radar_type, work_mode, jamming_type, metrics)
        
        return metrics
    
    def _calculate_snr_degradation(self, radar_response: Dict, 
                                 baseline: RadarPerformanceBaseline,
                                 jamming_power: float) -> float:
        """计算信噪比退化"""
        current_snr = radar_response.get('current_snr', baseline.baseline_snr)
        snr_degradation = baseline.baseline_snr - current_snr
        
        # 考虑干扰功率的影响
        power_factor = jamming_power * 20  # 最大20dB退化
        
        return min(snr_degradation, power_factor)
    
    def _calculate_detection_degradation(self, radar_response: Dict,
                                       baseline: RadarPerformanceBaseline,
                                       jamming_type: JammingType,
                                       jamming_power: float) -> float:
        """计算检测性能退化"""
        current_detection_prob = radar_response.get('detection_probability', 
                                                   baseline.baseline_detection_prob)
        
        # 基础退化
        detection_loss = (baseline.baseline_detection_prob - current_detection_prob) / \
                        baseline.baseline_detection_prob * 100
        
        # 干扰类型修正
        if jamming_type in self.jamming_effectiveness_coefficients:
            coeff = self.jamming_effectiveness_coefficients[jamming_type]
            detection_loss *= coeff['detection_impact']
        
        return min(100.0, max(0.0, detection_loss))

    def _calculate_tracking_degradation(self, radar_response: Dict,
                                      baseline: RadarPerformanceBaseline,
                                      jamming_type: JammingType,
                                      jamming_power: float) -> float:
        """计算跟踪性能退化"""
        # 从雷达响应中获取跟踪性能指标
        performance_metrics = radar_response.get('performance_metrics', {})
        range_accuracy = performance_metrics.get('range_accuracy', 1.0)
        velocity_accuracy = performance_metrics.get('velocity_accuracy', 1.0)

        # 计算跟踪性能退化
        tracking_degradation = (2.0 - range_accuracy - velocity_accuracy) / 2.0 * 100

        # 干扰类型修正
        if jamming_type in self.jamming_effectiveness_coefficients:
            coeff = self.jamming_effectiveness_coefficients[jamming_type]
            # 对于欺骗干扰，跟踪退化更明显
            if jamming_type in [JammingType.RANGE_DECEPTION, JammingType.VELOCITY_DECEPTION,
                               JammingType.RANGE_VELOCITY_DECEPTION]:
                tracking_degradation *= 1.5

        return min(100.0, max(0.0, tracking_degradation))

    def _calculate_accuracy_losses(self, radar_response: Dict,
                                 baseline: RadarPerformanceBaseline,
                                 jamming_type: JammingType,
                                 jamming_params: Dict) -> Tuple[float, float, float]:
        """计算精度损失"""
        # 基础精度损失计算
        range_loss = 0.0
        velocity_loss = 0.0
        angular_loss = 0.0

        if jamming_type in self.jamming_effectiveness_coefficients:
            coeff = self.jamming_effectiveness_coefficients[jamming_type]

            # 距离精度损失
            if 'range_error' in radar_response:
                range_error = abs(radar_response['range_error'])
                range_loss = min(90.0, range_error / baseline.baseline_range_accuracy * 100)
            else:
                range_loss = coeff['range_impact'] * 50  # 默认损失

            # 速度精度损失
            if 'velocity_error' in radar_response:
                velocity_error = abs(radar_response['velocity_error'])
                velocity_loss = min(90.0, velocity_error / baseline.baseline_velocity_accuracy * 100)
            else:
                velocity_loss = coeff['velocity_impact'] * 50  # 默认损失

            # 角度精度损失（基于干扰类型估算）
            angular_loss = min(coeff['range_impact'], coeff['velocity_impact']) * 30

        return range_loss, velocity_loss, angular_loss

    def _calculate_false_alarm_increase(self, radar_response: Dict,
                                      baseline: RadarPerformanceBaseline) -> float:
        """计算虚警率增加"""
        performance_metrics = radar_response.get('performance_metrics', {})
        current_false_alarm = performance_metrics.get('false_alarm_rate',
                                                     baseline.baseline_false_alarm_rate)

        # 计算虚警率增加倍数
        if baseline.baseline_false_alarm_rate > 0:
            increase_factor = current_false_alarm / baseline.baseline_false_alarm_rate
        else:
            increase_factor = 1.0

        return max(1.0, increase_factor)

    def _calculate_mode_degradation(self, current_mode: WorkMode,
                                  previous_state: Optional[Dict]) -> int:
        """计算模式降级等级"""
        if previous_state is None:
            return 0

        previous_mode = previous_state.get('work_mode')
        if previous_mode is None:
            return 0

        # 模式优先级：GUIDANCE > IMAGING > TRACK > SEARCH
        mode_priority = {
            WorkMode.GUIDANCE: 4,
            WorkMode.IMAGING: 3,
            WorkMode.TRACK: 2,
            WorkMode.SEARCH: 1
        }

        current_priority = mode_priority.get(current_mode, 1)
        previous_priority = mode_priority.get(previous_mode, 1)

        return max(0, previous_priority - current_priority)

    def _calculate_overall_effectiveness(self, metrics: JammingEffectivenessMetrics) -> float:
        """计算综合干扰有效性"""
        # 归一化各项指标到0-1范围
        snr_score = min(1.0, metrics.snr_degradation / 20.0)  # 20dB为满分
        detection_score = metrics.detection_degradation / 100.0
        tracking_score = metrics.tracking_degradation / 100.0

        # 精度损失综合评分
        accuracy_score = (metrics.range_accuracy_loss +
                         metrics.velocity_accuracy_loss +
                         metrics.angular_accuracy_loss) / 300.0

        # 系统响应评分
        false_alarm_score = min(1.0, (metrics.false_alarm_increase - 1.0) / 9.0)  # 10倍为满分
        false_target_score = min(1.0, metrics.false_target_count / 10.0)  # 10个为满分
        mode_degradation_score = min(1.0, metrics.mode_degradation_level / 3.0)  # 3级为满分

        system_response_score = (false_alarm_score + false_target_score +
                               mode_degradation_score) / 3.0

        # 加权综合评分
        weights = self.evaluation_weights
        overall_score = (
            weights['snr_weight'] * snr_score +
            weights['detection_weight'] * detection_score +
            weights['tracking_weight'] * tracking_score +
            weights['accuracy_weight'] * accuracy_score +
            weights['system_response_weight'] * system_response_score
        )

        return min(1.0, max(0.0, overall_score))

    def _determine_effectiveness_level(self, overall_effectiveness: float) -> EffectivenessLevel:
        """确定干扰效果等级"""
        if overall_effectiveness >= 0.8:
            return EffectivenessLevel.VERY_HIGH
        elif overall_effectiveness >= 0.6:
            return EffectivenessLevel.HIGH
        elif overall_effectiveness >= 0.4:
            return EffectivenessLevel.MODERATE
        elif overall_effectiveness >= 0.2:
            return EffectivenessLevel.LOW
        else:
            return EffectivenessLevel.INEFFECTIVE

    def _estimate_adaptation_time(self, radar_type: RadarType,
                                jamming_type: JammingType,
                                effectiveness: float) -> float:
        """估算雷达适应时间"""
        # 基础适应时间（秒）
        base_adaptation_times = {
            RadarType.GUIDANCE: 0.5,
            RadarType.FIRE_CONTROL: 1.0,
            RadarType.COMMAND_GUIDANCE: 2.0,
            RadarType.SEARCH: 3.0,
            RadarType.NAVIGATION: 5.0
        }

        base_time = base_adaptation_times.get(radar_type, 2.0)

        # 干扰效果越好，雷达适应时间越长
        effectiveness_factor = 1.0 + effectiveness * 2.0

        # 复杂干扰类型需要更长适应时间
        complexity_factors = {
            JammingType.RANGE_VELOCITY_DECEPTION: 1.5,
            JammingType.COMB_SPECTRUM: 1.3,
            JammingType.SWEEP_FREQUENCY: 1.8,
            JammingType.NOISE_FREQUENCY: 1.2
        }

        complexity_factor = complexity_factors.get(jamming_type, 1.0)

        return base_time * effectiveness_factor * complexity_factor

    def _record_evaluation(self, radar_type: RadarType, work_mode: WorkMode,
                          jamming_type: JammingType, metrics: JammingEffectivenessMetrics):
        """记录评估结果"""
        evaluation_record = {
            'timestamp': time.time(),
            'radar_type': radar_type,
            'work_mode': work_mode,
            'jamming_type': jamming_type,
            'metrics': metrics
        }

        self.evaluation_history.append(evaluation_record)

        # 更新性能趋势
        key = f"{radar_type.name}_{jamming_type.name}"
        if key not in self.performance_trends:
            self.performance_trends[key] = []

        self.performance_trends[key].append({
            'timestamp': evaluation_record['timestamp'],
            'effectiveness': metrics.overall_effectiveness,
            'level': metrics.effectiveness_level
        })

        # 保持历史记录在合理范围内
        if len(self.evaluation_history) > 1000:
            self.evaluation_history = self.evaluation_history[-500:]

    def get_effectiveness_trend(self, radar_type: RadarType,
                              jamming_type: JammingType,
                              time_window: float = 300.0) -> List[Dict]:
        """
        获取干扰效果趋势

        Args:
            radar_type: 雷达类型
            jamming_type: 干扰类型
            time_window: 时间窗口（秒）

        Returns:
            效果趋势数据列表
        """
        key = f"{radar_type.name}_{jamming_type.name}"
        if key not in self.performance_trends:
            return []

        current_time = time.time()
        trend_data = []

        for record in self.performance_trends[key]:
            if current_time - record['timestamp'] <= time_window:
                trend_data.append(record)

        return sorted(trend_data, key=lambda x: x['timestamp'])

    def get_effectiveness_statistics(self, radar_type: Optional[RadarType] = None,
                                   jamming_type: Optional[JammingType] = None) -> Dict:
        """
        获取干扰效果统计信息

        Args:
            radar_type: 雷达类型过滤（可选）
            jamming_type: 干扰类型过滤（可选）

        Returns:
            统计信息字典
        """
        filtered_records = []

        for record in self.evaluation_history:
            if radar_type is not None and record['radar_type'] != radar_type:
                continue
            if jamming_type is not None and record['jamming_type'] != jamming_type:
                continue
            filtered_records.append(record)

        if not filtered_records:
            return {}

        # 提取效果值
        effectiveness_values = [r['metrics'].overall_effectiveness for r in filtered_records]

        # 统计各效果等级的分布
        level_counts = {}
        for record in filtered_records:
            level = record['metrics'].effectiveness_level
            level_counts[level.name] = level_counts.get(level.name, 0) + 1

        statistics = {
            'total_evaluations': len(filtered_records),
            'average_effectiveness': np.mean(effectiveness_values),
            'max_effectiveness': np.max(effectiveness_values),
            'min_effectiveness': np.min(effectiveness_values),
            'std_effectiveness': np.std(effectiveness_values),
            'level_distribution': level_counts,
            'success_rate': len([v for v in effectiveness_values if v >= 0.6]) / len(effectiveness_values)
        }

        return statistics

    def compare_jamming_effectiveness(self, radar_type: RadarType,
                                    jamming_types: List[JammingType]) -> Dict:
        """
        比较不同干扰类型的效果

        Args:
            radar_type: 雷达类型
            jamming_types: 要比较的干扰类型列表

        Returns:
            比较结果字典
        """
        comparison_results = {}

        for jamming_type in jamming_types:
            stats = self.get_effectiveness_statistics(radar_type, jamming_type)
            if stats:
                comparison_results[jamming_type.name] = {
                    'average_effectiveness': stats['average_effectiveness'],
                    'success_rate': stats['success_rate'],
                    'total_evaluations': stats['total_evaluations']
                }

        # 排序结果
        sorted_results = sorted(comparison_results.items(),
                              key=lambda x: x[1]['average_effectiveness'],
                              reverse=True)

        return {
            'radar_type': radar_type.name,
            'comparison_results': dict(sorted_results),
            'best_jamming_type': sorted_results[0][0] if sorted_results else None
        }

    def generate_effectiveness_report(self, radar_type: Optional[RadarType] = None,
                                    time_window: float = 3600.0) -> str:
        """
        生成干扰效果评估报告

        Args:
            radar_type: 雷达类型过滤（可选）
            time_window: 时间窗口（秒）

        Returns:
            报告文本
        """
        current_time = time.time()

        # 过滤时间窗口内的记录
        recent_records = []
        for record in self.evaluation_history:
            if current_time - record['timestamp'] <= time_window:
                if radar_type is None or record['radar_type'] == radar_type:
                    recent_records.append(record)

        if not recent_records:
            return "无可用的干扰效果评估数据"

        # 生成报告
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("干扰效果评估报告")
        report_lines.append("=" * 60)

        if radar_type:
            report_lines.append(f"雷达类型: {radar_type.name}")
        report_lines.append(f"时间窗口: {time_window/3600:.1f} 小时")
        report_lines.append(f"评估次数: {len(recent_records)}")
        report_lines.append("")

        # 总体统计
        stats = self.get_effectiveness_statistics(radar_type)
        if stats:
            report_lines.append("总体统计:")
            report_lines.append(f"  平均效果: {stats['average_effectiveness']:.3f}")
            report_lines.append(f"  最大效果: {stats['max_effectiveness']:.3f}")
            report_lines.append(f"  最小效果: {stats['min_effectiveness']:.3f}")
            report_lines.append(f"  成功率: {stats['success_rate']:.1%}")
            report_lines.append("")

        # 效果等级分布
        if 'level_distribution' in stats:
            report_lines.append("效果等级分布:")
            for level, count in stats['level_distribution'].items():
                percentage = count / stats['total_evaluations'] * 100
                report_lines.append(f"  {level}: {count} ({percentage:.1f}%)")
            report_lines.append("")

        # 各干扰类型效果比较
        all_jamming_types = list(set(r['jamming_type'] for r in recent_records))
        if len(all_jamming_types) > 1:
            comparison = self.compare_jamming_effectiveness(
                radar_type or recent_records[0]['radar_type'],
                all_jamming_types
            )

            report_lines.append("干扰类型效果排名:")
            for i, (jamming_name, data) in enumerate(comparison['comparison_results'].items(), 1):
                report_lines.append(f"  {i}. {jamming_name}: {data['average_effectiveness']:.3f}")
            report_lines.append("")

        report_lines.append("=" * 60)

        return "\n".join(report_lines)

    def reset_evaluation_history(self):
        """重置评估历史"""
        self.evaluation_history.clear()
        self.performance_trends.clear()

    def export_evaluation_data(self, filepath: str):
        """导出评估数据到文件"""
        import json

        export_data = {
            'evaluation_history': [],
            'performance_trends': self.performance_trends,
            'export_timestamp': time.time()
        }

        # 转换评估历史为可序列化格式
        for record in self.evaluation_history:
            serializable_record = {
                'timestamp': record['timestamp'],
                'radar_type': record['radar_type'].name,
                'work_mode': record['work_mode'].name,
                'jamming_type': record['jamming_type'].name,
                'metrics': {
                    'snr_degradation': float(record['metrics'].snr_degradation),
                    'detection_degradation': float(record['metrics'].detection_degradation),
                    'tracking_degradation': float(record['metrics'].tracking_degradation),
                    'range_accuracy_loss': float(record['metrics'].range_accuracy_loss),
                    'velocity_accuracy_loss': float(record['metrics'].velocity_accuracy_loss),
                    'angular_accuracy_loss': float(record['metrics'].angular_accuracy_loss),
                    'false_alarm_increase': float(record['metrics'].false_alarm_increase),
                    'false_target_count': int(record['metrics'].false_target_count),
                    'mode_degradation_level': int(record['metrics'].mode_degradation_level),
                    'overall_effectiveness': float(record['metrics'].overall_effectiveness),
                    'effectiveness_level': record['metrics'].effectiveness_level.name,
                    'response_time': float(record['metrics'].response_time),
                    'adaptation_time': float(record['metrics'].adaptation_time)
                }
            }
            export_data['evaluation_history'].append(serializable_record)

        # 转换性能趋势为可序列化格式
        serializable_trends = {}
        for key, trends in self.performance_trends.items():
            serializable_trends[key] = []
            for trend in trends:
                serializable_trend = {
                    'timestamp': float(trend['timestamp']),
                    'effectiveness': float(trend['effectiveness']),
                    'level': trend['level'].name
                }
                serializable_trends[key].append(serializable_trend)
        export_data['performance_trends'] = serializable_trends

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)


# 便捷函数
def create_effectiveness_evaluator(config: Optional[Dict] = None) -> JammingEffectivenessEvaluator:
    """创建干扰效果评估器实例"""
    return JammingEffectivenessEvaluator(config)


def quick_evaluate_effectiveness(radar_type: RadarType,
                                work_mode: WorkMode,
                                jamming_type: JammingType,
                                jamming_power: float,
                                radar_response: Dict) -> float:
    """快速评估干扰效果（返回综合有效性分数）"""
    evaluator = create_effectiveness_evaluator()
    metrics = evaluator.evaluate_jamming_effectiveness(
        radar_type, work_mode, jamming_type, jamming_power, {}, radar_response
    )
    return metrics.overall_effectiveness
