# ONNX模型转换和推理依赖包
# 用于PyTorch模型转换为ONNX格式和ONNX推理

# 核心ONNX相关
onnx>=1.10.0
onnxruntime>=1.8.0
onnxruntime-gpu>=1.8.0  # GPU版本(可选)

# PyTorch (用于模型加载和转换)
torch>=1.8.0
torchvision>=0.9.0

# 数值计算
numpy>=1.19.0

# 模型优化
onnx-simplifier>=0.3.6
onnxoptimizer>=0.2.6

# 模型分析工具
netron>=5.0.0  # 模型可视化

# 数据处理
pandas>=1.2.0

# 配置文件
pyyaml>=5.4.0

# 类型提示
typing-extensions>=3.7.0

# 注意: 以下是Python内置模块，无需安装
# json, pathlib, typing, argparse, os, sys, time, math
# collections, dataclasses, shutil, glob, re, logging, warnings, gc
# random, copy, functools, itertools, operator, pickle

# 进度显示
tqdm>=4.60.0

# 模型验证
pytest>=6.0.0

# 内存分析
memory-profiler>=0.58.0

# 模型转换工具
tf2onnx>=1.8.0  # TensorFlow转ONNX(可选)

# 模型部署
flask>=2.0.0  # Web服务(可选)
fastapi>=0.65.0  # API服务(可选)
uvicorn>=0.14.0  # ASGI服务器(可选)

# 数据验证
cerberus>=1.3.0

# 环境管理
python-dotenv>=0.17.0

# HTTP客户端
requests>=2.25.0

# 注意: 以下是Python内置模块，无需安装
# concurrent.futures, threading, multiprocessing, queue, datetime
# string, base64, urllib, hashlib, tempfile, zipfile, tarfile
# csv, xml, traceback, unittest, unittest.mock, subprocess
# signal, platform, socket, threading.local, weakref, contextlib
# abc, enum, decimal, fractions, statistics, bisect, heapq
# collections.deque, collections.Counter, collections.defaultdict
# collections.OrderedDict, collections.namedtuple, cProfile, pstats
