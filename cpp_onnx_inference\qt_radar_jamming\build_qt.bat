@echo off
echo === Qt Radar Jamming System Build Script ===

REM Set Qt environment variables (adjust according to your Qt installation path)
set QT_DIR=C:\Qt\5.15.2\msvc2019_64
set PATH=%QT_DIR%\bin;%PATH%

REM Check if Qt is available
qmake --version > nul 2>&1
if errorlevel 1 (
    echo Error: Qt not found. Please install Qt and set QT_DIR correctly.
    echo Current QT_DIR: %QT_DIR%
    echo.
    echo Please modify build_qt.bat and set the correct Qt installation path.
    echo Example: set QT_DIR=C:\Qt\5.15.2\msvc2019_64
    pause
    exit /b 1
)

echo Qt found: 
qmake --version

REM Check ONNX Runtime
if exist "..\third_party\onnxruntime\lib\onnxruntime.lib" (
    echo Found ONNX Runtime, enabling real inference...
) else (
    echo ONNX Runtime not found, using simulation mode...
)

REM Create build directory
if not exist "build" mkdir build
cd build

REM Generate Makefile
echo Generating Makefile...
qmake ..\RadarJammingQt.pro

REM Check if qmake succeeded
if errorlevel 1 (
    echo Error: qmake failed
    cd ..
    pause
    exit /b 1
)

REM Compile project
echo Compiling...
nmake

REM Check if compilation succeeded
if errorlevel 1 (
    echo Error: Compilation failed
    cd ..
    pause
    exit /b 1
)

REM Copy ONNX Runtime DLL (if exists)
if exist "..\..\third_party\onnxruntime\lib\onnxruntime.dll" (
    echo Copying ONNX Runtime DLL...
    copy "..\..\third_party\onnxruntime\lib\onnxruntime.dll" . > nul
)

cd ..

echo.
echo Build successful!
echo Executable: build\RadarJammingQt.exe
echo.

REM 运行测试
echo Running test...
cd build
RadarJammingQt.exe
cd ..

echo.
echo Test completed
echo Build completed successfully!
pause
