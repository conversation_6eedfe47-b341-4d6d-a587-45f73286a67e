"""
频率同步跟踪系统
实现智能干扰模块对雷达频率变化的实时同步跟踪和调整
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import time


@dataclass
class FrequencyTrackingState:
    """频率跟踪状态 - 简化版，专注于同步干扰频点"""
    current_radar_frequency: float  # 当前雷达频率 (Hz)
    previous_radar_frequency: float  # 上一次雷达频率 (Hz)
    jamming_frequencies: List[float]  # 当前干扰频点列表 (Hz)
    last_update_time: float  # 上次更新时间戳
    frequency_change_history: List[Tuple[float, float]]  # 频率变化历史 (时间, 频率)


class FrequencyTrackingSystem:
    """频率同步跟踪系统 - 简化版，专注于同步干扰频点"""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.tracking_state = None
        self.jamming_frequency_calculator = JammingFrequencyCalculator()
    
    def initialize_tracking(self, initial_radar_freq: float, jamming_types: List[str]) -> FrequencyTrackingState:
        """初始化频率跟踪"""
        current_time = time.time()
        
        # 计算初始干扰频点
        initial_jamming_freqs = self.jamming_frequency_calculator.calculate_initial_frequencies(
            radar_frequency=initial_radar_freq,
            jamming_types=jamming_types
        )
        
        self.tracking_state = FrequencyTrackingState(
            current_radar_frequency=initial_radar_freq,
            previous_radar_frequency=initial_radar_freq,
            jamming_frequencies=initial_jamming_freqs,
            last_update_time=current_time,
            frequency_change_history=[(current_time, initial_radar_freq)]
        )
        
        if self.verbose:
            print(f"频率跟踪系统初始化:")
            print(f"   雷达频率: {initial_radar_freq/1e9:.2f} GHz")
            print(f"   干扰频点: {[f/1e9 for f in initial_jamming_freqs]} GHz")
        
        return self.tracking_state
    
    def update_radar_frequency(self, new_radar_freq: float, jamming_types: List[str]) -> Dict:
        """更新雷达频率并同步调整干扰频点"""
        if self.tracking_state is None:
            # 初始化跟踪状态
            self.initialize_tracking(new_radar_freq, jamming_types)
            return {
                'frequency_changed': False,  # 初始化时不算频率变化
                'frequency_delta': 0.0,
                'new_jamming_frequencies': self.tracking_state.jamming_frequencies,
                'tracking_state': self.tracking_state,
                'synchronization_success': True
            }
        
        current_time = time.time()
        freq_delta = new_radar_freq - self.tracking_state.current_radar_frequency

        # 简化频率跟踪：直接基于雷达载频计算偏移
        # 不使用复杂的历史分析，直接返回当前频率差值

        # 保护机制：限制频率偏移的范围，避免异常值
        if abs(freq_delta) > 10e9:  # 超过10GHz的偏移是异常的
            if self.verbose:
                print(f"警告：检测到异常频率偏移 {freq_delta/1e9:.1f} GHz，重置为0")
            freq_delta = 0.0
        
        # 检测频率变化
        if abs(freq_delta) > 1e6:  # 频率变化超过1MHz
            if self.verbose:
                print(f"检测到雷达频率变化: {freq_delta/1e6:+.1f} MHz")
                print(f"   {self.tracking_state.current_radar_frequency/1e9:.3f} GHz → {new_radar_freq/1e9:.3f} GHz")
            
            # 更新跟踪状态
            self.tracking_state.previous_radar_frequency = self.tracking_state.current_radar_frequency
            self.tracking_state.current_radar_frequency = new_radar_freq
            self.tracking_state.last_update_time = current_time
            
            # 记录频率变化历史
            self.tracking_state.frequency_change_history.append((current_time, new_radar_freq))
            if len(self.tracking_state.frequency_change_history) > 50:  # 保持最近50次记录
                self.tracking_state.frequency_change_history.pop(0)
            
            # 分析频率变化模式
            self._analyze_frequency_pattern(freq_delta)

            # 同步调整干扰频点 - 核心功能
            new_jamming_freqs = self._synchronize_jamming_frequencies(
                new_radar_freq, jamming_types
            )

            self.tracking_state.jamming_frequencies = new_jamming_freqs

            if self.verbose:
                print(f"   同步调整干扰频点: {[f/1e9 for f in new_jamming_freqs]} GHz")
        
        return {
            'frequency_changed': abs(freq_delta) > 1e6,
            'frequency_delta': freq_delta,
            'new_jamming_frequencies': self.tracking_state.jamming_frequencies,
            'tracking_state': self.tracking_state,
            'synchronization_success': True
        }
    
    def _analyze_frequency_pattern(self, freq_delta: float):
        """分析频率变化模式 - 仅用于同步干扰频点"""
        abs_delta = abs(freq_delta)

        # 简化：只记录频率变化，不调整带宽参数
        if self.verbose:
            if abs_delta > 500e6:  # 大幅跳变
                print(f"   检测到大幅频率跳变: {abs_delta/1e6:.0f} MHz")
            elif abs_delta > 100e6:  # 中等跳变
                print(f"   检测到中等频率跳变: {abs_delta/1e6:.0f} MHz")
            else:  # 小幅调整
                print(f"   检测到小幅频率调整: {abs_delta/1e6:.0f} MHz")
    
    def _synchronize_jamming_frequencies(self, radar_freq: float, jamming_types: List[str], 
                                       predicted_freq: Optional[float] = None) -> List[float]:
        """同步调整干扰频点"""
        jamming_frequencies = []
        
        for jamming_type in jamming_types:
            if jamming_type == '梳状谱':
                # 梳状谱：在雷达频率周围生成多个频点
                comb_freqs = self._generate_comb_frequencies(radar_freq, predicted_freq)
                jamming_frequencies.extend(comb_freqs)
                
            elif jamming_type == '宽带阻塞噪声':
                # 宽带噪声：覆盖雷达频率及预测频率
                noise_freqs = self._generate_noise_frequencies(radar_freq, predicted_freq)
                jamming_frequencies.extend(noise_freqs)
                
            elif jamming_type == '灵巧噪声':
                # 灵巧噪声：智能跟踪雷达频率
                smart_freqs = self._generate_smart_noise_frequencies(radar_freq, predicted_freq)
                jamming_frequencies.extend(smart_freqs)
        
        return jamming_frequencies
    
    def _generate_comb_frequencies(self, radar_freq: float, predicted_freq: Optional[float]) -> List[float]:
        """生成梳状谱干扰频点"""
        frequencies = []
        
        # 主频点：雷达当前频率
        frequencies.append(radar_freq)
        
        # 侧频点：雷达频率 ± 偏移
        offsets = [50e6, 100e6, 150e6, 200e6]  # MHz偏移
        for offset in offsets:
            frequencies.extend([radar_freq - offset, radar_freq + offset])
        
        # 预测频点：如果有预测频率，添加预测频点
        if predicted_freq and abs(predicted_freq - radar_freq) > 10e6:
            frequencies.append(predicted_freq)
            # 预测频率的侧频点
            for offset in offsets[:2]:  # 只用前两个偏移
                frequencies.extend([predicted_freq - offset, predicted_freq + offset])
        
        return frequencies[:8]  # 最多8个频点
    
    def _generate_noise_frequencies(self, radar_freq: float, predicted_freq: Optional[float]) -> List[float]:
        """生成宽带噪声干扰频点 - 仅同步跟踪雷达频率"""
        # 宽带噪声直接跟踪雷达频率，不扩展带宽
        center_freq = radar_freq

        # 如果有预测频率，调整中心频率到雷达和预测频率的中点
        if predicted_freq:
            center_freq = (radar_freq + predicted_freq) / 2

        return [center_freq]  # 宽带噪声跟踪中心频率
    
    def _generate_smart_noise_frequencies(self, radar_freq: float, predicted_freq: Optional[float]) -> List[float]:
        """生成灵巧噪声干扰频点"""
        frequencies = [radar_freq]
        
        # 如果有预测频率，添加预测频点
        if predicted_freq:
            frequencies.append(predicted_freq)
        
        # 添加频率分集点
        freq_diversity = [radar_freq + 25e6, radar_freq - 25e6]
        frequencies.extend(freq_diversity)
        
        return frequencies


class FrequencyPredictor:
    """频率预测器"""
    
    def predict_next_frequency(self, frequency_history: List[Tuple[float, float]]) -> Optional[float]:
        """基于历史数据预测下一个频率"""
        if len(frequency_history) < 3:
            return None
        
        # 提取最近的频率变化
        recent_freqs = [freq for _, freq in frequency_history[-5:]]
        
        # 简单的线性预测
        if len(recent_freqs) >= 3:
            # 计算频率变化趋势
            freq_diffs = [recent_freqs[i+1] - recent_freqs[i] for i in range(len(recent_freqs)-1)]
            avg_diff = np.mean(freq_diffs)
            
            # 预测下一个频率
            predicted_freq = recent_freqs[-1] + avg_diff
            
            # 限制预测范围在合理区间内
            if 1e9 <= predicted_freq <= 20e9:  # 1-20 GHz
                return predicted_freq
        
        return None


class JammingFrequencyCalculator:
    """干扰频率计算器"""
    
    def calculate_initial_frequencies(self, radar_frequency: float, jamming_types: List[str]) -> List[float]:
        """计算初始干扰频点"""
        frequencies = []
        
        for jamming_type in jamming_types:
            if jamming_type == '梳状谱':
                # 梳状谱：雷达频率 ± 多个偏移
                offsets = [0, 50e6, 100e6, 150e6]  # MHz
                for offset in offsets:
                    if offset == 0:
                        frequencies.append(radar_frequency)
                    else:
                        frequencies.extend([radar_frequency - offset, radar_frequency + offset])
            
            elif jamming_type in ['宽带阻塞噪声', '灵巧噪声']:
                # 噪声类型：以雷达频率为中心
                frequencies.append(radar_frequency)
        
        return list(set(frequencies))  # 去重
