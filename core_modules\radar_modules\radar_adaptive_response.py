"""
雷达自适应响应模块
根据检测到的干扰信号，调整雷达工作模式和参数
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from ..system_integration.data_structures import RadarEmissionData


class RadarAdaptiveResponse:
    """雷达自适应响应系统"""

    def __init__(self, verbose=False):
        self.interference_history = []
        self.adaptation_count = 0
        self.current_mode = "正常"
        self.verbose = verbose
        
    def detect_interference(self, received_signal: np.ndarray, radar_signal: np.ndarray, 
                          jamming_signals: List[np.ndarray], jamming_types: List[str]) -> Dict:
        """
        检测干扰信号类型和强度
        
        Args:
            received_signal: 雷达接收信号
            radar_signal: 雷达发射信号
            jamming_signals: 干扰信号列表
            jamming_types: 干扰类型列表
            
        Returns:
            干扰检测结果
        """
        detection_results = {
            'interference_detected': False,
            'interference_types': [],
            'interference_power': [],
            'snr_degradation': 0.0,
            'detection_probability': 1.0
        }
        
        if not jamming_signals:
            return detection_results
        
        # 计算信号功率
        signal_power = np.mean(np.abs(radar_signal)**2)
        total_jamming_power = sum(np.mean(np.abs(jam_sig)**2) for jam_sig in jamming_signals)
        
        # 计算信噪比恶化
        if signal_power > 0:
            snr_degradation = 10 * np.log10(1 + total_jamming_power / signal_power)
        else:
            snr_degradation = 50  # 极大恶化
        
        # 检测各种干扰类型
        for i, (jam_signal, jam_type) in enumerate(zip(jamming_signals, jamming_types)):
            jam_power = np.mean(np.abs(jam_signal)**2)
            
            if jam_power > signal_power * 0.1:  # 干扰功率超过信号功率10%
                detection_results['interference_detected'] = True
                detection_results['interference_types'].append(jam_type)
                detection_results['interference_power'].append(jam_power)
        
        detection_results['snr_degradation'] = snr_degradation
        
        # 计算检测概率恶化
        if snr_degradation > 20:
            detection_results['detection_probability'] = 0.1
        elif snr_degradation > 10:
            detection_results['detection_probability'] = 0.5
        elif snr_degradation > 5:
            detection_results['detection_probability'] = 0.8
        else:
            detection_results['detection_probability'] = 0.95
        
        return detection_results
    
    def adapt_radar_parameters(self, radar_params: RadarEmissionData, 
                             interference_info: Dict) -> RadarEmissionData:
        """
        根据干扰情况自适应调整雷达参数
        
        Args:
            radar_params: 当前雷达参数
            interference_info: 干扰检测信息
            
        Returns:
            调整后的雷达参数
        """
        adapted_params = RadarEmissionData(
            peak_power=radar_params.peak_power,
            frequency=radar_params.frequency,
            pulse_width=radar_params.pulse_width,
            prf=radar_params.prf,
            antenna_gain=radar_params.antenna_gain,
            operating_mode=radar_params.operating_mode,
            beam_direction=radar_params.beam_direction,
            scan_pattern=radar_params.scan_pattern,
            target_range=radar_params.target_range,
            target_velocity=radar_params.target_velocity,
            target_rcs=radar_params.target_rcs
        )
        
        if not interference_info['interference_detected']:
            return adapted_params
        
        interference_types = interference_info['interference_types']
        snr_degradation = interference_info['snr_degradation']
        
        # 记录干扰历史
        self.interference_history.append({
            'types': interference_types,
            'snr_degradation': snr_degradation,
            'adaptation_count': self.adaptation_count
        })
        
        # 根据干扰类型进行自适应
        for interference_type in interference_types:
            if interference_type == '梳状谱':
                adapted_params = self._adapt_to_comb_jamming(adapted_params, snr_degradation)
            elif interference_type == '间歇采样转发':
                adapted_params = self._adapt_to_isrj(adapted_params, snr_degradation)
            elif interference_type == '宽带阻塞噪声':
                adapted_params = self._adapt_to_broadband_noise(adapted_params, snr_degradation)
            elif interference_type == '灵巧噪声':
                adapted_params = self._adapt_to_smart_noise(adapted_params, snr_degradation)
            elif interference_type == '拖引':
                adapted_params = self._adapt_to_deception(adapted_params, snr_degradation)
        
        self.adaptation_count += 1
        return adapted_params
    
    def _adapt_to_comb_jamming(self, params: RadarEmissionData, snr_degradation: float) -> RadarEmissionData:
        """应对梳状谱干扰的自适应策略"""
        if self.verbose:
            print(f"检测到梳状谱干扰，SNR恶化: {snr_degradation:.1f}dB")

        # 频率捷变
        freq_offset = np.random.uniform(-500e6, 500e6)  # ±500MHz
        new_frequency = max(1e9, min(20e9, params.frequency + freq_offset))
        params.frequency = new_frequency
        if self.verbose:
            print(f"   频率捷变: {new_frequency/1e9:.2f} GHz")

        # 增加发射功率
        if snr_degradation > 10:
            power_increase = min(2.0, 1 + snr_degradation / 20)
            params.peak_power *= power_increase
            if self.verbose:
                print(f"   功率增加: {power_increase:.1f}倍")

        # 改变脉冲重复频率
        prf_change = np.random.uniform(0.8, 1.2)
        params.prf = max(100, min(10000, params.prf * prf_change))
        if self.verbose:
            print(f"   PRF调整: {params.prf:.0f} Hz")

        self.current_mode = "抗梳状谱干扰"
        return params
    
    def _adapt_to_isrj(self, params: RadarEmissionData, snr_degradation: float) -> RadarEmissionData:
        """应对间歇采样转发干扰的自适应策略"""
        if self.verbose:
            print(f"检测到间歇采样转发干扰，SNR恶化: {snr_degradation:.1f}dB")

        # 脉冲压缩
        if params.pulse_width > 0.5e-6:
            params.pulse_width *= 0.5  # 缩短脉宽
            if self.verbose:
                print(f"   脉宽压缩: {params.pulse_width*1e6:.1f} μs")

        # 随机化脉冲重复间隔
        prf_jitter = np.random.uniform(0.9, 1.1)
        params.prf = max(1000, min(8000, params.prf * prf_jitter))
        if self.verbose:
            print(f"   PRF随机化: {params.prf:.0f} Hz")

        # 增加天线增益（波束锐化）
        params.antenna_gain = min(50.0, params.antenna_gain * 1.1)
        if self.verbose:
            print(f"   天线增益提升: {params.antenna_gain:.1f} dB")

        # 切换到跟踪模式
        if params.operating_mode != "跟踪":
            params.operating_mode = "跟踪"
            params.scan_pattern = "track"
            if self.verbose:
                print(f"   切换到跟踪模式")

        self.current_mode = "抗间歇采样转发"
        return params
    
    def _adapt_to_broadband_noise(self, params: RadarEmissionData, snr_degradation: float) -> RadarEmissionData:
        """应对宽带阻塞噪声的自适应策略"""
        if self.verbose:
            print(f"检测到宽带阻塞噪声，SNR恶化: {snr_degradation:.1f}dB")

        # 大幅增加发射功率
        power_increase = min(3.0, 1 + snr_degradation / 15)
        params.peak_power *= power_increase
        if self.verbose:
            print(f"   大幅功率增加: {power_increase:.1f}倍")

        # 频率跳变
        freq_bands = [2e9, 4e9, 8e9, 12e9, 16e9]  # 预设频段
        current_band = min(freq_bands, key=lambda x: abs(x - params.frequency))
        available_bands = [f for f in freq_bands if f != current_band]
        if available_bands:
            params.frequency = np.random.choice(available_bands)
            if self.verbose:
                print(f"   频率跳变: {params.frequency/1e9:.0f} GHz")

        # 波束锐化
        params.antenna_gain = min(55.0, params.antenna_gain * 1.2)
        if self.verbose:
            print(f"   波束锐化: {params.antenna_gain:.1f} dB")

        # 切换到高分辨率模式
        if params.operating_mode != "高分辨":
            params.operating_mode = "高分辨"
            params.pulse_width = min(params.pulse_width, 0.5e-6)
            if self.verbose:
                print(f"   切换到高分辨率模式")

        self.current_mode = "抗宽带噪声"
        return params
    
    def _adapt_to_smart_noise(self, params: RadarEmissionData, snr_degradation: float) -> RadarEmissionData:
        """应对灵巧噪声的自适应策略"""
        if self.verbose:
            print(f"检测到灵巧噪声，SNR恶化: {snr_degradation:.1f}dB")

        # 自适应波形
        params.pulse_width = np.random.uniform(0.5e-6, 2e-6)
        if self.verbose:
            print(f"   自适应脉宽: {params.pulse_width*1e6:.1f} μs")

        # 频率分集
        freq_offset = np.random.uniform(-200e6, 200e6)
        params.frequency = max(2e9, min(18e9, params.frequency + freq_offset))
        if self.verbose:
            print(f"   频率分集: {params.frequency/1e9:.2f} GHz")

        # 极化分集
        if self.verbose:
            print(f"   启用极化分集")

        # 空间分集（多波束）
        beam_offset = np.random.uniform(-10, 10)  # 度
        params.beam_direction += beam_offset
        if self.verbose:
            print(f"   波束指向调整: {beam_offset:+.1f}°")

        # 功率控制
        power_adjust = 1.2 if snr_degradation > 15 else 1.1
        params.peak_power *= power_adjust
        if self.verbose:
            print(f"   功率调整: {power_adjust:.1f}倍")

        self.current_mode = "抗灵巧噪声"
        return params
    
    def _adapt_to_deception(self, params: RadarEmissionData, snr_degradation: float) -> RadarEmissionData:
        """应对拖引干扰的自适应策略"""
        if self.verbose:
            print(f"检测到拖引干扰，SNR恶化: {snr_degradation:.1f}dB")

        # 距离门控
        if self.verbose:
            print(f"   启用距离门控")

        # 速度门控
        if self.verbose:
            print(f"   启用速度门控")

        # 角度跟踪
        if params.operating_mode != "精密跟踪":
            params.operating_mode = "精密跟踪"
            params.scan_pattern = "precision_track"
            if self.verbose:
                print(f"   切换到精密跟踪模式")

        # 提高测量精度
        params.pulse_width = min(params.pulse_width, 0.3e-6)
        params.prf = max(params.prf, 5000)  # 提高PRF
        if self.verbose:
            print(f"   提高测量精度: 脉宽{params.pulse_width*1e6:.1f}μs, PRF{params.prf:.0f}Hz")

        # 多普勒滤波器组
        if self.verbose:
            print(f"   启用多普勒滤波器组")

        self.current_mode = "抗拖引干扰"
        return params
    
    def get_adaptation_summary(self) -> Dict:
        """获取自适应总结"""
        return {
            'current_mode': self.current_mode,
            'adaptation_count': self.adaptation_count,
            'interference_history': self.interference_history[-5:],  # 最近5次
            'total_adaptations': len(self.interference_history)
        }
