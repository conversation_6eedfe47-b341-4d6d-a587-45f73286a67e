#include "external_api.h"
#include "jamming_decision.h"
#include "threat_evaluator.h"
#include "onnx_inference.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

// 前向声明
static void parse_onnx_output_for_jamming(const ONNXOutput* onnx_output, ExternalDecisionResult* result);
static void fallback_jamming_decision(ExternalDecisionResult* result);

// 版本信息
const char* radar_get_version(void) {
    return "ONNX Radar Jamming System v1.0.0";
}

// 初始化决策引擎
DecisionEngineHandle radar_init_decision_engine(const char* model_path) {
    if (!model_path) {
        return NULL;
    }

    // 初始化ONNX模型
    printf("正在加载ONNX模型: %s\n", model_path);
    RadarErrorCode init_result = init_onnx_model(model_path);
    if (init_result != RADAR_SUCCESS) {
        printf("警告: ONNX模型初始化失败，将使用备用决策\n");
        // 即使ONNX初始化失败，也返回句柄，使用备用决策
    } else {
        printf("ONNX模型加载成功\n");
    }

    // 创建引擎句柄
    void* engine = malloc(sizeof(int));
    if (engine) {
        *((int*)engine) = (init_result == RADAR_SUCCESS) ? 1 : 0; // 标记ONNX状态
    }
    return engine;
}

// 配置决策引擎
RadarErrorCode radar_config_decision_engine(DecisionEngineHandle engine,
                                          int enable_threat_assessment,
                                          int enable_model_inference,
                                          double confidence_threshold) {
    if (!engine) {
        return RADAR_ERROR_INVALID_PARAM;
    }

    // 在实际实现中，这里会配置引擎参数
    // 现在只是模拟配置成功
    return RADAR_SUCCESS;
}

// 检查引擎是否就绪
int radar_is_engine_ready(DecisionEngineHandle engine) {
    if (!engine) {
        return 0;
    }

    // 检查引擎状态
    return *((int*)engine) == 1;
}

// 验证雷达参数
int radar_validate_parameters(const ExternalRadarParams* params) {
    if (!params) {
        return 0;
    }

    // 基本参数验证
    if (params->frequency <= 0 || params->frequency > 100000) {
        return 0; // 频率范围不合理
    }

    if (params->pulse_width <= 0 || params->pulse_width > 1000) {
        return 0; // 脉宽范围不合理
    }

    if (params->prt <= 0 || params->prt > 100000) {
        return 0; // PRT范围不合理
    }

    if (params->power <= 0) {
        return 0; // 功率必须为正
    }

    if (params->distance <= 0 || params->distance > 1000) {
        return 0; // 距离范围不合理
    }

    return 1; // 参数有效
}

// 执行干扰决策
RadarErrorCode radar_execute_jamming_decision(DecisionEngineHandle engine,
                                            const ExternalRadarParams* radar_params,
                                            ExternalDecisionResult* result) {
    if (!engine || !radar_params || !result) {
        return RADAR_ERROR_INVALID_PARAM;
    }

    // 清空结果结构体
    memset(result, 0, sizeof(ExternalDecisionResult));

    // 转换为内部雷达参数结构体
    RadarParams internal_params;
    internal_params.frequency = radar_params->frequency;
    internal_params.pulse_width = radar_params->pulse_width;
    internal_params.prt = radar_params->prt;
    internal_params.power = radar_params->power;
    internal_params.distance = radar_params->distance;
    internal_params.speed = radar_params->speed;
    internal_params.direction = radar_params->direction;
    internal_params.work_mode = radar_params->work_mode;

    // 执行真正的威胁评估
    ThreatAssessment assessment;
    RadarErrorCode eval_result = evaluate_threat(&internal_params, &assessment);

    if (eval_result != RADAR_SUCCESS) {
        result->status = -1;
        strcpy(result->error_message, "Threat evaluation failed");
        return RADAR_ERROR_INFERENCE;
    }

    result->threat_value = assessment.threat_value;
    result->threat_level = assessment.threat_level;
    result->confidence = assessment.confidence;

    // 使用ONNX模型进行智能干扰决策
    ONNXInput onnx_input;
    ONNXOutput onnx_output;

    // 准备ONNX模型输入特征（基于训练时的特征工程）
    onnx_input.input_data[0] = (float)(radar_params->frequency / 10000.0);  // 频率归一化
    onnx_input.input_data[1] = (float)(radar_params->pulse_width / 10.0);   // 脉宽归一化
    onnx_input.input_data[2] = (float)(radar_params->prt / 10000.0);        // PRT归一化
    onnx_input.input_data[3] = (float)(radar_params->power / 1000000.0);    // 功率归一化
    onnx_input.input_data[4] = (float)(radar_params->distance / 300.0);     // 距离归一化
    onnx_input.input_data[5] = (float)(radar_params->speed / 1000.0);       // 速度归一化
    onnx_input.input_data[6] = (float)(radar_params->direction / 360.0);    // 方向归一化
    onnx_input.input_data[7] = (float)(radar_params->work_mode / 4.0);      // 工作模式归一化
    onnx_input.input_data[8] = (float)assessment.threat_value;              // 威胁值
    onnx_input.input_data[9] = (float)(assessment.threat_level / 5.0);      // 威胁等级归一化

    // 填充剩余输入（模型期望12个特征）
    for (int i = 10; i < 12; i++) {
        onnx_input.input_data[i] = 0.0f;
    }
    onnx_input.input_size = 12;

    // 执行ONNX推理
    RadarErrorCode onnx_result = onnx_inference(&onnx_input, &onnx_output);

    if (onnx_result == RADAR_SUCCESS) {
        // 解析ONNX模型输出进行干扰决策
        parse_onnx_output_for_jamming(&onnx_output, result);
        result->confidence = onnx_output.confidence;
    } else {
        // ONNX推理失败，使用备用规则决策
        fallback_jamming_decision(result);
        result->confidence = 0.5;  // 降低置信度
    }

    result->status = 0;
    strcpy(result->error_message, "Success");

    return RADAR_SUCCESS;
}

// 获取决策统计信息
RadarErrorCode radar_get_decision_statistics(DecisionEngineHandle engine,
                                           int* total_decisions,
                                           int* successful_decisions,
                                           double* average_time) {
    if (!engine || !total_decisions || !successful_decisions || !average_time) {
        return RADAR_ERROR_INVALID_PARAM;
    }

    // 模拟统计数据
    *total_decisions = 100;
    *successful_decisions = 95;
    *average_time = 12.5; // 毫秒

    return RADAR_SUCCESS;
}

// 清理决策引擎
void radar_cleanup_decision_engine(DecisionEngineHandle engine) {
    if (engine) {
        // 清理ONNX模型
        cleanup_onnx_model();
        free(engine);
    }
}

// 错误信息获取
const char* radar_get_error_message(RadarErrorCode error_code) {
    switch (error_code) {
        case RADAR_SUCCESS:
            return "Success";
        case RADAR_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case RADAR_ERROR_MEMORY_ALLOC:
            return "Memory allocation failed";
        case RADAR_ERROR_FILE_NOT_FOUND:
            return "File not found";
        case RADAR_ERROR_MODEL_LOAD:
            return "Model loading failed";
        case RADAR_ERROR_INFERENCE:
            return "Inference failed";
        case RADAR_ERROR_ENGINE_NOT_READY:
            return "Engine not ready";
        default:
            return "Unknown error";
    }
}

// 枚举值转换为字符串的辅助函数
const char* radar_get_threat_level_name(int threat_level) {
    switch (threat_level) {
        case THREAT_LEVEL_NONE:
            return "None";
        case THREAT_LEVEL_LOW:
            return "Low";
        case THREAT_LEVEL_MEDIUM:
            return "Medium";
        case THREAT_LEVEL_HIGH:
            return "High";
        case THREAT_LEVEL_CRITICAL:
            return "Critical";
        default:
            return "Unknown";
    }
}

const char* radar_get_jamming_type_name(int jamming_type) {
    switch (jamming_type) {
        case JAMMING_TYPE_NONE:
            return "None";
        case JAMMING_TYPE_NOISE:
            return "Noise";
        case JAMMING_TYPE_DECEPTION:
            return "Deception";
        case JAMMING_TYPE_BARRAGE:
            return "Barrage";
        case JAMMING_TYPE_SWEEP:
            return "Sweep";
        case JAMMING_TYPE_PULSE:
            return "Pulse";
        case JAMMING_TYPE_CHAFF:
            return "Chaff";
        case JAMMING_TYPE_FLARE:
            return "Flare";
        default:
            return "Unknown";
    }
}

const char* radar_get_work_mode_name(int work_mode) {
    switch (work_mode) {
        case WORK_MODE_SILENT:
            return "Silent";
        case WORK_MODE_SEARCH:
            return "Search";
        case WORK_MODE_TRACK:
            return "Track";
        case WORK_MODE_GUIDANCE:
            return "Guidance";
        default:
            return "Unknown";
    }
}

// 解析ONNX模型输出进行干扰决策
static void parse_onnx_output_for_jamming(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    if (!onnx_output || !result) {
        return;
    }

    // ONNX模型实际输出格式：
    // jamming_type_probs: [batch_size, 5] - 5种干扰类型的概率
    // 输出数据：[prob0, prob1, prob2, prob3, prob4]

    // 检查输出大小
    if (onnx_output->output_size < 5) {
        result->jamming_count = 0;
        return;
    }

    // 解析所有ONNX模型输出
    printf("ONNX模型输出解析 (总共%d个值):\n", onnx_output->output_size);

    // 正确的输出结构（基于模型检查结果）：
    // 输出0: jamming_type_probs [1, 5] - 5个干扰类型概率
    // 输出1: combination_scores
    // 输出2: comb_params - 梳状谱参数
    // 输出3: isrj_params - 间歇采样参数
    // 输出4: broadband_params - 宽带噪声参数
    // 输出5: smart_noise_params - 灵巧噪声参数
    // 输出6: deception_params - 拖引参数
    // 输出7-10: 其他输出

    if (onnx_output->output_size < 5) {
        printf("错误: ONNX输出数据不足\n");
        result->jamming_count = 0;
        return;
    }

    // 解析干扰类型概率（前5个值）
    printf("干扰类型概率: ");
    for (int i = 0; i < 5; i++) {
        printf("%.4f ", onnx_output->output_data[i]);
    }
    printf("\n");

    // 找到概率最高的干扰类型
    int best_jamming_type = 0;
    float best_prob = onnx_output->output_data[0];

    for (int i = 1; i < 5; i++) {
        if (onnx_output->output_data[i] > best_prob) {
            best_prob = onnx_output->output_data[i];
            best_jamming_type = i;
        }
    }

    printf("最佳干扰类型: %d, 概率: %.4f\n", best_jamming_type, best_prob);

    // 如果最高概率太低，则不进行干扰
    if (best_prob < 0.15) {  // 降低阈值
        result->jamming_count = 0;
        return;
    }

    // 检查是否需要组合干扰
    int jamming_combination_count = determine_jamming_combination(onnx_output, result, best_jamming_type, best_prob);

    if (jamming_combination_count > 1) {
        printf("使用组合干扰: %d种干扰类型\n", jamming_combination_count);
    } else {
        printf("使用单一干扰: 类型%d\n", best_jamming_type);
        // 直接使用模型的专用参数输出
        parse_direct_model_outputs(onnx_output, result, best_jamming_type);

        // 设置基本干扰策略
        result->jamming_count = 1;  // 使用一种干扰类型
        result->jamming_types[0] = best_jamming_type + 1;  // 干扰类型从1开始
    }

    // 设置基本的干扰参数（基于干扰类型）
    switch (best_jamming_type) {
        case 0:  // 组合干扰
            result->jamming_params[0][0] = 10000.0f;  // 频率
            result->jamming_params[0][1] = 50000.0f;  // 功率
            break;
        case 1:  // ISRJ干扰
            result->jamming_params[0][0] = 8000.0f;   // 频率
            result->jamming_params[0][1] = 30000.0f;  // 功率
            break;
        case 2:  // 宽带干扰
            result->jamming_params[0][0] = 12000.0f;  // 频率
            result->jamming_params[0][1] = 80000.0f;  // 功率
            break;
        case 3:  // 智能噪声
            result->jamming_params[0][0] = 9000.0f;   // 频率
            result->jamming_params[0][1] = 40000.0f;  // 功率
            break;
        case 4:  // 欺骗干扰
            result->jamming_params[0][0] = 11000.0f;  // 频率
            result->jamming_params[0][1] = 60000.0f;  // 功率
            break;
        default:
            result->jamming_params[0][0] = 10000.0f;
            result->jamming_params[0][1] = 50000.0f;
            break;
    }

    // 设置剩余参数为0
    for (int k = 2; k < 6; k++) {
        result->jamming_params[0][k] = 0.0f;
    }
}

// 备用干扰决策（当ONNX推理失败时使用）
static void fallback_jamming_decision(ExternalDecisionResult* result) {
    if (!result) {
        return;
    }

    // 基于威胁等级的简单规则决策
    if (result->threat_level <= 2) {  // 高威胁或极高威胁
        result->jamming_count = 2;
        result->jamming_types[0] = JAMMING_TYPE_NOISE;
        result->jamming_types[1] = JAMMING_TYPE_DECEPTION;

        // 设置基本参数
        result->jamming_params[0][0] = 10000.0;  // 频率
        result->jamming_params[0][1] = 100000.0; // 功率
        result->jamming_params[1][0] = 10100.0;  // 频率偏移
        result->jamming_params[1][1] = 50000.0;  // 功率
    } else if (result->threat_level <= 4) {  // 中等威胁或低威胁
        result->jamming_count = 1;
        result->jamming_types[0] = JAMMING_TYPE_NOISE;

        result->jamming_params[0][0] = 10000.0;  // 频率
        result->jamming_params[0][1] = 50000.0;  // 功率
    } else {  // 极低威胁
        result->jamming_count = 0;  // 无需干扰
    }
}

// 解析模型的所有输出参数
void parse_model_outputs(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int jamming_type) {
    if (!onnx_output || !result || onnx_output->output_size < 5) {
        return;
    }

    printf("解析模型输出参数:\n");
    printf("  选择的干扰类型: %d\n", jamming_type);

    // 解析其他输出（如果有的话）
    int param_start_idx = 5;  // 跳过前5个干扰类型概率

    if (onnx_output->output_size > param_start_idx) {
        printf("  其他模型输出参数: ");
        for (int i = param_start_idx; i < onnx_output->output_size && i < param_start_idx + 10; i++) {
            printf("%.3f ", onnx_output->output_data[i]);
        }
        printf("\n");

        // 根据干扰类型和模型输出设置参数
        set_jamming_parameters_from_model(result, jamming_type,
                                         &onnx_output->output_data[param_start_idx],
                                         onnx_output->output_size - param_start_idx);
    } else {
        // 如果没有额外参数，使用默认参数
        printf("  使用默认参数（模型输出不足）\n");
        set_default_jamming_parameters(result, jamming_type);
    }
}

// 根据模型输出设置干扰参数
void set_jamming_parameters_from_model(ExternalDecisionResult* result, int jamming_type,
                                     const float* model_params, int param_count) {
    if (!result || !model_params || param_count <= 0) {
        return;
    }

    // 设置干扰类型（转换为1-based）
    result->jamming_types[0] = jamming_type + 1;

    // 根据模型输出设置参数
    printf("  从模型输出设置参数:\n");

    // 使用模型输出的前6个参数（如果有的话）
    for (int i = 0; i < 6 && i < param_count; i++) {
        // 将模型输出转换为合理的参数值
        float raw_value = model_params[i];

        switch (i) {
            case 0: // 频率参数
                result->jamming_params[0][i] = 8000.0 + raw_value * 2000.0;  // 8-10 GHz
                break;
            case 1: // 功率参数
                result->jamming_params[0][i] = 30000.0 + fabs(raw_value) * 20000.0;  // 30-50 kW
                break;
            default: // 其他参数
                result->jamming_params[0][i] = raw_value * 100.0;  // 缩放到合理范围
                break;
        }

        printf("    参数%d: %.3f -> %.3f\n", i, raw_value, result->jamming_params[0][i]);
    }
}

// 设置默认干扰参数（备用方案）
void set_default_jamming_parameters(ExternalDecisionResult* result, int jamming_type) {
    if (!result) {
        return;
    }

    printf("  使用默认干扰参数\n");

    // 设置干扰类型（转换为1-based）
    result->jamming_types[0] = jamming_type + 1;

    // 设置默认参数
    result->jamming_params[0][0] = 9000.0;   // 默认频率 9 GHz
    result->jamming_params[0][1] = 40000.0;  // 默认功率 40 kW
    result->jamming_params[0][2] = 0.0;
    result->jamming_params[0][3] = 0.0;
    result->jamming_params[0][4] = 0.0;
    result->jamming_params[0][5] = 0.0;
}

// 直接解析模型的专用参数输出（正确的方法）
void parse_direct_model_outputs(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int jamming_type) {
    if (!onnx_output || !result || onnx_output->output_size < 5) {
        return;
    }

    printf("直接使用模型的专用参数输出:\n");
    printf("  选择的干扰类型: %d\n", jamming_type);

    // 设置干扰类型（转换为1-based）
    result->jamming_types[0] = jamming_type + 1;

    // 根据干扰类型直接使用对应的模型输出
    switch (jamming_type) {
        case 0: // 梳状谱 - 使用输出2 (comb_params)
            extract_comb_params_from_model(onnx_output, result);
            break;
        case 1: // 间歇采样 - 使用输出3 (isrj_params)
            extract_isrj_params_from_model(onnx_output, result);
            break;
        case 2: // 宽带噪声 - 使用输出4 (broadband_params)
            extract_broadband_params_from_model(onnx_output, result);
            break;
        case 3: // 灵巧噪声 - 使用输出5 (smart_noise_params)
            extract_smart_noise_params_from_model(onnx_output, result);
            break;
        case 4: // 拖引 - 使用输出6 (deception_params)
            extract_deception_params_from_model(onnx_output, result);
            break;
        default:
            printf("  未知干扰类型，使用默认参数\n");
            set_default_jamming_parameters(result, jamming_type);
            break;
    }
}

// 从模型输出中提取梳状谱参数
void extract_comb_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    printf("  提取梳状谱参数（来自模型输出2）\n");

    // 假设模型输出2包含梳状谱参数
    // 这里需要根据实际的模型输出结构来解析
    // 目前我们从总输出中估算位置

    int comb_start_idx = 5;  // 跳过前5个干扰类型概率
    if (onnx_output->output_size > comb_start_idx + 6) {
        // 直接使用模型输出的参数
        for (int i = 0; i < 6; i++) {
            result->jamming_params[0][i] = onnx_output->output_data[comb_start_idx + i];
        }

        printf("  模型输出的梳状谱参数: ");
        for (int i = 0; i < 6; i++) {
            printf("%.3f ", result->jamming_params[0][i]);
        }
        printf("\n");
    } else {
        printf("  警告: 模型输出不足，使用默认梳状谱参数\n");
        set_default_jamming_parameters(result, 0);
    }
}

// 从模型输出中提取间歇采样参数
void extract_isrj_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    printf("  提取间歇采样参数（来自模型输出3）\n");

    int isrj_start_idx = 11;  // 估算位置
    if (onnx_output->output_size > isrj_start_idx + 6) {
        // 直接使用模型输出的参数
        for (int i = 0; i < 6; i++) {
            result->jamming_params[0][i] = onnx_output->output_data[isrj_start_idx + i];
        }

        printf("  模型输出的间歇采样参数: ");
        for (int i = 0; i < 6; i++) {
            printf("%.3f ", result->jamming_params[0][i]);
        }
        printf("\n");
    } else {
        printf("  警告: 模型输出不足，使用默认间歇采样参数\n");
        set_default_jamming_parameters(result, 1);
    }
}

// 从模型输出中提取宽带噪声参数
void extract_broadband_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    printf("  提取宽带噪声参数（来自模型输出4）\n");

    int broadband_start_idx = 17;  // 估算位置
    if (onnx_output->output_size > broadband_start_idx + 1) {
        // 宽带噪声只有1个参数
        result->jamming_params[0][0] = onnx_output->output_data[broadband_start_idx];

        printf("  模型输出的宽带噪声参数: %.3f\n", result->jamming_params[0][0]);
    } else {
        printf("  警告: 模型输出不足，使用默认宽带噪声参数\n");
        set_default_jamming_parameters(result, 2);
    }
}

// 从模型输出中提取灵巧噪声参数
void extract_smart_noise_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    printf("  提取灵巧噪声参数（来自模型输出5）\n");

    int smart_start_idx = 18;  // 估算位置
    if (onnx_output->output_size > smart_start_idx + 7) {
        // 灵巧噪声有7个参数
        for (int i = 0; i < 6; i++) {  // 只存储前6个到jamming_params
            result->jamming_params[0][i] = onnx_output->output_data[smart_start_idx + i];
        }

        printf("  模型输出的灵巧噪声参数: ");
        for (int i = 0; i < 6; i++) {
            printf("%.3f ", result->jamming_params[0][i]);
        }
        printf("\n");
    } else {
        printf("  警告: 模型输出不足，使用默认灵巧噪声参数\n");
        set_default_jamming_parameters(result, 3);
    }
}

// 从模型输出中提取拖引参数
void extract_deception_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result) {
    printf("  提取拖引参数（来自模型输出6）\n");

    int deception_start_idx = 25;  // 估算位置
    if (onnx_output->output_size > deception_start_idx + 8) {
        // 拖引有8个参数，只存储前6个到jamming_params
        for (int i = 0; i < 6; i++) {
            result->jamming_params[0][i] = onnx_output->output_data[deception_start_idx + i];
        }

        printf("  模型输出的拖引参数: ");
        for (int i = 0; i < 6; i++) {
            printf("%.3f ", result->jamming_params[0][i]);
        }
        printf("\n");
    } else {
        printf("  警告: 模型输出不足，使用默认拖引参数\n");
        set_default_jamming_parameters(result, 4);
    }
}

// 确定是否使用组合干扰
int determine_jamming_combination(const ONNXOutput* onnx_output, ExternalDecisionResult* result,
                                int primary_jamming_type, float primary_prob) {
    if (!onnx_output || !result || onnx_output->output_size < 10) {
        return 1;  // 只使用单一干扰
    }

    printf("分析组合干扰可能性:\n");

    // 解析combination_scores（假设在输出1的位置）
    int combination_start_idx = 5;  // 跳过前5个干扰类型概率
    if (onnx_output->output_size <= combination_start_idx) {
        printf("  组合分数数据不足，使用单一干扰\n");
        return 1;
    }

    float combination_score = onnx_output->output_data[combination_start_idx];
    printf("  组合干扰分数: %.4f\n", combination_score);

    // 组合干扰的条件：
    // 1. 组合分数足够高
    // 2. 主要干扰类型概率不是压倒性优势
    // 3. 存在第二高的干扰类型

    bool should_use_combination = false;
    int secondary_jamming_type = -1;
    float secondary_prob = 0.0;

    // 找到第二高概率的干扰类型
    for (int i = 0; i < 5; i++) {
        if (i != primary_jamming_type && onnx_output->output_data[i] > secondary_prob) {
            secondary_prob = onnx_output->output_data[i];
            secondary_jamming_type = i;
        }
    }

    printf("  次要干扰类型: %d, 概率: %.4f\n", secondary_jamming_type, secondary_prob);

    // 组合干扰决策逻辑（调整阈值以便测试）
    if (combination_score > 0.05 && secondary_prob > 0.18 && primary_prob < 0.8) {
        should_use_combination = true;
        printf("  决策: 使用组合干扰\n");
    } else {
        printf("  决策: 使用单一干扰\n");
        printf("    原因: 组合分数%.3f, 次要概率%.3f, 主要概率%.3f\n",
               combination_score, secondary_prob, primary_prob);
    }

    if (should_use_combination && secondary_jamming_type >= 0) {
        // 设置组合干扰
        result->jamming_count = 2;
        result->jamming_types[0] = primary_jamming_type + 1;
        result->jamming_types[1] = secondary_jamming_type + 1;

        // 为两种干扰类型分别设置参数
        parse_direct_model_outputs_for_combination(onnx_output, result, primary_jamming_type, 0);
        parse_direct_model_outputs_for_combination(onnx_output, result, secondary_jamming_type, 1);

        printf("  组合干扰设置: 类型%d + 类型%d\n", primary_jamming_type, secondary_jamming_type);

        return 2;
    } else {
        return 1;
    }
}

// 为组合干扰解析模型输出
void parse_direct_model_outputs_for_combination(const ONNXOutput* onnx_output, ExternalDecisionResult* result,
                                               int jamming_type, int slot_index) {
    if (!onnx_output || !result || slot_index >= 8) {
        return;
    }

    printf("  为组合干扰槽%d设置类型%d的参数\n", slot_index, jamming_type);

    // 根据干扰类型直接使用对应的模型输出
    switch (jamming_type) {
        case 0: // 梳状谱
            extract_comb_params_for_slot(onnx_output, result, slot_index);
            break;
        case 1: // 间歇采样
            extract_isrj_params_for_slot(onnx_output, result, slot_index);
            break;
        case 2: // 宽带噪声
            extract_broadband_params_for_slot(onnx_output, result, slot_index);
            break;
        case 3: // 灵巧噪声
            extract_smart_noise_params_for_slot(onnx_output, result, slot_index);
            break;
        case 4: // 拖引
            extract_deception_params_for_slot(onnx_output, result, slot_index);
            break;
        default:
            printf("  未知干扰类型%d，使用默认参数\n", jamming_type);
            for (int i = 0; i < 6; i++) {
                result->jamming_params[slot_index][i] = 0.0;
            }
            break;
    }
}

// 为特定槽位提取梳状谱参数
void extract_comb_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index) {
    int comb_start_idx = 6;  // 估算梳状谱参数在输出中的位置
    if (onnx_output->output_size > comb_start_idx + 6) {
        for (int i = 0; i < 6; i++) {
            result->jamming_params[slot_index][i] = onnx_output->output_data[comb_start_idx + i];
        }
        printf("    梳状谱参数已设置到槽%d\n", slot_index);
    } else {
        // 使用默认参数
        result->jamming_params[slot_index][0] = 9000.0;  // 频率
        result->jamming_params[slot_index][1] = 30000.0; // 功率
        for (int i = 2; i < 6; i++) {
            result->jamming_params[slot_index][i] = 0.0;
        }
    }
}

// 为特定槽位提取间歇采样参数
void extract_isrj_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index) {
    int isrj_start_idx = 12;  // 估算间歇采样参数在输出中的位置
    if (onnx_output->output_size > isrj_start_idx + 6) {
        for (int i = 0; i < 6; i++) {
            result->jamming_params[slot_index][i] = onnx_output->output_data[isrj_start_idx + i];
        }
        printf("    间歇采样参数已设置到槽%d\n", slot_index);
    } else {
        // 使用默认参数
        result->jamming_params[slot_index][0] = 8500.0;  // 频率
        result->jamming_params[slot_index][1] = 35000.0; // 功率
        for (int i = 2; i < 6; i++) {
            result->jamming_params[slot_index][i] = 0.0;
        }
    }
}

// 为特定槽位提取宽带噪声参数
void extract_broadband_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index) {
    int broadband_start_idx = 18;  // 估算宽带噪声参数在输出中的位置
    if (onnx_output->output_size > broadband_start_idx + 1) {
        result->jamming_params[slot_index][0] = onnx_output->output_data[broadband_start_idx];
        printf("    宽带噪声参数已设置到槽%d\n", slot_index);
    } else {
        result->jamming_params[slot_index][0] = 10000.0; // 默认频率
        result->jamming_params[slot_index][1] = 25000.0; // 默认功率
    }
    for (int i = 2; i < 6; i++) {
        result->jamming_params[slot_index][i] = 0.0;
    }
}

// 为特定槽位提取灵巧噪声参数
void extract_smart_noise_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index) {
    int smart_start_idx = 19;  // 估算灵巧噪声参数在输出中的位置
    if (onnx_output->output_size > smart_start_idx + 7) {
        for (int i = 0; i < 6; i++) {  // 只存储前6个参数
            result->jamming_params[slot_index][i] = onnx_output->output_data[smart_start_idx + i];
        }
        printf("    灵巧噪声参数已设置到槽%d\n", slot_index);
    } else {
        // 使用默认参数
        result->jamming_params[slot_index][0] = 9500.0;  // 频率
        result->jamming_params[slot_index][1] = 40000.0; // 功率
        for (int i = 2; i < 6; i++) {
            result->jamming_params[slot_index][i] = 0.0;
        }
    }
}

// 为特定槽位提取拖引参数
void extract_deception_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index) {
    int deception_start_idx = 26;  // 估算拖引参数在输出中的位置
    if (onnx_output->output_size > deception_start_idx + 8) {
        for (int i = 0; i < 6; i++) {  // 只存储前6个参数
            result->jamming_params[slot_index][i] = onnx_output->output_data[deception_start_idx + i];
        }
        printf("    拖引参数已设置到槽%d\n", slot_index);
    } else {
        // 使用默认参数
        result->jamming_params[slot_index][0] = 8800.0;  // 频率
        result->jamming_params[slot_index][1] = 45000.0; // 功率
        for (int i = 2; i < 6; i++) {
            result->jamming_params[slot_index][i] = 0.0;
        }
    }
}