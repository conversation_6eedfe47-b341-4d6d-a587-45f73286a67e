function level = determine_threat_level(threat_value)
% 确定威胁等级
% 输入:
%   threat_value - 威胁度
% 输出:
%   level - 威胁等级


if threat_value > 0.792 && threat_value <= 1.000
    level = "一级威胁";
elseif threat_value > 0.655 && threat_value <= 0.792
    level = "二级威胁";
elseif threat_value > 0.421 && threat_value <= 0.655
    level = "三级威胁";
elseif threat_value > 0.210 && threat_value <= 0.421
    level = "四级威胁";
elseif threat_value >= 0.000 && threat_value <= 0.210
    level = "五级威胁";
else
    level = "无法确定威胁等级";
    warning('威胁度值超出范围: %f', threat_value);
end

end
