#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复包清理脚本
删除重复的依赖包，只保留一个副本
"""

import os
import sys
from pathlib import Path
from collections import defaultdict

def get_package_name(filename):
    """从文件名提取包名"""
    if filename.endswith('.whl'):
        # 处理wheel文件: package-version-python-abi-platform.whl
        parts = filename.split('-')
        if len(parts) >= 2:
            return parts[0].lower().replace('_', '-')
    elif filename.endswith('.tar.gz'):
        # 处理源码包: package-version.tar.gz
        name = filename.replace('.tar.gz', '')
        parts = name.split('-')
        if len(parts) >= 2:
            return parts[0].lower().replace('_', '-')
    return filename.lower()

def find_duplicates():
    """查找重复的包"""
    base_dir = Path('packages')
    dirs = ['training_packages', 'onnx_packages', 'rknn_packages']
    
    all_packages = defaultdict(list)
    
    for dir_name in dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            # 查找wheel文件
            for file in dir_path.glob('*.whl'):
                pkg_name = get_package_name(file.name)
                all_packages[pkg_name].append((dir_name, file))
            
            # 查找tar.gz文件
            for file in dir_path.glob('*.tar.gz'):
                pkg_name = get_package_name(file.name)
                all_packages[pkg_name].append((dir_name, file))
    
    # 找出重复的包
    duplicates = {}
    for pkg_name, files in all_packages.items():
        if len(files) > 1:
            duplicates[pkg_name] = files
    
    return duplicates

def choose_package_to_keep(pkg_name, files):
    """选择要保留的包版本"""
    # 优先级规则：
    # 1. 训练包优先（因为是核心功能）
    # 2. ONNX包次之
    # 3. RKNN包最后
    
    priority = {
        'training_packages': 1,
        'onnx_packages': 2, 
        'rknn_packages': 3
    }
    
    # 按优先级排序
    files_sorted = sorted(files, key=lambda x: priority.get(x[0], 999))
    
    # 特殊规则
    if pkg_name in ['torch', 'torchvision', 'torchaudio']:
        # PyTorch相关包保留在training_packages
        for dir_name, file_path in files_sorted:
            if dir_name == 'training_packages':
                return dir_name, file_path
    
    elif pkg_name in ['onnx', 'onnxruntime', 'onnxruntime-gpu']:
        # ONNX相关包保留在onnx_packages
        for dir_name, file_path in files_sorted:
            if dir_name == 'onnx_packages':
                return dir_name, file_path
    
    elif pkg_name in ['opencv-python', 'opencv-contrib-python', 'pillow']:
        # 图像处理包保留在rknn_packages
        for dir_name, file_path in files_sorted:
            if dir_name == 'rknn_packages':
                return dir_name, file_path
    
    # 默认保留第一个（优先级最高的）
    return files_sorted[0]

def remove_duplicates(dry_run=True):
    """删除重复包"""
    duplicates = find_duplicates()
    
    if not duplicates:
        print("没有发现重复的包")
        return
    
    print(f"发现 {len(duplicates)} 个重复的包")
    
    total_removed_size = 0
    removed_files = []
    
    for pkg_name, files in duplicates.items():
        print(f"\n📦 处理包: {pkg_name}")
        
        # 选择要保留的包
        keep_dir, keep_file = choose_package_to_keep(pkg_name, files)
        print(f"  ✅ 保留: {keep_dir}/{keep_file.name}")
        
        # 删除其他副本
        for dir_name, file_path in files:
            if file_path != keep_file:
                file_size = file_path.stat().st_size
                total_removed_size += file_size
                removed_files.append((dir_name, file_path.name, file_size))
                
                if dry_run:
                    print(f"  🗑️  将删除: {dir_name}/{file_path.name} ({file_size/(1024*1024):.1f}MB)")
                else:
                    try:
                        file_path.unlink()
                        print(f"  🗑️  已删除: {dir_name}/{file_path.name} ({file_size/(1024*1024):.1f}MB)")
                    except Exception as e:
                        print(f"  ❌ 删除失败: {dir_name}/{file_path.name} - {e}")
    
    print(f"\n=== 清理统计 ===")
    print(f"重复包数量: {len(duplicates)}")
    print(f"删除文件数量: {len(removed_files)}")
    print(f"节省空间: {total_removed_size/(1024*1024):.1f}MB")
    
    if dry_run:
        print(f"\n⚠️  这是预览模式，没有实际删除文件")
        print(f"要执行实际删除，请运行: python remove_duplicates.py --execute")
    else:
        print(f"\n✅ 重复包清理完成！")
    
    return removed_files

def create_shared_packages():
    """创建共享包目录"""
    shared_dir = Path('packages/shared_packages')
    shared_dir.mkdir(exist_ok=True)
    
    print(f"创建共享包目录: {shared_dir}")
    
    # 将常用包移动到共享目录
    common_packages = [
        'numpy', 'pandas', 'pyyaml', 'tqdm', 'typing-extensions',
        'cerberus', 'python-dotenv', 'requests', 'pytest'
    ]
    
    duplicates = find_duplicates()
    moved_count = 0
    
    for pkg_name in common_packages:
        if pkg_name in duplicates:
            files = duplicates[pkg_name]
            # 选择要保留的文件
            keep_dir, keep_file = choose_package_to_keep(pkg_name, files)
            
            # 移动到共享目录
            shared_file = shared_dir / keep_file.name
            if not shared_file.exists():
                try:
                    keep_file.rename(shared_file)
                    print(f"移动到共享目录: {pkg_name}")
                    moved_count += 1
                except Exception as e:
                    print(f"移动失败: {pkg_name} - {e}")
    
    print(f"共移动 {moved_count} 个包到共享目录")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理重复的依赖包')
    parser.add_argument('--execute', action='store_true', help='执行实际删除（默认为预览模式）')
    parser.add_argument('--create-shared', action='store_true', help='创建共享包目录')
    
    args = parser.parse_args()
    
    if args.create_shared:
        create_shared_packages()
        return
    
    # 检查packages目录是否存在
    if not Path('packages').exists():
        print("错误: packages目录不存在")
        return 1
    
    # 清理重复包
    dry_run = not args.execute
    removed_files = remove_duplicates(dry_run)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
