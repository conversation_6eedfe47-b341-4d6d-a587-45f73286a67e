"""
雷达干扰系统集成模块
实现模块化的闭环干扰系统

闭环系统架构：
雷达模块 → 威胁评估 → 智能决策 → 干扰执行 → 效果评估 → 反馈学习 → 循环
"""

# 数据结构
from .data_structures import (
    RadarEmissionData, ThreatAssessmentData, JammingDecisionData,
    JammingExecutionData, EffectivenessData, FeedbackData,
    SystemCycleData, SystemConfiguration, SystemMode, JammingStrategy
)

# 接口定义
from .interfaces import (
    IRadarModule, IThreatEvaluator, IJammingDecisionModule,
    IJammingExecutor, IEffectivenessEvaluator, IFeedbackProcessor,
    ISystemController
)

# 核心模块（只导入存在的模块）
from .intelligent_decision_module import IntelligentDecisionModule

# 可选模块已移除，这些适配器模块不存在且未使用

__all__ = [
    # 数据结构
    'RadarEmissionData',
    'ThreatAssessmentData',
    'JammingDecisionData',
    'JammingExecutionData',
    'EffectivenessData',
    'FeedbackData',
    'SystemCycleData',
    'SystemConfiguration',
    'SystemMode',
    'JammingStrategy',

    # 接口
    'IRadarModule',
    'IThreatEvaluator',
    'IJammingDecisionModule',
    'IJammingExecutor',
    'IEffectivenessEvaluator',
    'IFeedbackProcessor',
    'ISystemController',

    # 核心实现类
    'IntelligentDecisionModule'
]
