#ifndef EXTERNAL_API_H
#define EXTERNAL_API_H

#include "data_structures.h"

#ifdef __cplusplus
extern "C" {
#endif

// 决策引擎句柄类型
typedef void* DecisionEngineHandle;

// 版本信息
const char* radar_get_version(void);

// 初始化决策引擎
DecisionEngineHandle radar_init_decision_engine(const char* model_path);

// 配置决策引擎
RadarErrorCode radar_config_decision_engine(DecisionEngineHandle engine,
                                          int enable_threat_assessment,
                                          int enable_model_inference,
                                          double confidence_threshold);

// 检查引擎是否就绪
int radar_is_engine_ready(DecisionEngineHandle engine);

// 验证雷达参数
int radar_validate_parameters(const ExternalRadarParams* params);

// 执行干扰决策
RadarErrorCode radar_execute_jamming_decision(DecisionEngineHandle engine,
                                            const ExternalRadarParams* radar_params,
                                            ExternalDecisionResult* result);

// 获取决策统计信息
RadarErrorCode radar_get_decision_statistics(DecisionEngineHandle engine,
                                           int* total_decisions,
                                           int* successful_decisions,
                                           double* average_time);

// 清理决策引擎
void radar_cleanup_decision_engine(DecisionEngineHandle engine);

// 错误信息获取
const char* radar_get_error_message(RadarErrorCode error_code);

// 枚举值转换为字符串的辅助函数
const char* radar_get_threat_level_name(int threat_level);
const char* radar_get_jamming_type_name(int jamming_type);
const char* radar_get_work_mode_name(int work_mode);

// 内部函数声明（用于模型输出解析）
void parse_model_outputs(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int jamming_type);
void parse_direct_model_outputs(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int jamming_type);
void set_jamming_parameters_from_model(ExternalDecisionResult* result, int jamming_type,
                                     const float* model_params, int param_count);
void set_default_jamming_parameters(ExternalDecisionResult* result, int jamming_type);

// 专用参数提取函数
void extract_comb_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result);
void extract_isrj_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result);
void extract_broadband_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result);
void extract_smart_noise_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result);
void extract_deception_params_from_model(const ONNXOutput* onnx_output, ExternalDecisionResult* result);

// 组合干扰相关函数
int determine_jamming_combination(const ONNXOutput* onnx_output, ExternalDecisionResult* result,
                                int primary_jamming_type, float primary_prob);
void parse_direct_model_outputs_for_combination(const ONNXOutput* onnx_output, ExternalDecisionResult* result,
                                               int jamming_type, int slot_index);
void extract_comb_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index);
void extract_isrj_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index);
void extract_broadband_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index);
void extract_smart_noise_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index);
void extract_deception_params_for_slot(const ONNXOutput* onnx_output, ExternalDecisionResult* result, int slot_index);

#ifdef __cplusplus
}
#endif

#endif // EXTERNAL_API_H