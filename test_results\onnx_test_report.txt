================================================================================
ONNX模型输出格式测试报告
================================================================================
模型文件: models/onnx_fixed/jamming_model_ppo.onnx
测试时间: 2025-07-19T09:28:00

验证结果摘要:
  总体状态: ✅ 通过

输出统计信息:
  jamming_type_probs:
    形状: (10, 5)
    数值范围: [0.170, 0.239]
    均值±标准差: 0.200±0.018
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

  comb_params:
    形状: (10, 25)
    数值范围: [-0.449, 0.498]
    均值±标准差: 0.076±0.228
    范围类型: tanh_like (-1 to 1)

  isrj_params:
    形状: (10, 6)
    数值范围: [-0.326, 0.455]
    均值±标准差: 0.065±0.238
    范围类型: tanh_like (-1 to 1)

  broadband_params:
    形状: (10, 1)
    数值范围: [-0.575, -0.231]
    均值±标准差: -0.395±0.103
    范围类型: tanh_like (-1 to 1)

  smart_noise_params:
    形状: (10, 7)
    数值范围: [-0.229, 0.372]
    均值±标准差: 0.039±0.144
    范围类型: tanh_like (-1 to 1)

  deception_params:
    形状: (10, 3)
    数值范围: [-0.212, 0.308]
    均值±标准差: 0.037±0.144
    范围类型: tanh_like (-1 to 1)

  threat_level_probs:
    形状: (10, 5)
    数值范围: [0.158, 0.251]
    均值±标准差: 0.200±0.025
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

  jamming_count_probs:
    形状: (10, 4)
    数值范围: [0.201, 0.314]
    均值±标准差: 0.250±0.032
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

  jamming1_probs:
    形状: (10, 5)
    数值范围: [0.180, 0.220]
    均值±标准差: 0.200±0.011
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

  jamming2_probs:
    形状: (10, 5)
    数值范围: [0.174, 0.228]
    均值±标准差: 0.200±0.014
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

  jamming3_probs:
    形状: (10, 5)
    数值范围: [0.160, 0.226]
    均值±标准差: 0.200±0.019
    概率分布: 是
    范围类型: tanh_like (-1 to 1)

性能信息:
  平均推理时间: 0.14 ms
  推理时间范围: [0.00, 1.45] ms
  成功率: 100.0%
