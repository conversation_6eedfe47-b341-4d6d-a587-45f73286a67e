QT -= gui
QT += core

CONFIG += c++11 console
CONFIG -= app_bundle

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = RadarJammingQt

# 包含路径
INCLUDEPATH += ../include
INCLUDEPATH += ../third_party/onnxruntime/include

# 库路径
LIBS += -L../third_party/onnxruntime/lib -lonnxruntime

# 检查ONNX Runtime是否存在
exists(../third_party/onnxruntime/lib/onnxruntime.lib) {
    DEFINES += HAS_ONNXRUNTIME=1
    message("Found ONNX Runtime, enabling real inference...")
} else {
    DEFINES += HAS_ONNXRUNTIME=0
    message("ONNX Runtime not found, using simulation mode...")
}

# 源文件
SOURCES += \
    main.cpp \
    ../src/external_api.cpp \
    ../src/data_structures.cpp \
    ../src/jamming_decision.cpp \
    ../src/onnx_inference.cpp \
    ../src/threat_evaluator.cpp

# 头文件
HEADERS += \
    ../include/external_api.h \
    ../include/data_structures.h \
    ../include/jamming_decision.h \
    ../include/onnx_inference.h \
    ../include/threat_evaluator.h

# Windows特定配置
win32 {
    # 复制ONNX Runtime DLL到输出目录
    exists(../third_party/onnxruntime/lib/onnxruntime.dll) {
        QMAKE_POST_LINK += $$quote(copy /Y $$shell_path($$PWD/../third_party/onnxruntime/lib/onnxruntime.dll) $$shell_path($$OUT_PWD/))
    }
    
    # 设置控制台子系统
    CONFIG += console
}

# 默认部署规则
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
