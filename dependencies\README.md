# 雷达电子战智能干扰决策系统 - 依赖包管理

## 概述

本目录包含了雷达电子战智能干扰决策系统三个主要阶段的依赖包管理工具：

1. **训练阶段** - 用于PPO强化学习模型训练
2. **ONNX转换阶段** - 用于PyTorch模型转换为ONNX格式
3. **RKNN部署阶段** - 用于ONNX模型转换为RKNN格式并部署

## 文件结构

```
dependencies/
├── training_requirements.txt          # 训练阶段依赖包列表
├── onnx_requirements.txt             # ONNX转换阶段依赖包列表
├── rknn_requirements.txt             # RKNN部署阶段依赖包列表
├── download_training_packages.py     # 训练依赖包下载脚本
├── download_onnx_packages.py         # ONNX依赖包下载脚本
├── download_rknn_packages.py         # RKNN依赖包下载脚本
├── manage_dependencies.py            # 统一依赖包管理脚本
└── README.md                         # 本说明文档
```

## 快速开始

### 1. 下载所有依赖包

```bash
# 进入dependencies目录
cd dependencies

# 下载所有阶段的依赖包
python manage_dependencies.py --download-all

# 创建目录结构和文档
python manage_dependencies.py --create-structure
```

### 2. 下载特定阶段的依赖包

```bash
# 下载训练阶段依赖包
python manage_dependencies.py --stage training

# 下载ONNX转换阶段依赖包
python manage_dependencies.py --stage onnx

# 下载RKNN部署阶段依赖包
python manage_dependencies.py --stage rknn
```

### 3. 使用单独的下载脚本

```bash
# 训练阶段
python download_training_packages.py --requirements training_requirements.txt --download-dir training_packages

# ONNX转换阶段
python download_onnx_packages.py --requirements onnx_requirements.txt --download-dir onnx_packages --include-tools

# RKNN部署阶段
python download_rknn_packages.py --requirements rknn_requirements.txt --download-dir rknn_packages --include-opencv
```

## 各阶段依赖说明

### 训练阶段依赖 (training_requirements.txt)

**核心框架:**
- `torch>=1.8.0` - PyTorch深度学习框架
- `torchvision>=0.9.0` - PyTorch视觉库
- `cupy-cuda11x>=9.0.0` - GPU加速计算库

**科学计算:**
- `numpy>=1.19.0` - 数值计算
- `scipy>=1.6.0` - 科学计算
- `matplotlib>=3.3.0` - 数据可视化

**机器学习:**
- `scikit-learn>=0.24.0` - 机器学习工具
- `optuna>=2.7.0` - 超参数优化

**工具库:**
- `tqdm>=4.60.0` - 进度条
- `pandas>=1.2.0` - 数据处理
- `pyyaml>=5.4.0` - 配置文件

### ONNX转换阶段依赖 (onnx_requirements.txt)

**ONNX核心:**
- `onnx>=1.10.0` - ONNX核心库
- `onnxruntime>=1.8.0` - ONNX运行时
- `onnxruntime-gpu>=1.8.0` - ONNX GPU运行时

**模型优化:**
- `onnx-simplifier>=0.3.6` - 模型简化
- `onnxoptimizer>=0.2.6` - 模型优化
- `netron>=5.0.0` - 模型可视化

**转换工具:**
- `torch>=1.8.0` - PyTorch(用于模型加载)
- `tf2onnx>=1.8.0` - TensorFlow转ONNX(可选)

### RKNN部署阶段依赖 (rknn_requirements.txt)

**RKNN工具包:**
- `rknn-toolkit2>=1.4.0` - 瑞芯微官方工具包(需手动下载)
- `rknn-toolkit-lite2>=1.4.0` - 轻量版工具包

**图像处理:**
- `opencv-python>=4.5.0` - OpenCV图像处理
- `Pillow>=8.0.0` - 图像处理库

**系统工具:**
- `psutil>=5.8.0` - 系统监控
- `nvidia-ml-py3>=7.352.0` - GPU监控

## 安装方法

### 方法1: 在线安装

```bash
# 直接安装requirements文件
pip install -r training_requirements.txt
pip install -r onnx_requirements.txt
pip install -r rknn_requirements.txt
```

### 方法2: 离线安装

```bash
# 1. 先下载包到本地
python download_training_packages.py --download-dir training_packages

# 2. 从本地安装
pip install --find-links training_packages --no-index -r training_requirements.txt
```

### 方法3: 使用安装脚本

```bash
# 下载完成后会自动生成安装脚本
bash packages/install_all_dependencies.sh
```

## 镜像源配置

### 使用清华大学镜像源

```bash
python manage_dependencies.py --download-all --index-url https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 使用阿里云镜像源

```bash
python manage_dependencies.py --download-all --index-url https://mirrors.aliyun.com/pypi/simple/
```

### 使用中科大镜像源

```bash
python manage_dependencies.py --download-all --index-url https://pypi.mirrors.ustc.edu.cn/simple/
```

## 特殊说明

### RKNN工具包安装

RKNN工具包需要从瑞芯微官网手动下载：

1. 访问: https://github.com/rockchip-linux/rknn-toolkit2
2. 下载对应版本的.whl文件
3. 将文件放入`rknn_packages`目录
4. 运行安装脚本

### GPU支持

**CUDA版本对应:**
- CUDA 10.x: `cupy-cuda10x`
- CUDA 11.x: `cupy-cuda11x`
- CUDA 12.x: `cupy-cuda12x`

**检查CUDA版本:**
```bash
nvcc --version
nvidia-smi
```

### 虚拟环境建议

```bash
# 创建虚拟环境
python -m venv jamming_env

# 激活虚拟环境
# Windows:
jamming_env\Scripts\activate
# Linux/Mac:
source jamming_env/bin/activate

# 安装依赖
pip install -r training_requirements.txt
```

## 故障排除

### 常见问题

1. **网络连接问题**
   - 使用国内镜像源
   - 检查防火墙设置
   - 使用代理

2. **CUDA版本不匹配**
   - 检查CUDA版本
   - 安装对应版本的CuPy
   - 使用CPU版本

3. **权限问题**
   - 使用虚拟环境
   - 添加`--user`参数
   - 使用管理员权限

4. **依赖冲突**
   - 使用虚拟环境
   - 更新pip版本
   - 逐个安装包

### 获取帮助

```bash
# 查看脚本帮助
python manage_dependencies.py --help
python download_training_packages.py --help
python download_onnx_packages.py --help
python download_rknn_packages.py --help
```

## 版本兼容性

- **Python**: 3.8-3.10 (推荐3.9)
- **PyTorch**: 1.8+ (推荐1.12+)
- **ONNX**: 1.10+ (推荐1.12+)
- **CUDA**: 10.2+ (推荐11.6+)

## 更新日志

- v1.0.0: 初始版本，支持三阶段依赖管理
- v1.1.0: 添加镜像源支持和安装脚本
- v1.2.0: 优化RKNN工具包安装流程

---

如有问题，请查看项目文档或提交Issue。
