function [membership_prf,membership_freq,membership_pw,membership_mode] = work_membership(prf,freq,pw,work_mode)
% 计算参数与工作模式的隶属度
% 输入:
%   prf - 脉冲重复频率 (单位: kHz)
%   freq - 载频 (单位: GHz)
%   pw - 脉宽 (单位: us)
%   work_mode - 工作模式
% 输出:
%   membership - 隶属度值

%重频
if prf <= 0.1
    membership_prf = 0;
else
    membership_prf = 1 - exp(-5 * (prf - 0.1)^2);
end

%载频
if freq > 8
    membership_freq = 1;
elseif freq > 2 && freq <= 8
    membership_freq = 0.6;
elseif freq > 0.3 && freq <= 2
    membership_freq = 0.3;
elseif freq > 0.03 && freq <= 0.3
    membership_freq = 0.1;
else
    membership_freq = 0;  
end

%脉宽
membership_pw = 1 / (1 + (0.1 * pw)^2);

%工作模式
if contains(work_mode, "制导模式")
    membership_mode = 1.0;
elseif contains(work_mode, "跟踪模式")
    membership_mode = 0.8;
elseif contains(work_mode, "成像模式")
    membership_mode = 0.5;
elseif contains(work_mode, "搜索模式")
    membership_mode = 0.2;
else
    membership_mode = 0.0;
end

end