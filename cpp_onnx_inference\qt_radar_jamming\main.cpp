#include <iostream>
#include <cmath>
#include <thread>
#include <chrono>
#include "external_api.h"

#ifdef _WIN32
#include <windows.h>
#define SLEEP_MS(ms) Sleep(ms)
#else
#include <unistd.h>
#define SLEEP_MS(ms) usleep((ms) * 1000)
#endif

// 前向声明
const char* get_work_mode_name(int work_mode);
const char* get_threat_level_desc(int threat_level);
const char* get_jamming_type_name(int jamming_type);
int generate_numerical_output(const ExternalDecisionResult* result, int* output, int max_size);
void explain_numerical_output_brief(const int* numerical_output, int size);
int add_real_jamming_params(int jamming_id, const double* params, int* output, int start_idx, int max_size);
void explain_jamming_details(const int* numerical_output, int size);
int explain_jamming_type_params(int jamming_id, const int* numerical_output, int start_idx, int size);

// 模拟硬件接口发送干扰指令
void send_jamming_command(int jamming_type, const double* params, int param_count) {
    std::cout << "    -> Send jamming command:" << std::endl;
    std::cout << "      Jamming type: " << get_jamming_type_name(jamming_type) 
              << " (code " << jamming_type << ")" << std::endl;
    std::cout << "      Parameter count: " << param_count << std::endl;
    std::cout << "      Parameter values: ";
    for (int i = 0; i < param_count; i++) {
        std::cout << params[i] << " ";
    }
    std::cout << std::endl;
}

// 模拟雷达参数输入
void simulate_radar_input(ExternalRadarParams* radar) {
    static int scenario = 0;
    
    switch (scenario) {
        case 0: // High threat scenario
            radar->frequency = 8000.0;   // 8 GHz
            radar->pulse_width = 2.0;    // 2 us
            radar->prt = 2000.0;         // 2000 us
            radar->power = 500000.0;     // 500 kW
            radar->distance = 80.0;      // 80 km
            radar->speed = 200.0;        // 200 m/s
            radar->direction = 30.0;     // 30°
            radar->work_mode = 1;        // Search mode
            break;
            
        case 1: // Medium threat scenario
            radar->frequency = 5000.0;   // 5 GHz
            radar->pulse_width = 5.0;    // 5 us
            radar->prt = 5000.0;         // 5000 us
            radar->power = 200000.0;     // 200 kW
            radar->distance = 120.0;     // 120 km
            radar->speed = 100.0;        // 100 m/s
            radar->direction = 15.0;     // 15°
            radar->work_mode = 1;        // Search mode
            break;
            
        case 2: // High frequency scenario (for comb spectrum test)
            radar->frequency = 12000.0;  // 12 GHz - high frequency
            radar->pulse_width = 0.5;    // 0.5 us - short pulse
            radar->prt = 800.0;          // 800 us
            radar->power = 2000000.0;    // 2 MW - high power
            radar->distance = 30.0;      // 30 km - close range
            radar->speed = 500.0;        // 500 m/s - high speed
            radar->direction = 60.0;     // 60 degrees
            radar->work_mode = 3;        // Imaging mode
            break;
            
        case 3: // Low threat scenario
            radar->frequency = 3000.0;   // 3 GHz
            radar->pulse_width = 10.0;   // 10 us
            radar->prt = 10000.0;        // 10000 us
            radar->power = 50000.0;      // 50 kW
            radar->distance = 200.0;     // 200 km
            radar->speed = 50.0;         // 50 m/s
            radar->direction = 0.0;      // 0°
            radar->work_mode = 0;        // Silent mode
            break;
            
        case 4: // Track mode scenario
            radar->frequency = 10000.0;  // 10 GHz
            radar->pulse_width = 1.0;    // 1 us
            radar->prt = 1000.0;         // 1000 us
            radar->power = 1000000.0;    // 1 MW
            radar->distance = 50.0;      // 50 km
            radar->speed = 300.0;        // 300 m/s
            radar->direction = 45.0;     // 45°
            radar->work_mode = 2;        // Track mode
            break;
    }
    
    scenario = (scenario + 1) % 5;

    std::cout << "\nProcessing radar input" << std::endl;
    std::cout << "   Frequency: " << radar->frequency << " MHz" << std::endl;
    std::cout << "   Pulse width: " << radar->pulse_width << " μs" << std::endl;
    double prf = 1e6 / radar->prt;  // Calculate PRF
    std::cout << "   PRF: " << prf << " Hz" << std::endl;
    std::cout << "   PRT: " << radar->prt << " μs" << std::endl;
    std::cout << "   Distance: " << radar->distance << " km" << std::endl;
    std::cout << "   Speed: " << radar->speed << " m/s" << std::endl;
    std::cout << "   Direction: " << radar->direction << "°" << std::endl;
    std::cout << "   Work mode: " << radar->work_mode << " (" << get_work_mode_name(radar->work_mode) << ")" << std::endl;
}

// 显示决策结果
void display_decision_result(const ExternalDecisionResult* result) {
    // 生成标准化输出
    int numerical_output[32];
    int output_size = generate_numerical_output(result, numerical_output, 32);
    
    // 显示决策信息
    if (result->jamming_count > 0) {
        std::cout << "   Decision: Execute jamming (type " << result->jamming_types[0] << ")" << std::endl;
        std::cout << "   Power level: " << result->jamming_params[0][1] << std::endl;
        std::cout << "   Jamming effect: 0.85" << std::endl;  // Simulated effect value
    } else {
        std::cout << "   Decision: No jamming required" << std::endl;
    }
    
    // 显示标准输出格式
    std::cout << "   📋 Standard output: [";
    for (int i = 0; i < output_size; i++) {
        std::cout << numerical_output[i];
        if (i < output_size - 1) std::cout << ", ";
    }
    std::cout << "]" << std::endl;
    
    // 解释标准输出格式
    explain_numerical_output_brief(numerical_output, output_size);
    
    std::cout << "   Decision confidence: " << result->confidence << std::endl;
    std::cout << "   Processing time: 12.5ms" << std::endl;  // Simulated processing time

    if (result->jamming_count > 0) {
        std::cout << "\nJamming strategy details:" << std::endl;
        for (int i = 0; i < result->jamming_count; i++) {
            std::cout << "  Strategy " << (i + 1) << ": " 
                      << get_jamming_type_name(result->jamming_types[i]) 
                      << " (type " << result->jamming_types[i] << ")" << std::endl;
            
            // 显示主要参数
            if (result->jamming_params[i][0] != 0.0 || result->jamming_params[i][1] != 0.0) {
                std::cout << "    Jamming frequency: " << result->jamming_params[i][0] << " MHz" << std::endl;
                std::cout << "    Jamming power: " << result->jamming_params[i][1] << " W" << std::endl;
            }
            
            // 发送干扰指令模拟
            send_jamming_command(result->jamming_types[i],
                               result->jamming_params[i], 6);
        }
    } else {
        std::cout << "\nDecision result: No jamming required" << std::endl;
    }

    if (result->status != 0) {
        std::cout << "Error message: " << result->error_message << std::endl;
    }
}

void runRadarJammingSystem() {
    std::cout << "=== Intelligent Jamming Decision System ===" << std::endl;
    std::cout << "System version: " << radar_get_version() << std::endl;

    // Initialize decision engine
    RadarDecisionEngine* engine = radar_init_decision_engine("../models/jamming_decision.onnx");
    if (!engine) {
        std::cout << "Error: Failed to initialize decision engine" << std::endl;
        return;
    }

        std::cout << "Complete intelligent jamming decision system initialization completed" << std::endl;
        std::cout << "   Output interpretation mode: Enabled" << std::endl;
        std::cout << "   Detailed logging: Enabled" << std::endl;
        std::cout << "   GPU acceleration: Disabled" << std::endl;

        // Configure decision engine
        RadarErrorCode config_result = radar_config_decision_engine(
            engine,
            1,      // Enable threat assessment
            1,      // Enable model inference
            0.7     // Confidence threshold
        );

    if (config_result != RADAR_SUCCESS) {
        std::cout << "Error: Decision engine configuration failed - "
                  << radar_get_error_message(config_result) << std::endl;
        radar_cleanup_decision_engine(engine);
        return;
    }

        std::cout << "System module initialization completed" << std::endl;

        // Check engine status
        if (!radar_is_engine_ready(engine)) {
            std::cout << "Warning: Decision engine not ready" << std::endl;
        }

        // Simulate real-time decision loop
        std::cout << "\nStarting real-time decision loop..." << std::endl;

        for (int cycle = 0; cycle < 5; cycle++) {
            std::cout << "\n--- Test cycle " << (cycle + 1) << " ---" << std::endl;

            // Simulate radar parameter input
            ExternalRadarParams radar_params;
            simulate_radar_input(&radar_params);

            // Validate radar parameters
            if (!radar_validate_parameters(&radar_params)) {
                std::cout << "Warning: Radar parameter validation failed" << std::endl;
                continue;
            }

            // Execute jamming decision
            ExternalDecisionResult decision_result;
            RadarErrorCode decision_status = radar_execute_jamming_decision(
                engine, &radar_params, &decision_result);

            if (decision_status == RADAR_SUCCESS) {
                display_decision_result(&decision_result);
            } else {
                std::cout << "Error: Decision execution failed - " 
                          << radar_get_error_message(decision_status) << std::endl;
            }

            // Simulate processing delay
            QThread::msleep(1000);
        }

        // Get statistics information
        int total_decisions, successful_decisions;
        double average_time;
        RadarErrorCode stats_result = radar_get_decision_statistics(
            engine, &total_decisions, &successful_decisions, &average_time);

        if (stats_result == RADAR_SUCCESS) {
            std::cout << "\n=== Test completed ===" << std::endl;
            std::cout << "Test scenarios: 5" << std::endl;
            std::cout << "Successful decisions: " << successful_decisions << std::endl;
            std::cout << "Success rate: " 
                      << (total_decisions > 0 ? (100.0 * successful_decisions / total_decisions) : 0.0) 
                      << "%" << std::endl;
            std::cout << "Average processing time: " << average_time << "ms" << std::endl;
        }

        // Cleanup resources
        radar_cleanup_decision_engine(engine);
        std::cout << "\nSystem test completed" << std::endl;

        // Exit application
        QCoreApplication::quit();
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    RadarJammingSystem system;
    
    // Start the system after event loop starts
    QTimer::singleShot(0, &system, &RadarJammingSystem::runSystem);

    return app.exec();
}

// ========== 辅助函数实现 ==========

// 获取工作模式名称
const char* get_work_mode_name(int work_mode) {
    switch (work_mode) {
        case 0: return "Silent";
        case 1: return "Search";
        case 2: return "Track";
        case 3: return "Imaging";
        case 4: return "Guidance";
        default: return "Unknown";
    }
}

// 获取威胁等级描述
const char* get_threat_level_desc(int threat_level) {
    switch (threat_level) {
        case 1: return "Critical";
        case 2: return "High";
        case 3: return "Medium";
        case 4: return "Low";
        case 5: return "Minimal";
        default: return "Unknown";
    }
}

// 获取干扰类型名称
const char* get_jamming_type_name(int jamming_type) {
    switch (jamming_type) {
        case 1: return "Combined";
        case 2: return "ISRJ";
        case 3: return "Broadband";
        case 4: return "Smart Noise";
        case 5: return "Deception";
        default: return "Unknown";
    }
}

// 生成标准化数值输出（使用真实模型参数）
int generate_numerical_output(const ExternalDecisionResult* result, int* output, int max_size) {
    if (!result || !output || max_size < 2) {
        return 0;
    }

    int idx = 0;

    // [0] 威胁等级
    output[idx++] = result->threat_level;

    // [1] 干扰数量
    output[idx++] = result->jamming_count;

    // 如果有干扰，添加每种干扰的详细参数
    if (result->jamming_count > 0) {
        for (int i = 0; i < result->jamming_count && i < 8; i++) {
            if (idx + 2 >= max_size) break;

            // 干扰编号
            output[idx++] = i;

            // 干扰类型ID (转换为0-4的ID)
            int jamming_id = result->jamming_types[i] - 1;  // 转换为0-based
            if (jamming_id < 0) jamming_id = 0;
            if (jamming_id > 4) jamming_id = 4;
            output[idx++] = jamming_id;

            // 根据干扰类型添加真实的模型参数
            idx = add_real_jamming_params(jamming_id, result->jamming_params[i], output, idx, max_size);
        }
    }

    return idx;
}

// 根据真实模型输出添加干扰参数
int add_real_jamming_params(int jamming_id, const double* params, int* output, int start_idx, int max_size) {
    int idx = start_idx;

    std::cout << "  Using real model parameters to generate standard output:" << std::endl;
    std::cout << "    Jamming type ID: " << jamming_id << std::endl;
    std::cout << "    Model parameters: ";
    for (int i = 0; i < 6; i++) {
        std::cout << params[i] << " ";
    }
    std::cout << std::endl;

    // 模拟威胁等级和功率（从模型参数推导）
    int threat_level = 3;  // 默认中等威胁
    double power = 0.8;    // 默认功率

    // 从模型参数调整威胁等级和功率
    if (fabs(params[0]) > 0.3) threat_level = 2;  // 高威胁
    if (fabs(params[1]) > 0.3) power = 0.9;       // 高功率

    switch (jamming_id) {
        case 0: // 梳状谱 - 动态数量
            {
                int count = 6 - threat_level;
                if (count < 1) count = 1;
                if (count > 8) count = 8;

                if (idx + 1 + count * 3 <= max_size) {
                    output[idx++] = count;

                    // 频偏序列
                    for (int i = 0; i < count; i++) {
                        output[idx++] = 200 + i * 100;  // kHz
                    }
                    // 闪烁周期序列
                    for (int i = 0; i < count; i++) {
                        output[idx++] = 20 + i * 5;  // μs
                    }
                    // 闪烁保持时间序列
                    for (int i = 0; i < count; i++) {
                        output[idx++] = 15 + i * 3;  // μs
                    }

                    std::cout << "    Generated comb spectrum parameters: count=" << count << std::endl;
                }
            }
            break;

        case 1: // 间歇采样转发 - 6个固定参数
            if (idx + 6 <= max_size) {
                output[idx++] = (int)(125.0 / threat_level);  // 重复转发时间间隔
                output[idx++] = (power > 0.5) ? 1 : 0;        // 间歇采样开关
                output[idx++] = (int)(1.5 * power);           // 间歇采样周期
                output[idx++] = (int)(0.8 + 0.4 * power);     // 间歇采样宽度
                output[idx++] = (int)(60000.0 - threat_level * 10000);  // 干扰覆盖距离范围
                output[idx++] = (int)(1.2 * power);           // 脉冲采样长度

                std::cout << "    Generated ISRJ parameters: 6 fixed parameters" << std::endl;
            }
            break;

        case 2: // 宽带阻塞噪声 - 1个参数
            if (idx + 1 <= max_size) {
                int bandwidth_selection = (6 - threat_level) * 4;
                if (bandwidth_selection < 0) bandwidth_selection = 0;
                if (bandwidth_selection > 20) bandwidth_selection = 20;
                output[idx++] = bandwidth_selection;

                std::cout << "    Generated broadband noise parameters: bandwidth=" << bandwidth_selection << std::endl;
            }
            break;

        case 3: // 灵巧噪声 - 7个参数
            if (idx + 7 <= max_size) {
                output[idx++] = (6 - threat_level) * 3;       // 噪声带宽选择
                output[idx++] = (threat_level >= 3) ? 1 : 2;  // 噪声源选择
                output[idx++] = (threat_level <= 3) ? 2 : 1;  // 多普勒闪烁模式
                output[idx++] = (int)(10.0 * power);          // 闪烁保持时间
                output[idx++] = (int)(1.0 + power);           // 闪烁消失时间
                output[idx++] = (int)(50.0 * power);          // 多普勒噪声带宽
                output[idx++] = (int)(10.0 * power);          // 多普勒噪声跳变周期

                std::cout << "    Generated smart noise parameters: 7 parameters" << std::endl;
            }
            break;

        case 4: // 拖引 - 8个参数
            if (idx + 8 <= max_size) {
                output[idx++] = (int)(450.0 + (5 - threat_level) * 50);  // 速拖速度
                output[idx++] = (int)(290.0 + power * 100);              // 速拖加速度
                output[idx++] = (int)(20.0 * power);                     // 距拖速度
                output[idx++] = (int)(15.0 + power * 10);                // 距拖加速度
                output[idx++] = (int)(1.6 * power);                      // 捕获时间
                output[idx++] = (int)(3.5 + power);                      // 拖引时间
                output[idx++] = (int)(2.4 * power);                      // 保持时间
                output[idx++] = (int)(0.8 + power * 0.5);                // 消失时间

                std::cout << "    Generated deception parameters: 8 parameters" << std::endl;
            }
            break;

        default:
            if (idx < max_size) output[idx++] = 0;
            break;
    }

    return idx;
}

// 简要解释标准输出格式
void explain_numerical_output_brief(const int* numerical_output, int size) {
    if (size < 2) {
        return;
    }

    int threat_level = numerical_output[0];
    int jamming_count = numerical_output[1];

    std::cout << "   📊 Format analysis: threat_level=" << threat_level
              << ", jamming_count=" << jamming_count << std::endl;

    if (jamming_count > 0 && size > 2) {
        std::cout << "   📋 Standardized output interpretation:" << std::endl;
        std::cout << "     Numerical output: [";
        for (int i = 0; i < size; i++) {
            std::cout << numerical_output[i];
            if (i < size - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
        std::cout << "     Format description:" << std::endl;
        std::cout << "       [0] Threat level: " << threat_level << " (1=Critical, 5=Minimal)" << std::endl;
        std::cout << "       [1] Jamming count: " << jamming_count << std::endl;

        if (jamming_count > 0) {
            explain_jamming_details(numerical_output, size);
        } else {
            std::cout << "       No jamming, output ends" << std::endl;
        }
    }
    std::cout << std::endl;
}

// 解释干扰详细参数
void explain_jamming_details(const int* numerical_output, int size) {
    std::cout << "       Jamming type ID mapping: 0=Comb, 1=ISRJ, 2=Broadband, 3=Smart, 4=Deception" << std::endl;

    int jamming_count = numerical_output[1];
    int idx = 2;  // 从第2个位置开始解析

    for (int i = 0; i < jamming_count && idx + 1 < size; i++) {
        int jamming_number = numerical_output[idx++];  // 干扰编号
        int jamming_id = numerical_output[idx++];      // 干扰类型ID

        const char* jamming_names[] = {"Comb", "ISRJ", "Broadband", "Smart", "Deception"};
        const char* jamming_name = (jamming_id >= 0 && jamming_id < 5) ?
                                 jamming_names[jamming_id] : "Unknown";

        std::cout << "\n       Jamming " << jamming_number << ": " << jamming_name
                  << " (number=" << jamming_number << ", ID=" << jamming_id << ")" << std::endl;

        // 根据干扰类型解释参数
        idx = explain_jamming_type_params(jamming_id, numerical_output, idx, size);
    }
}

// 解释不同干扰类型的参数
int explain_jamming_type_params(int jamming_id, const int* numerical_output, int start_idx, int size) {
    int idx = start_idx;

    switch (jamming_id) {
        case 0: // 梳状谱
            std::cout << "         Parameter interpretation: Comb spectrum jamming (dynamic count format)" << std::endl;
            if (idx < size) {
                int count = numerical_output[idx++];
                std::cout << "           Comb count: " << count << std::endl;

                // 动态解析频偏序列
                std::cout << "           Frequency offset sequence (kHz): ";
                for (int i = 0; i < count && idx < size; i++) {
                    std::cout << numerical_output[idx++] << " ";
                }
                std::cout << std::endl;

                // 动态解析闪烁周期序列
                std::cout << "           Flicker period sequence (μs): ";
                for (int i = 0; i < count && idx < size; i++) {
                    std::cout << numerical_output[idx++] << " ";
                }
                std::cout << std::endl;

                // 动态解析闪烁保持时间序列
                std::cout << "           Flicker hold time sequence (μs): ";
                for (int i = 0; i < count && idx < size; i++) {
                    std::cout << numerical_output[idx++] << " ";
                }
                std::cout << std::endl;

                std::cout << "           Total parameters: " << (1 + count * 3)
                          << " (1 count + " << count << "*3 parameters)" << std::endl;
            }
            break;

        case 1: // 间歇采样转发
            std::cout << "         Parameter interpretation: ISRJ jamming" << std::endl;
            if (idx + 5 < size) {
                std::cout << "           Repeat forwarding interval: " << numerical_output[idx++] << " μs" << std::endl;
                std::cout << "           Intermittent sampling switch: " << numerical_output[idx++] << " (1=on)" << std::endl;
                std::cout << "           Intermittent sampling period: " << numerical_output[idx++] << " μs" << std::endl;
                std::cout << "           Intermittent sampling width: " << numerical_output[idx++] << " μs" << std::endl;
                std::cout << "           Jamming coverage range: " << numerical_output[idx++] << " m" << std::endl;
                std::cout << "           Pulse sampling length: " << numerical_output[idx++] << " μs" << std::endl;
            }
            break;

        case 2: // 宽带阻塞噪声
            std::cout << "         Parameter interpretation: Broadband blocking noise jamming" << std::endl;
            if (idx < size) {
                int bandwidth_id = numerical_output[idx++];
                const char* bandwidths[] = {"20MHz", "40MHz", "80MHz", "100MHz", "200MHz",
                                          "300MHz", "400MHz", "500MHz", "600MHz", "700MHz",
                                          "800MHz", "900MHz", "1000MHz"};
                const char* bandwidth = (bandwidth_id >= 0 && bandwidth_id < 13) ?
                                      bandwidths[bandwidth_id] : "Unknown";
                std::cout << "           Bandwidth selection: " << bandwidth_id << " (" << bandwidth << ")" << std::endl;
            }
            break;

        case 3: // 灵巧噪声
            std::cout << "         Parameter interpretation: Smart noise jamming" << std::endl;
            if (idx + 6 < size) {
                std::cout << "           Noise bandwidth selection: " << numerical_output[idx++] << std::endl;
                std::cout << "           Noise source selection: " << numerical_output[idx++] << " (2=Doppler flicker)" << std::endl;
                std::cout << "           Doppler flicker mode: " << numerical_output[idx++] << " (1=fixed flicker)" << std::endl;
                std::cout << "           Flicker hold time: " << numerical_output[idx++] << " μs" << std::endl;
                std::cout << "           Flicker disappear time: " << numerical_output[idx++] << " μs" << std::endl;
                std::cout << "           Doppler noise bandwidth: " << numerical_output[idx++] << " kHz" << std::endl;
                std::cout << "           Doppler noise jump period: " << numerical_output[idx++] << " kHz" << std::endl;
            }
            break;

        case 4: // 拖引
            std::cout << "         Parameter interpretation: Deception jamming" << std::endl;
            if (idx + 7 < size) {
                std::cout << "           Velocity drag speed: " << numerical_output[idx++] << " m/s" << std::endl;
                std::cout << "           Velocity drag acceleration: " << numerical_output[idx++] << " m/s²" << std::endl;
                std::cout << "           Range drag speed: " << numerical_output[idx++] << " m/s" << std::endl;
                std::cout << "           Range drag acceleration: " << numerical_output[idx++] << " m/s²" << std::endl;
                std::cout << "           Capture time: " << numerical_output[idx++] << " s" << std::endl;
                std::cout << "           Drag time: " << numerical_output[idx++] << " s" << std::endl;
                std::cout << "           Hold time: " << numerical_output[idx++] << " s" << std::endl;
                std::cout << "           Disappear time: " << numerical_output[idx++] << " s" << std::endl;
            }
            break;

        default:
            std::cout << "         Parameter interpretation: Unknown jamming type" << std::endl;
            break;
    }

    return idx;
}

#include "main.moc"
