﻿#include "rknn_inference.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>

/*
 * RKNN推理引擎实现
 */

// RKNN推理器内部结构
typedef struct {
    int initialized;
    int model_loaded;
    char model_path[256];
    
    // 模型信息
    int input_size;
    int output_size;
    
    // 统计信息
    int inference_count;
    double total_inference_time;
    double min_inference_time;
    double max_inference_time;
    
    // 配置参数
    int thread_count;
    char precision[32];
    int batch_size;
} RKNNInferenceImpl;

/*
 * 创建RKNN推理器
 */
RKNNInference* rknn_inference_create(void) {
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)malloc(sizeof(RKNNInferenceImpl));
    if (!impl) {
        return NULL;
    }
    
    // 初始化结构
    impl->initialized = 1;
    impl->model_loaded = 0;
    memset(impl->model_path, 0, sizeof(impl->model_path));
    
    impl->input_size = RKNN_INPUT_DIM;
    impl->output_size = RKNN_THREAT_LEVELS + RKNN_JAMMING_TYPES;
    
    // 初始化统计信息
    impl->inference_count = 0;
    impl->total_inference_time = 0.0;
    impl->min_inference_time = 999999.0;
    impl->max_inference_time = 0.0;
    
    // 默认配置
    impl->thread_count = 1;
    strcpy(impl->precision, "fp32");
    impl->batch_size = 1;
    
    printf("RKNN推理引擎初始化成功\n");
    return (RKNNInference*)impl;
}

/**
 * 销毁RKNN推理器
 */
void rknn_inference_destroy(RKNNInference* inference) {
    if (inference) {
        RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
        if (impl->model_loaded) {
            printf("卸载RKNN模型: %s\n", impl->model_path);
        }
        free(inference);
        printf("RKNN推理引擎已销毁\n");
    }
}

/**
 * 加载RKNN模型
 */
int rknn_inference_load_model(RKNNInference* inference, const char* model_path) {
    if (!inference || !model_path) {
        return RKNN_ERROR_INVALID;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    
    // 检查文件是否存在（简化版本）
    FILE* file = fopen(model_path, "rb");
    if (!file) {
        printf("错误: 无法打开模型文件: %s\n", model_path);
        return RKNN_ERROR_MODEL;
    }
    fclose(file);
    
    // 保存模型路径
    strncpy(impl->model_path, model_path, sizeof(impl->model_path) - 1);
    impl->model_loaded = 1;
    
    printf("RKNN模型加载成功: %s\n", model_path);
    return RKNN_SUCCESS;
}

/**
 * 检查模型是否已加载
 */
int rknn_inference_is_model_loaded(RKNNInference* inference) {
    if (!inference) {
        return 0;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    return impl->model_loaded;
}

/**
 * 构建输入向量
 */
static void build_input_vector(const RadarParameters* radar_params,
                              const ThreatAssessmentData* threat_data,
                              float* input_vector) {
    // 威胁信息 (4维)
    input_vector[0] = (float)threat_data->threat_level / 5.0f;  // 威胁等级归一化
    input_vector[1] = (float)threat_data->threat_value;         // 威胁值
    input_vector[2] = (float)threat_data->confidence;           // 置信度
    input_vector[3] = (float)threat_data->priority;             // 优先级
    
    // 雷达参数 (8维) - 归一化
    input_vector[4] = (float)radar_params->frequency_mhz / 20000.0f;    // 频率归一化
    input_vector[5] = (float)radar_params->pulse_width_us / 10.0f;      // 脉宽归一化
    input_vector[6] = (float)radar_params->prt_us / 1000.0f;            // PRT归一化
    input_vector[7] = (float)radar_params->power_w / 1e7f;              // 功率归一化
    input_vector[8] = (float)radar_params->distance_km / 300.0f;        // 距离归一化
    input_vector[9] = (float)radar_params->speed_ms / 1000.0f;          // 速度归一化
    input_vector[10] = (float)radar_params->direction_deg / 360.0f;     // 方向归一化
    input_vector[11] = (float)radar_params->work_mode / 4.0f;           // 工作模式归一化
}

/**
 * 模拟RKNN推理（当实际RKNN不可用时）
 */
static int simulate_rknn_inference(const float* input_vector, 
                                  float* threat_level_probs,
                                  float* jamming_type_probs) {
    // 基于输入特征生成模拟输出
    float threat_value = input_vector[1];  // 威胁值
    float frequency_norm = input_vector[4]; // 归一化频率
    float distance_norm = input_vector[8];  // 归一化距离
    
    // 生成威胁等级概率分布
    for (int i = 0; i < RKNN_THREAT_LEVELS; i++) {
        threat_level_probs[i] = 0.1f;  // 基础概率
    }
    
    // 根据威胁值调整概率分布
    if (threat_value > 0.8f) {
        threat_level_probs[0] = 0.7f;  // 极高威胁
        threat_level_probs[1] = 0.2f;
    } else if (threat_value > 0.6f) {
        threat_level_probs[1] = 0.6f;  // 高威胁
        threat_level_probs[0] = 0.2f;
        threat_level_probs[2] = 0.1f;
    } else if (threat_value > 0.4f) {
        threat_level_probs[2] = 0.5f;  // 中等威胁
        threat_level_probs[1] = 0.2f;
        threat_level_probs[3] = 0.2f;
    } else if (threat_value > 0.2f) {
        threat_level_probs[3] = 0.5f;  // 低威胁
        threat_level_probs[2] = 0.2f;
        threat_level_probs[4] = 0.2f;
    } else {
        threat_level_probs[4] = 0.6f;  // 极低威胁
        threat_level_probs[3] = 0.3f;
    }
    
    // 生成干扰类型概率分布
    for (int i = 0; i < RKNN_JAMMING_TYPES; i++) {
        jamming_type_probs[i] = 0.1f;  // 基础概率
    }
    
    // 根据威胁等级和频率特征决定干扰类型
    if (threat_value > 0.6f) {
        if (frequency_norm > 0.4f) {  // 高频
            jamming_type_probs[3] = 0.5f;  // 灵巧噪声
            jamming_type_probs[1] = 0.3f;  // 间歇采样
        } else {
            jamming_type_probs[2] = 0.5f;  // 宽带噪声
            jamming_type_probs[4] = 0.3f;  // 拖引干扰
        }
    } else if (threat_value > 0.3f) {
        jamming_type_probs[1] = 0.4f;  // 间歇采样
        jamming_type_probs[2] = 0.3f;  // 宽带噪声
    } else {
        jamming_type_probs[0] = 0.7f;  // 无干扰
    }
    
    return RKNN_SUCCESS;
}

/*
 * 执行RKNN推理
 */
int rknn_inference_predict(RKNNInference* inference, 
                          const RadarParameters* radar_params,
                          const ThreatAssessmentData* threat_data,
                          RKNNInferenceData* result) {
    if (!inference || !radar_params || !threat_data || !result) {
        return RKNN_ERROR_INVALID;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    if (!impl->initialized) {
        return RKNN_ERROR_INVALID;
    }
    
    // 记录开始时间
    clock_t start_time = clock();
    
    // 构建输入向量
    float input_vector[RKNN_INPUT_DIM];
    build_input_vector(radar_params, threat_data, input_vector);
    
    // 执行推理（模拟版本）
    int ret = simulate_rknn_inference(input_vector, 
                                     result->threat_level_probs,
                                     result->jamming_type_probs);
    
    if (ret != RKNN_SUCCESS) {
        result->success = 0;
        return ret;
    }
    
    // 计算推理时间
    clock_t end_time = clock();
    result->inference_time_ms = ((double)(end_time - start_time)) / CLOCKS_PER_SEC * 1000.0;
    
    // 找到最大概率的索引
    int max_threat_idx = 0;
    int max_jamming_idx = 0;
    
    for (int i = 1; i < RKNN_THREAT_LEVELS; i++) {
        if (result->threat_level_probs[i] > result->threat_level_probs[max_threat_idx]) {
            max_threat_idx = i;
        }
    }
    
    for (int i = 1; i < RKNN_JAMMING_TYPES; i++) {
        if (result->jamming_type_probs[i] > result->jamming_type_probs[max_jamming_idx]) {
            max_jamming_idx = i;
        }
    }
    
    result->predicted_threat_level = max_threat_idx + 1;  // 1-5
    result->predicted_jamming_type = max_jamming_idx;     // 0-4
    result->success = 1;
    
    // 更新统计信息
    impl->inference_count++;
    impl->total_inference_time += result->inference_time_ms;
    
    if (result->inference_time_ms < impl->min_inference_time) {
        impl->min_inference_time = result->inference_time_ms;
    }
    if (result->inference_time_ms > impl->max_inference_time) {
        impl->max_inference_time = result->inference_time_ms;
    }
    
    return RKNN_SUCCESS;
}

/**
 * 批量推理
 */
int rknn_inference_batch_predict(RKNNInference* inference,
                                 const RadarParameters* radar_params_array,
                                 const ThreatAssessmentData* threat_data_array,
                                 int batch_size,
                                 RKNNInferenceData* results) {
    if (!inference || !radar_params_array || !threat_data_array || !results || batch_size <= 0) {
        return RKNN_ERROR_INVALID;
    }
    
    int success_count = 0;
    
    for (int i = 0; i < batch_size; i++) {
        int ret = rknn_inference_predict(inference, 
                                        &radar_params_array[i],
                                        &threat_data_array[i],
                                        &results[i]);
        if (ret == RKNN_SUCCESS) {
            success_count++;
        }
    }
    
    return success_count;
}

/**
 * 设置配置参数
 */
int rknn_inference_set_config(RKNNInference* inference, 
                             const char* config_key, 
                             const char* config_value) {
    if (!inference || !config_key || !config_value) {
        return RKNN_ERROR_INVALID;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    
    if (strcmp(config_key, RKNN_CONFIG_THREADS) == 0) {
        impl->thread_count = atoi(config_value);
        printf("RKNN线程数设置为: %d\n", impl->thread_count);
    } else if (strcmp(config_key, RKNN_CONFIG_PRECISION) == 0) {
        strncpy(impl->precision, config_value, sizeof(impl->precision) - 1);
        printf("RKNN精度设置为: %s\n", impl->precision);
    } else if (strcmp(config_key, RKNN_CONFIG_BATCH_SIZE) == 0) {
        impl->batch_size = atoi(config_value);
        printf("RKNN批处理大小设置为: %d\n", impl->batch_size);
    } else {
        printf("警告: 未知配置参数: %s\n", config_key);
        return RKNN_ERROR_INVALID;
    }
    
    return RKNN_SUCCESS;
}

/**
 * 获取模型信息
 */
int rknn_inference_get_model_info(RKNNInference* inference, char* info_buffer, int buffer_size) {
    if (!inference || !info_buffer || buffer_size <= 0) {
        return RKNN_ERROR_INVALID;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    
    snprintf(info_buffer, buffer_size,
             "模型路径: %s\n"
             "模型状态: %s\n"
             "输入维度: %d\n"
             "输出维度: %d\n"
             "线程数: %d\n"
             "精度: %s\n"
             "批处理大小: %d",
             impl->model_loaded ? impl->model_path : "未加载",
             impl->model_loaded ? "已加载" : "未加载",
             impl->input_size,
             impl->output_size,
             impl->thread_count,
             impl->precision,
             impl->batch_size);
    
    return RKNN_SUCCESS;
}

/**
 * 获取统计信息
 */
const char* rknn_inference_get_stats(RKNNInference* inference) {
    static char stats_buffer[512];
    
    if (!inference) {
        snprintf(stats_buffer, sizeof(stats_buffer), "RKNN推理引擎无效");
        return stats_buffer;
    }
    
    RKNNInferenceImpl* impl = (RKNNInferenceImpl*)inference;
    
    double avg_time = impl->inference_count > 0 ? 
                     impl->total_inference_time / impl->inference_count : 0.0;
    
    snprintf(stats_buffer, sizeof(stats_buffer),
             "推理次数: %d, 平均耗时: %.2f ms, 最小耗时: %.2f ms, 最大耗时: %.2f ms, 模型: %s",
             impl->inference_count,
             avg_time,
             impl->min_inference_time == 999999.0 ? 0.0 : impl->min_inference_time,
             impl->max_inference_time,
             impl->model_loaded ? "已加载" : "未加载");
    
    return stats_buffer;
}

/**
 * 获取错误描述
 */
const char* rknn_get_error_string(int error_code) {
    switch (error_code) {
        case RKNN_SUCCESS:
            return "成功";
        case RKNN_ERROR_INVALID:
            return "无效参数";
        case RKNN_ERROR_MODEL:
            return "模型错误";
        case RKNN_ERROR_INFERENCE:
            return "推理错误";
        case RKNN_ERROR_MEMORY:
            return "内存错误";
        case RKNN_ERROR_NOT_LOADED:
            return "模型未加载";
        default:
            return "未知错误";
    }
}
