﻿#ifndef RKNN_INFERENCE_H
#define RKNN_INFERENCE_H

#ifdef __cplusplus
extern "C" {
#endif

#if HAS_RKNN
// #include "rknn_api.h"  // 暂时注释掉，避免编译错误
#endif

/**
 * RKNN����ģ��
 * �������RKNNģ�Ͳ�ִ������
 */

// RKNN输入数据结构 (与ONNX版本保持一致)
typedef struct RKNNInput {
    float input_data[12];     // 12维输入特征向量
    int input_size;           // 输入数据大小 (固定为12)
} RKNNInput;

// RKNN输出数据结构
typedef struct RKNNOutput {
    float* output_data;   // 输出数据数组
    int output_size;      // 输出数据大小
    int valid;           // 数据是否有效
} RKNNOutput;

// RKNN���������
typedef void* RKNNInference;

// ���ĺ���
RKNNInference* rknn_inference_create(const char* model_path);
void rknn_inference_destroy(RKNNInference* inference);
int rknn_inference_predict(RKNNInference* inference, 
                          const RKNNInput* input, 
                          RKNNOutput* output);

// ��������
int rknn_validate_input(const RKNNInput* input);
void rknn_free_output(RKNNOutput* output);
const char* rknn_get_error_string(int error_code);

// �����붨��
#define RKNN_SUCCESS           0
#define RKNN_ERROR_INVALID     -1
#define RKNN_ERROR_MODEL       -2
#define RKNN_ERROR_INFERENCE   -3
#define RKNN_ERROR_MEMORY      -4

#ifdef __cplusplus
}
#endif

#endif // RKNN_INFERENCE_H
