"""
智能干扰系统模块接口定义
定义各模块的标准接口，确保模块间的解耦和可替换性
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from .data_structures import *


class IRadarModule(ABC):
    """雷达模块接口"""
    
    @abstractmethod
    def get_emission_parameters(self) -> RadarEmissionData:
        """
        获取雷达发射参数
        
        Returns:
            雷达发射参数数据
        """
        pass
    
    @abstractmethod
    def update_operating_mode(self, mode: str) -> bool:
        """
        更新雷达工作模式
        
        Args:
            mode: 工作模式
            
        Returns:
            更新是否成功
        """
        pass
    
    @abstractmethod
    def process_jamming_signal(self, jamming_data: JammingExecutionData) -> Dict:
        """
        处理接收到的干扰信号
        
        Args:
            jamming_data: 干扰执行数据
            
        Returns:
            雷达响应数据
        """
        pass
    
    @abstractmethod
    def get_system_status(self) -> Dict:
        """
        获取雷达系统状态
        
        Returns:
            系统状态字典
        """
        pass


class IThreatEvaluator(ABC):
    """威胁评估模块接口"""
    
    @abstractmethod
    def evaluate_threat(self, radar_data: RadarEmissionData, 
                       context: Optional[Dict] = None) -> ThreatAssessmentData:
        """
        评估威胁等级
        
        Args:
            radar_data: 雷达发射数据
            context: 上下文信息
            
        Returns:
            威胁评估数据
        """
        pass
    
    @abstractmethod
    def update_threat_model(self, feedback: FeedbackData) -> bool:
        """
        更新威胁评估模型
        
        Args:
            feedback: 反馈数据
            
        Returns:
            更新是否成功
        """
        pass


class IJammingDecisionModule(ABC):
    """干扰决策模块接口"""
    
    @abstractmethod
    def make_decision(self, threat_data: ThreatAssessmentData,
                     radar_data: RadarEmissionData,
                     context: Optional[Dict] = None) -> JammingDecisionData:
        """
        制定干扰决策
        
        Args:
            threat_data: 威胁评估数据
            radar_data: 雷达数据
            context: 上下文信息
            
        Returns:
            干扰决策数据
        """
        pass
    
    @abstractmethod
    def update_decision_model(self, feedback: FeedbackData) -> bool:
        """
        更新决策模型
        
        Args:
            feedback: 反馈数据
            
        Returns:
            更新是否成功
        """
        pass
    
    @abstractmethod
    def get_decision_confidence(self) -> float:
        """
        获取决策置信度
        
        Returns:
            置信度值 [0,1]
        """
        pass


class IJammingExecutor(ABC):
    """干扰执行模块接口"""
    
    @abstractmethod
    def execute_jamming(self, decision: JammingDecisionData) -> JammingExecutionData:
        """
        执行干扰
        
        Args:
            decision: 干扰决策数据
            
        Returns:
            干扰执行数据
        """
        pass
    
    @abstractmethod
    def stop_jamming(self) -> bool:
        """
        停止干扰
        
        Returns:
            停止是否成功
        """
        pass
    
    @abstractmethod
    def get_equipment_status(self) -> Dict:
        """
        获取设备状态
        
        Returns:
            设备状态字典
        """
        pass
    
    @abstractmethod
    def calibrate_equipment(self) -> bool:
        """
        校准设备
        
        Returns:
            校准是否成功
        """
        pass


class IEffectivenessEvaluator(ABC):
    """干扰效果评估模块接口"""
    
    @abstractmethod
    def evaluate_effectiveness(self, 
                             before_jamming: RadarEmissionData,
                             jamming_execution: JammingExecutionData,
                             after_jamming: Dict) -> EffectivenessData:
        """
        评估干扰效果
        
        Args:
            before_jamming: 干扰前雷达数据
            jamming_execution: 干扰执行数据
            after_jamming: 干扰后雷达响应
            
        Returns:
            效果评估数据
        """
        pass
    
    @abstractmethod
    def compare_with_baseline(self, effectiveness: EffectivenessData) -> Dict:
        """
        与基线性能比较
        
        Args:
            effectiveness: 效果数据
            
        Returns:
            比较结果
        """
        pass


class IFeedbackProcessor(ABC):
    """反馈处理模块接口"""
    
    @abstractmethod
    def process_feedback(self, cycle_data: SystemCycleData) -> FeedbackData:
        """
        处理系统反馈
        
        Args:
            cycle_data: 完整周期数据
            
        Returns:
            反馈数据
        """
        pass
    
    @abstractmethod
    def update_system_parameters(self, feedback: FeedbackData) -> Dict:
        """
        更新系统参数
        
        Args:
            feedback: 反馈数据
            
        Returns:
            参数更新结果
        """
        pass
    
    @abstractmethod
    def get_learning_metrics(self) -> Dict:
        """
        获取学习指标
        
        Returns:
            学习指标字典
        """
        pass


class ISystemController(ABC):
    """系统控制器接口"""
    
    @abstractmethod
    def initialize_system(self, config: SystemConfiguration) -> bool:
        """
        初始化系统
        
        Args:
            config: 系统配置
            
        Returns:
            初始化是否成功
        """
        pass
    
    @abstractmethod
    def run_single_cycle(self) -> SystemCycleData:
        """
        运行单个闭环周期
        
        Returns:
            周期数据
        """
        pass
    
    @abstractmethod
    def run_continuous(self, duration: Optional[float] = None) -> List[SystemCycleData]:
        """
        连续运行系统
        
        Args:
            duration: 运行时长，None表示无限运行
            
        Returns:
            所有周期数据列表
        """
        pass
    
    @abstractmethod
    def stop_system(self) -> bool:
        """
        停止系统
        
        Returns:
            停止是否成功
        """
        pass
    
    @abstractmethod
    def get_system_metrics(self) -> Dict:
        """
        获取系统性能指标
        
        Returns:
            性能指标字典
        """
        pass
    
    @abstractmethod
    def emergency_stop(self) -> bool:
        """
        紧急停止
        
        Returns:
            停止是否成功
        """
        pass
