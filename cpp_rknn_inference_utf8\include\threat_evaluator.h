﻿#ifndef THREAT_EVALUATOR_H
#define THREAT_EVALUATOR_H

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 威胁评估模块
 * 负责分析雷达状态并计算威胁等级
 */

// 雷达参数结构
typedef struct {
    double frequency_mhz;      // 频率 (MHz)
    double pulse_width_us;     // 脉宽 (μs)
    double prt_us;            // 脉冲重复周期 (μs)
    double power_w;           // 功率 (W)
    double distance_km;       // 距离 (km)
    double speed_ms;          // 速度 (m/s)
    double direction_deg;     // 方向 (度)
    int work_mode;            // 工作模式
} RadarParameters;

// 威胁评估结果
typedef struct {
    int threat_level;         // 威胁等级 (1-5)
    double threat_value;      // 威胁数值 (0-1)
    double confidence;        // 评估置信度 (0-1)
    double priority;          // 威胁优先级 (0-1)

    // 详细威胁度分析
    double frequency_threat;  // 频率威胁度
    double power_threat;      // 功率威胁度
    double distance_threat;   // 距离威胁度
    double mode_threat;       // 工作模式威胁度
} ThreatAssessmentData;

// Threat evaluator handle
typedef void* ThreatEvaluator;

// 核心函数
ThreatEvaluator* threat_evaluator_create(void);
void threat_evaluator_destroy(ThreatEvaluator* evaluator);
int threat_evaluator_assess(ThreatEvaluator* evaluator, 
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result);

// 辅助函数
const char* threat_get_level_description(int threat_level);
const char* threat_evaluator_get_stats(ThreatEvaluator* evaluator);

// 配置函数
int threat_evaluator_set_weights(ThreatEvaluator* evaluator,
                                double freq_weight,
                                double power_weight, 
                                double distance_weight,
                                double mode_weight);

// 威胁等级常量
#define THREAT_LEVEL_CRITICAL    1  // 极高威胁
#define THREAT_LEVEL_HIGH        2  // 高威胁
#define THREAT_LEVEL_MEDIUM      3  // 中等威胁
#define THREAT_LEVEL_LOW         4  // 低威胁
#define THREAT_LEVEL_MINIMAL     5  // 极低威胁

// 工作模式常量
#define WORK_MODE_SILENT         0  // 静默模式
#define WORK_MODE_SEARCH         1  // 搜索模式
#define WORK_MODE_TRACK          2  // 跟踪模式
#define WORK_MODE_GUIDANCE       3  // 制导模式
#define WORK_MODE_TERMINAL       4  // 终末制导模式

#ifdef __cplusplus
}
#endif

#endif // THREAT_EVALUATOR_H
