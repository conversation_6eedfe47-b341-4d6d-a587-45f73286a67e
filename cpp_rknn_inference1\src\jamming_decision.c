﻿#include "jamming_decision.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/**
 * 干扰决策模块实现
 */

/**
 * 创建干扰决策器
 */
JammingDecision* jamming_decision_create(void) {
    JammingDecisionImpl* impl = (JammingDecisionImpl*)malloc(sizeof(JammingDecisionImpl));
    if (!impl) {
        return NULL;
    }
    
     // 初始化参数
    impl->initialized = 1;
    impl->combination_threshold = 0.15;  // 组合干扰阈值
    
    // 初始化各种干扰类型的效果权重
    impl->effectiveness_weights[0] = 0.0;  // 无干扰
    impl->effectiveness_weights[1] = 0.8;  // 梳状谱
    impl->effectiveness_weights[2] = 0.9;  // 间歇采样转发
    impl->effectiveness_weights[3] = 0.6;  // 宽带阻塞噪声
    impl->effectiveness_weights[4] = 0.85; // 灵巧噪声
    impl->effectiveness_weights[5] = 0.95; // 拖引
    
    printf("初始化成功\n");
    return (JammingDecision*)impl;
}

/**
 * 销毁干扰决策器
 */
void jamming_decision_destroy(JammingDecision* decision) {
    if (decision) {
        free(decision);
        printf("干扰决策器已销毁\n");
    }
}

/**
 * 获取干扰类型名称
 */
const char* jamming_get_type_name(JammingType type) {
    switch (type) {
        case JAMMING_NONE: return "无干扰";
        case JAMMING_COMB_SPECTRUM: return "梳状谱干扰";
        case JAMMING_ISRJ: return "间歇采样转发干扰";
        case JAMMING_BROADBAND: return "宽带阻塞噪声";
        case JAMMING_SMART_NOISE: return "灵巧噪声干扰";
        case JAMMING_DRAG: return "拖引干扰";
        default: return "未知干扰类型";
    }
}

/**
 * 计算干扰效果
 */
double jamming_calculate_effectiveness(JammingType type,
                                     int threat_level,
                                     double threat_value) {
    double base_effectiveness = 0.0;
    
    switch (type) {
        case JAMMING_COMB_SPECTRUM:
            base_effectiveness = 0.8;
            break;
        case JAMMING_ISRJ:
            base_effectiveness = 0.9;
            break;
        case JAMMING_BROADBAND:
            base_effectiveness = 0.6;
            break;
        case JAMMING_SMART_NOISE:
            base_effectiveness = 0.85;
            break;
        case JAMMING_DRAG:
            base_effectiveness = 0.95;
            break;
        default:
            return 0.0;
    }
    
    //
    double threat_factor = 1.0;
    if (threat_level <= 2) {
        threat_factor = 1.2;  //
    } else if (threat_level >= 4) {
        threat_factor = 0.8;  //
    }
    
    return base_effectiveness * threat_factor * threat_value;
}

/**
 * 判断是否推荐组合干扰
 */
int jamming_is_combination_recommended(const RKNNOutput* rknn_output,
                                      int primary_type,
                                      double primary_prob) {
    if (!rknn_output || !rknn_output->valid || rknn_output->output_size < 10) {
        return 0;
    }
    

    float combination_score = rknn_output->output_data[5];
    

    float secondary_prob = 0.0f;
    int secondary_type = -1;
    
    for (int i = 0; i < 5; i++) {
        if (i != primary_type && rknn_output->output_data[i] > secondary_prob) {
            secondary_prob = rknn_output->output_data[i];
            secondary_type = i;
        }
    }
    

    if (combination_score > 0.05f && secondary_prob > 0.15f && primary_prob < 0.8f) {
        printf("  推荐组合干扰: 主要=%d(%.3f), 次要=%d(%.3f), 组合分数=%.3f\n",
               primary_type, primary_prob, secondary_type, secondary_prob, combination_score);
        return 1;
    }
    
    return 0;
}

/**
 * 根据工作模式判断是否应该干扰
 */
static int should_jam_based_on_mode(int threat_level, int work_mode) {
    // 威胁等级5不干扰
    if (threat_level >= 5) {
        return 0;
    }

    // 威胁等级4且静默模式时不干扰
    if (threat_level == 4 && work_mode == 0) {
        return 0;
    }

    return 1;
}

// 执行干扰决策
int jamming_decision_decide(JammingDecision* decision,
                           int threat_level,
                           double threat_value,
                           const RKNNOutput* rknn_output,
                           int work_mode,
                           JammingDecisionResult* result) {
    if (!decision || !result) {
        return -1;
    }

    JammingDecisionImpl* impl = (JammingDecisionImpl*)decision;
    if (!impl->initialized) {
        return -2;
    }

    // 验证工作模式参数
    if (work_mode < 0 || work_mode > 2) {
        printf("警告: 工作模式参数无效 (%d)，使用默认搜索模式\n", work_mode);
        work_mode = 1;  // 默认为搜索模式
    }


    memset(result, 0, sizeof(JammingDecisionResult));


    if (threat_level >= 5 || threat_value < 0.1) {
        result->jamming_count = 0;
        result->jamming_types[0] = JAMMING_NONE;
        strcpy(result->strategy_description, "威胁等级5不干扰");
        result->confidence = 0.9;
        result->use_combination = 0;
        return 0;
    }


    // 工作模式直接从参数传入
    // work_mode: 0=静默模式, 1=搜索模式, 2=跟踪模式
    if (threat_level == 4 && work_mode == 0) {
        result->jamming_count = 0;
        result->jamming_types[0] = JAMMING_NONE;
        strcpy(result->strategy_description, "威胁等级4且静默模式不干扰");
        result->confidence = 0.9;
        result->use_combination = 0;
        return 0;
    }


    if (rknn_output && rknn_output->valid && rknn_output->output_size >= 5) {

        int best_jamming_type = 0;
        float best_prob = rknn_output->output_data[0];

        for (int i = 1; i < 5; i++) {
            if (rknn_output->output_data[i] > best_prob) {
                best_prob = rknn_output->output_data[i];
                best_jamming_type = i;
            }
        }


        JammingType primary_type = (JammingType)(best_jamming_type + 1);
        result->jamming_types[0] = primary_type;
        result->jamming_probs[0] = best_prob;
        result->jamming_count = 1;


        if (threat_level <= 2) {

            float secondary_prob = 0.0f;
            int secondary_type = -1;

            for (int i = 0; i < 5; i++) {
                if (i != best_jamming_type && rknn_output->output_data[i] > secondary_prob) {
                    secondary_prob = rknn_output->output_data[i];
                    secondary_type = i;
                }
            }

            if (secondary_type >= 0) {
                result->jamming_types[1] = (JammingType)(secondary_type + 1);
                result->jamming_probs[1] = secondary_prob;
                result->jamming_count = 2;
                result->use_combination = 1;

                sprintf(result->strategy_description, "威胁等级%d使用组合干扰: %s + %s",
                        threat_level,
                        jamming_get_type_name(result->jamming_types[0]),
                        jamming_get_type_name(result->jamming_types[1]));
            } else {
                result->use_combination = 0;
                sprintf(result->strategy_description, "威胁等级%d使用单一干扰: %s",
                        threat_level, jamming_get_type_name(result->jamming_types[0]));
            }
        } else {
            // 威胁等级3-4使用单一干扰
            result->use_combination = 0;  // 修改为单一干扰
            sprintf(result->strategy_description, "威胁等级%d使用单一干扰: %s",
                    threat_level, jamming_get_type_name(result->jamming_types[0]));
        }

        // 计算决策置信度
        result->confidence = best_prob * 0.8 + 0.2;

    } else {
        // 基于威胁等级的简单决策
        if (threat_level <= 2) {
            // 威胁等级1-2使用组合干扰，无RKNN时使用多种干扰
            result->jamming_types[0] = JAMMING_DRAG;
            result->jamming_types[1] = JAMMING_SMART_NOISE;
            result->jamming_count = 2;
            result->use_combination = 1;
            sprintf(result->strategy_description, "威胁等级%d使用组合干扰: %s + %s",
                    threat_level,
                    jamming_get_type_name(result->jamming_types[0]),
                    jamming_get_type_name(result->jamming_types[1]));
        } else if (threat_level == 3) {
            // 威胁等级3使用单一干扰
            result->jamming_types[0] = JAMMING_SMART_NOISE;
            result->jamming_count = 1;
            result->use_combination = 0;  // 修改为单一干扰
            sprintf(result->strategy_description, "威胁等级%d使用单一干扰: %s",
                    threat_level, jamming_get_type_name(result->jamming_types[0]));
        } else {
            // 威胁等级4使用单一干扰
            result->jamming_types[0] = JAMMING_BROADBAND;
            result->jamming_count = 1;
            result->use_combination = 0;  // 修改为单一干扰
            sprintf(result->strategy_description, "威胁等级%d使用单一干扰: %s",
                    threat_level, jamming_get_type_name(result->jamming_types[0]));
        }

        result->confidence = 0.7;
    }

    printf("干扰决策结果: %s, 置信度=%.3f\n",
           result->strategy_description, result->confidence);
    
    return 0;
}
