#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN部署依赖包下载脚本
下载所有RKNN转换和部署阶段需要的Python包到本地目录
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

def create_directory(path):
    """创建目录"""
    Path(path).mkdir(parents=True, exist_ok=True)
    print(f"创建目录: {path}")

def download_packages(requirements_file, download_dir, index_url=None):
    """
    下载依赖包到指定目录
    
    Args:
        requirements_file: requirements.txt文件路径
        download_dir: 下载目录
        index_url: PyPI镜像源URL
    """
    print(f"开始下载RKNN依赖包...")
    print(f"需求文件: {requirements_file}")
    print(f"下载目录: {download_dir}")
    
    # 创建下载目录
    create_directory(download_dir)
    
    # 构建pip下载命令
    cmd = [
        sys.executable, "-m", "pip", "download",
        "-r", requirements_file,
        "-d", download_dir,
        "--no-deps"  # 不下载依赖的依赖
    ]
    
    # 添加镜像源
    if index_url:
        cmd.extend(["-i", index_url])
        print(f"使用镜像源: {index_url}")
    
    try:
        # 执行下载
        print("执行下载命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("下载成功!")
        print(result.stdout)
        
        # 统计下载的包数量
        downloaded_files = list(Path(download_dir).glob("*.whl")) + list(Path(download_dir).glob("*.tar.gz"))
        print(f"共下载 {len(downloaded_files)} 个包文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"下载失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def download_rknn_toolkit(download_dir):
    """下载RKNN工具包"""
    print("下载RKNN工具包...")
    
    # RKNN工具包下载链接(需要从瑞芯微官网获取)
    rknn_urls = {
        "rknn-toolkit2": "https://github.com/rockchip-linux/rknn-toolkit2/releases/latest",
        "rknn-toolkit-lite2": "https://github.com/rockchip-linux/rknn-toolkit2/releases/latest"
    }
    
    print("注意: RKNN工具包需要从瑞芯微官网手动下载")
    print("下载地址: https://github.com/rockchip-linux/rknn-toolkit2")
    print("请将下载的.whl文件放入以下目录:")
    print(f"  {os.path.abspath(download_dir)}")
    
    # 创建说明文件
    readme_content = """# RKNN工具包安装说明

## 下载RKNN工具包

1. 访问瑞芯微官方GitHub: https://github.com/rockchip-linux/rknn-toolkit2
2. 下载对应版本的工具包:
   - rknn_toolkit2-x.x.x-cpxx-cpxxm-linux_x86_64.whl
   - rknn_toolkit_lite2-x.x.x-cpxx-cpxxm-linux_x86_64.whl

## 安装步骤

1. 将下载的.whl文件放入当前目录
2. 运行安装命令:
   ```bash
   pip install rknn_toolkit2-*.whl
   pip install rknn_toolkit_lite2-*.whl
   ```

## 支持的平台

- RK3588/RK3588S
- RK3566/RK3568
- RK3562
- RK3576
- RV1103/RV1106

## 注意事项

- RKNN工具包需要Linux x86_64环境
- 推荐使用Python 3.6-3.8
- 需要安装对应的驱动程序
"""
    
    readme_path = os.path.join(download_dir, "RKNN_INSTALL_README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"创建安装说明: {readme_path}")

def download_opencv_packages(download_dir):
    """下载OpenCV相关包"""
    print("下载OpenCV相关包...")
    
    opencv_packages = [
        "opencv-python>=4.5.0",
        "opencv-contrib-python>=4.5.0",
        "Pillow>=8.0.0"
    ]
    
    for package in opencv_packages:
        cmd = [
            sys.executable, "-m", "pip", "download",
            package,
            "-d", download_dir
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"下载成功: {package}")
        except subprocess.CalledProcessError as e:
            print(f"下载失败: {package} - {e}")

def create_install_script(download_dir):
    """创建安装脚本"""
    script_content = f"""#!/bin/bash
# RKNN依赖包安装脚本

echo "开始安装RKNN依赖包..."

# 设置包目录
PACKAGE_DIR="{download_dir}"

# 检查RKNN工具包
if ls $PACKAGE_DIR/rknn_toolkit2-*.whl 1> /dev/null 2>&1; then
    echo "安装RKNN Toolkit2..."
    pip install $PACKAGE_DIR/rknn_toolkit2-*.whl
else
    echo "警告: 未找到RKNN Toolkit2安装包"
    echo "请从瑞芯微官网下载并放入 $PACKAGE_DIR 目录"
fi

if ls $PACKAGE_DIR/rknn_toolkit_lite2-*.whl 1> /dev/null 2>&1; then
    echo "安装RKNN Toolkit Lite2..."
    pip install $PACKAGE_DIR/rknn_toolkit_lite2-*.whl
else
    echo "警告: 未找到RKNN Toolkit Lite2安装包"
fi

# 安装基础依赖
echo "安装基础依赖包..."
pip install --find-links $PACKAGE_DIR --no-index numpy
pip install --find-links $PACKAGE_DIR --no-index opencv-python
pip install --find-links $PACKAGE_DIR --no-index Pillow
pip install --find-links $PACKAGE_DIR --no-index onnx
pip install --find-links $PACKAGE_DIR --no-index onnxruntime

# 安装其他依赖
echo "安装其他依赖包..."
pip install --find-links $PACKAGE_DIR --no-index -r rknn_requirements.txt

echo "RKNN依赖包安装完成!"
echo "注意: 请确保已安装RKNN驱动程序"
"""
    
    script_path = os.path.join(download_dir, "install_rknn_packages.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print(f"创建安装脚本: {script_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载RKNN部署依赖包')
    parser.add_argument('--requirements', '-r', type=str, 
                       default='rknn_requirements.txt',
                       help='requirements文件路径')
    parser.add_argument('--download-dir', '-d', type=str,
                       default='rknn_packages',
                       help='下载目录')
    parser.add_argument('--index-url', '-i', type=str,
                       default='https://pypi.tuna.tsinghua.edu.cn/simple/',
                       help='PyPI镜像源URL')
    parser.add_argument('--include-opencv', action='store_true',
                       help='包含OpenCV相关包')
    parser.add_argument('--create-script', action='store_true',
                       help='创建安装脚本')
    
    args = parser.parse_args()
    
    # 检查requirements文件
    if not os.path.exists(args.requirements):
        print(f"错误: requirements文件不存在: {args.requirements}")
        return 1
    
    # 下载包
    success = download_packages(args.requirements, args.download_dir, args.index_url)
    
    if success:
        # 下载RKNN工具包说明
        download_rknn_toolkit(args.download_dir)
        
        if args.include_opencv:
            # 下载OpenCV相关包
            download_opencv_packages(args.download_dir)
        
        if args.create_script:
            # 创建安装脚本
            create_install_script(args.download_dir)
    
    if success:
        print(f"\nRKNN依赖包下载完成!")
        print(f"包文件位置: {os.path.abspath(args.download_dir)}")
        print(f"\n使用方法:")
        print(f"1. 手动下载RKNN工具包到 {args.download_dir} 目录")
        print(f"2. 离线安装: pip install --find-links {args.download_dir} --no-index <package_name>")
        print(f"3. 批量安装: pip install --find-links {args.download_dir} --no-index -r {args.requirements}")
        if args.create_script:
            print(f"4. 脚本安装: bash {args.download_dir}/install_rknn_packages.sh")
        return 0
    else:
        print("下载失败!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
