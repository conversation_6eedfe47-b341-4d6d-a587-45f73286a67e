﻿#ifndef JAMMING_DECISION_H
#define JAMMING_DECISION_H

#include "threat_evaluator.h"
#include "rknn_inference.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 干扰决策模块
 * 基于威胁评估和RKNN推理结果生成干扰决策
 */

// 干扰类型枚举
typedef enum {
    JAMMING_NONE = 0,           // 无干扰
    JAMMING_ISRJ = 1,          // 间歇采样转发
    JAMMING_BROADBAND = 2,     // 宽带噪声干扰
    JAMMING_SMART_NOISE = 3,   // 灵巧噪声
    JAMMING_DECEPTION = 4      // 拖引干扰
} JammingType;

// 干扰决策结果数据结构
typedef struct {
    // 基本决策
    int should_jam;                 // 是否进行干扰
    JammingType jamming_type;       // 干扰类型
    double jamming_power;           // 干扰功率 (0-1)
    double jamming_frequency_mhz;   // 干扰频率 (MHz)
    double decision_confidence;     // 决策置信度 (0-1)
    
    // 干扰参数
    double bandwidth_mhz;           // 干扰带宽 (MHz)
    double duration_ms;             // 干扰持续时间 (ms)
    double delay_ms;                // 干扰延迟 (ms)
    
    // 高级参数（针对特定干扰类型）
    union {
        struct {  // 间歇采样参数
            double sampling_rate;
            double duty_cycle;
            int pulse_count;
        } isrj_params;
        
        struct {  // 宽带噪声参数
            double noise_power;
            double center_frequency_mhz;
        } broadband_params;
        
        struct {  // 灵巧噪声参数
            double adaptation_rate;
            int filter_order;
        } smart_noise_params;
        
        struct {  // 拖引干扰参数
            double false_target_count;
            double range_offset_km;
            double velocity_offset_ms;
        } deception_params;
    } advanced_params;
    
    // 决策统计
    double processing_time_ms;      // 处理时间
    char strategy_description[256]; // 策略描述
    char error_message[128];        // 错误信息
} JammingDecisionData;

// 干扰决策器句柄
typedef void* JammingDecision;

// 核心函数
JammingDecision* jamming_decision_create(void);
void jamming_decision_destroy(JammingDecision* decision);

// 决策生成
int jamming_decision_make(JammingDecision* decision,
                         const RadarParameters* radar_params,
                         const ThreatAssessmentData* threat_data,
                         JammingDecisionData* result);

// 基于RKNN推理的决策
int jamming_decision_make_with_rknn(JammingDecision* decision,
                                   const RadarParameters* radar_params,
                                   const ThreatAssessmentData* threat_data,
                                   const RKNNInferenceData* rknn_result,
                                   JammingDecisionData* result);

// 批量决策
int jamming_decision_batch_make(JammingDecision* decision,
                               const RadarParameters* radar_params_array,
                               const ThreatAssessmentData* threat_data_array,
                               int batch_size,
                               JammingDecisionData* results);

// 配置函数
int jamming_decision_set_threshold(JammingDecision* decision, double threshold);
int jamming_decision_set_power_limits(JammingDecision* decision, 
                                     double min_power, 
                                     double max_power);

// 辅助函数
const char* jamming_get_type_description(JammingType type);
const char* jamming_decision_get_stats(JammingDecision* decision);
int jamming_decision_validate_params(const JammingDecisionData* decision_data);

// 干扰效果评估
double jamming_estimate_effectiveness(JammingType type,
                                    const RadarParameters* radar_params,
                                    const JammingDecisionData* decision_data);

// 干扰类型常量
#define JAMMING_TYPE_COUNT     5   // 干扰类型总数

// 决策阈值常量
#define JAMMING_THRESHOLD_LOW     0.3  // 低威胁干扰阈值
#define JAMMING_THRESHOLD_MEDIUM  0.5  // 中威胁干扰阈值
#define JAMMING_THRESHOLD_HIGH    0.7  // 高威胁干扰阈值

// 功率限制常量
#define JAMMING_POWER_MIN        0.1   // 最小干扰功率
#define JAMMING_POWER_MAX        1.0   // 最大干扰功率

// 错误代码
#define JAMMING_SUCCESS          0     // 成功
#define JAMMING_ERROR_INVALID    -1    // 无效参数
#define JAMMING_ERROR_THRESHOLD  -2    // 阈值错误
#define JAMMING_ERROR_POWER      -3    // 功率错误
#define JAMMING_ERROR_TYPE       -4    // 类型错误

#ifdef __cplusplus
}
#endif

#endif // JAMMING_DECISION_H
