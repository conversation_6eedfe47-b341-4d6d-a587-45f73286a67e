QT += core gui network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11 console
CONFIG -= app_bundle

# 项目名称
TARGET = qt_c_radar
TEMPLATE = app
# 定义
DEFINES += QT_DEPRECATED_WARNINGS
DEFINES += HAS_RKNN=1
DEFINES += _CRT_SECURE_NO_WARNINGS
DEFINES += _CRT_NONSTDC_NO_DEPRECATE
DEFINES += _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS

# RKNN库配置
RKNN_ROOT = $$PWD/rknn
INCLUDEPATH += $$RKNN_ROOT/include

# 添加RKNN stub源文件到项目中
SOURCES += $$RKNN_ROOT/lib/rknn_api_stub.c \
    mainwindow.cpp
#LIBS += -L$$RKNN_ROOT/lib -lrknn_api

SOURCES += qt_main_c.cpp \
           src/threat_evaluator.c \
           src/rknn_inference.c \
           src/jamming_decision.c\
           workmodel/workmodel.cpp

# 头文件
HEADERS += Headers/mainwindow.h \
           include/threat_evaluator.h \
           include/rknn_inference.h \
           include/jamming_decision.h\
           workmodel/workmodel.h \
    mainwindow.h



# 包含路径
INCLUDEPATH += . \
               include



# 输出目录
DESTDIR = build
OBJECTS_DIR = build/obj
MOC_DIR = build/moc

# 编码设置
CODECFORTR = GBK
CODECFORSRC = GBK

# Windows特定设置 - VS2015兼容
win32 {
    CONFIG += console
    
    # 禁用常见警告（VS2015兼容）
    QMAKE_CXXFLAGS += /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /wd4101 /wd4819
    
    # 定义Windows宏
    DEFINES += WIN32_LEAN_AND_MEAN NOMINMAX
}

# Linux特定设置
unix {
    QMAKE_CXXFLAGS += -finput-charset=UTF-8 -fexec-charset=GBK
}

# 链接库 - Windows不需要-lm
unix {
    LIBS += -lm
}

# 清理规则
QMAKE_CLEAN += $$DESTDIR/$$TARGET*

FORMS += \
    mainwindow.ui

