#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型输出格式测试程序
测试ONNX模型的输出格式、数值范围和标准化程度
"""

import os
import sys
import json
import numpy as np
import argparse
from typing import Dict, List, Tuple, Optional
from pathlib import Path
# import matplotlib.pyplot as plt  # 可选依赖，用于可视化
# import seaborn as sns  # 可选依赖，用于可视化

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("错误: ONNXRuntime未安装，请运行: pip install onnxruntime")
    sys.exit(1)


class ONNXOutputTester:
    """ONNX模型输出测试器"""
    
    def __init__(self, onnx_path: str):
        """
        初始化测试器
        
        Args:
            onnx_path: ONNX模型文件路径
        """
        self.onnx_path = onnx_path
        self.session = None
        self.input_name = None
        self.output_names = []
        self.output_shapes = {}
        self.test_results = {}
        
        self._load_model()
    
    def _load_model(self):
        """加载ONNX模型"""
        try:
            print(f"加载ONNX模型: {self.onnx_path}")
            self.session = ort.InferenceSession(self.onnx_path)
            
            # 获取输入信息
            input_info = self.session.get_inputs()[0]
            self.input_name = input_info.name
            input_shape = input_info.shape
            print(f"输入名称: {self.input_name}")
            print(f"输入形状: {input_shape}")
            
            # 获取输出信息
            print("\n输出信息:")
            for i, output_info in enumerate(self.session.get_outputs()):
                name = output_info.name
                shape = output_info.shape
                dtype = output_info.type
                self.output_names.append(name)
                self.output_shapes[name] = shape
                print(f"  {i+1}. {name}: {shape} ({dtype})")
            
        except Exception as e:
            print(f"加载模型失败: {e}")
            raise
    
    def generate_test_inputs(self, num_samples: int = 100) -> List[np.ndarray]:
        """
        生成测试输入数据
        
        Args:
            num_samples: 测试样本数量
            
        Returns:
            测试输入列表
        """
        print(f"\n生成 {num_samples} 个测试输入...")
        
        test_inputs = []
        
        for i in range(num_samples):
            # 生成符合雷达状态特征分布的测试数据
            test_input = np.array([
                np.random.uniform(0.0, 2.0),    # 频率 (归一化后 0-20GHz)
                np.random.uniform(0.0, 2.0),    # 脉宽 (归一化后)
                np.random.uniform(0.0, 8.0),    # PRT (归一化后)
                np.random.uniform(0.0, 1.0),    # 功率 (归一化后)
                np.random.uniform(0.0, 1.0),    # 距离 (归一化后)
                np.random.uniform(0.0, 1.0),    # 速度 (归一化后)
                np.random.uniform(0.0, 1.0),    # 方向 (归一化后)
                np.random.uniform(0.0, 1.0),    # 工作模式 (归一化后)
                np.random.uniform(0.2, 1.0),    # 威胁等级 (归一化后)
                np.random.uniform(0.0, 1.0),    # 威胁值
                np.random.uniform(0.0, 1.0),    # 威胁置信度
                np.random.uniform(0.0, 1.0),    # 威胁紧急度
            ], dtype=np.float32)
            
            test_inputs.append(test_input)
        
        return test_inputs
    
    def run_inference_tests(self, test_inputs: List[np.ndarray]) -> Dict:
        """
        运行推理测试
        
        Args:
            test_inputs: 测试输入列表
            
        Returns:
            测试结果字典
        """
        print(f"\n运行推理测试...")
        
        all_outputs = {name: [] for name in self.output_names}
        inference_times = []
        
        for i, test_input in enumerate(test_inputs):
            # 添加batch维度
            input_data = test_input.reshape(1, -1)
            
            # 计时推理
            import time
            start_time = time.time()
            
            try:
                # 运行推理
                outputs = self.session.run(self.output_names, {self.input_name: input_data})
                
                inference_time = time.time() - start_time
                inference_times.append(inference_time)
                
                # 收集输出
                for name, output in zip(self.output_names, outputs):
                    all_outputs[name].append(output[0])  # 移除batch维度
                
                if (i + 1) % 20 == 0:
                    print(f"  完成 {i + 1}/{len(test_inputs)} 个测试")
                    
            except Exception as e:
                print(f"推理失败 (样本 {i+1}): {e}")
                continue
        
        # 转换为numpy数组
        for name in self.output_names:
            if all_outputs[name]:
                all_outputs[name] = np.array(all_outputs[name])
        
        results = {
            'outputs': all_outputs,
            'inference_times': inference_times,
            'num_samples': len(test_inputs),
            'success_rate': len(inference_times) / len(test_inputs)
        }
        
        return results
    
    def analyze_output_statistics(self, results: Dict) -> Dict:
        """
        分析输出统计信息
        
        Args:
            results: 推理结果
            
        Returns:
            统计分析结果
        """
        print(f"\n分析输出统计信息...")
        
        stats = {}
        
        for name, outputs in results['outputs'].items():
            if len(outputs) == 0:
                continue
                
            output_stats = {
                'shape': outputs.shape,
                'dtype': str(outputs.dtype),
                'min': float(np.min(outputs)),
                'max': float(np.max(outputs)),
                'mean': float(np.mean(outputs)),
                'std': float(np.std(outputs)),
                'median': float(np.median(outputs)),
                'q25': float(np.percentile(outputs, 25)),
                'q75': float(np.percentile(outputs, 75))
            }
            
            # 检查是否为概率分布
            if 'probs' in name or 'scores' in name:
                # 检查每行是否和为1
                row_sums = np.sum(outputs, axis=1)
                output_stats['is_probability'] = np.allclose(row_sums, 1.0, atol=1e-3)
                output_stats['sum_mean'] = float(np.mean(row_sums))
                output_stats['sum_std'] = float(np.std(row_sums))
            
            # 检查数值范围
            if np.all(outputs >= -1.1) and np.all(outputs <= 1.1):
                output_stats['range_type'] = 'tanh_like (-1 to 1)'
            elif np.all(outputs >= -0.1) and np.all(outputs <= 1.1):
                output_stats['range_type'] = 'sigmoid_like (0 to 1)'
            else:
                output_stats['range_type'] = 'unrestricted'
            
            stats[name] = output_stats
        
        # 推理性能统计
        if results['inference_times']:
            stats['performance'] = {
                'avg_inference_time': float(np.mean(results['inference_times'])),
                'min_inference_time': float(np.min(results['inference_times'])),
                'max_inference_time': float(np.max(results['inference_times'])),
                'std_inference_time': float(np.std(results['inference_times'])),
                'success_rate': results['success_rate']
            }
        
        return stats
    
    def validate_output_format(self, stats: Dict) -> Dict:
        """
        验证输出格式是否符合预期
        
        Args:
            stats: 统计信息
            
        Returns:
            验证结果
        """
        print(f"\n验证输出格式...")
        
        validation = {
            'overall_passed': True,
            'issues': [],
            'warnings': [],
            'details': {}
        }
        
        expected_outputs = {
            'jamming_type_probs': {
                'expected_shape': (5,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '干扰类型概率分布'
            },
            'comb_params': {
                'expected_shape': (25,),
                'expected_range': (-1, 1),
                'should_sum_to_one': False,
                'description': '梳状谱干扰参数'
            },
            'isrj_params': {
                'expected_shape': (6,),
                'expected_range': (-1, 1),
                'should_sum_to_one': False,
                'description': '间歇采样转发参数'
            },
            'broadband_params': {
                'expected_shape': (1,),
                'expected_range': (-1, 1),
                'should_sum_to_one': False,
                'description': '宽带阻塞噪声参数'
            },
            'smart_noise_params': {
                'expected_shape': (7,),
                'expected_range': (-1, 1),
                'should_sum_to_one': False,
                'description': '灵巧噪声参数'
            },
            'deception_params': {
                'expected_shape': (3,),
                'expected_range': (-1, 1),
                'should_sum_to_one': False,
                'description': '拖引干扰参数'
            },
            'threat_level_probs': {
                'expected_shape': (5,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '威胁等级概率分布'
            },
            'jamming_count_probs': {
                'expected_shape': (4,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '干扰数量概率分布'
            },
            'jamming1_probs': {
                'expected_shape': (5,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '第一干扰类型概率分布'
            },
            'jamming2_probs': {
                'expected_shape': (5,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '第二干扰类型概率分布'
            },
            'jamming3_probs': {
                'expected_shape': (5,),
                'expected_range': (0, 1),
                'should_sum_to_one': True,
                'description': '第三干扰类型概率分布'
            }
        }
        
        for output_name, expected in expected_outputs.items():
            if output_name not in stats:
                validation['issues'].append(f"缺少输出: {output_name}")
                validation['overall_passed'] = False
                continue
            
            output_stats = stats[output_name]
            output_validation = {
                'shape_correct': True,
                'range_correct': True,
                'sum_correct': True,
                'issues': []
            }
            
            # 检查形状
            actual_shape = output_stats['shape'][1:]  # 移除batch维度
            if actual_shape != expected['expected_shape']:
                output_validation['shape_correct'] = False
                output_validation['issues'].append(
                    f"形状不匹配: 期望 {expected['expected_shape']}, 实际 {actual_shape}"
                )
            
            # 检查数值范围
            min_val, max_val = expected['expected_range']
            if output_stats['min'] < min_val - 0.1 or output_stats['max'] > max_val + 0.1:
                output_validation['range_correct'] = False
                output_validation['issues'].append(
                    f"数值范围超出预期: 期望 [{min_val}, {max_val}], "
                    f"实际 [{output_stats['min']:.3f}, {output_stats['max']:.3f}]"
                )
            
            # 检查概率分布
            if expected['should_sum_to_one']:
                if 'is_probability' in output_stats and not output_stats['is_probability']:
                    output_validation['sum_correct'] = False
                    output_validation['issues'].append(
                        f"概率分布不和为1: 平均和 = {output_stats.get('sum_mean', 'N/A'):.3f}"
                    )
            
            validation['details'][output_name] = output_validation
            
            if output_validation['issues']:
                validation['overall_passed'] = False
                validation['issues'].extend([f"{output_name}: {issue}" for issue in output_validation['issues']])
        
        return validation
    
    def generate_report(self, stats: Dict, validation: Dict, output_dir: str = "test_results"):
        """
        生成测试报告
        
        Args:
            stats: 统计信息
            validation: 验证结果
            output_dir: 输出目录
        """
        print(f"\n生成测试报告...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成JSON报告
        report = {
            'model_path': self.onnx_path,
            'test_timestamp': str(np.datetime64('now')),
            'input_info': {
                'name': self.input_name,
                'shape': self.session.get_inputs()[0].shape
            },
            'output_info': {name: shape for name, shape in self.output_shapes.items()},
            'statistics': stats,
            'validation': validation
        }
        
        report_path = os.path.join(output_dir, "onnx_test_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 生成文本报告
        text_report = self._generate_text_report(stats, validation)
        text_path = os.path.join(output_dir, "onnx_test_report.txt")
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ 测试报告已生成:")
        print(f"   JSON报告: {report_path}")
        print(f"   文本报告: {text_path}")
        
        return report_path
    
    def _generate_text_report(self, stats: Dict, validation: Dict) -> str:
        """生成文本格式报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("ONNX模型输出格式测试报告")
        lines.append("=" * 80)
        lines.append(f"模型文件: {self.onnx_path}")
        lines.append(f"测试时间: {np.datetime64('now')}")
        lines.append("")
        
        # 验证结果摘要
        lines.append("验证结果摘要:")
        lines.append(f"  总体状态: {'✅ 通过' if validation['overall_passed'] else '❌ 失败'}")
        if validation['issues']:
            lines.append("  发现问题:")
            for issue in validation['issues']:
                lines.append(f"    - {issue}")
        lines.append("")
        
        # 输出统计信息
        lines.append("输出统计信息:")
        for name, stat in stats.items():
            if name == 'performance':
                continue
            lines.append(f"  {name}:")
            lines.append(f"    形状: {stat['shape']}")
            lines.append(f"    数值范围: [{stat['min']:.3f}, {stat['max']:.3f}]")
            lines.append(f"    均值±标准差: {stat['mean']:.3f}±{stat['std']:.3f}")
            if 'is_probability' in stat:
                lines.append(f"    概率分布: {'是' if stat['is_probability'] else '否'}")
            lines.append(f"    范围类型: {stat.get('range_type', '未知')}")
            lines.append("")
        
        # 性能信息
        if 'performance' in stats:
            perf = stats['performance']
            lines.append("性能信息:")
            lines.append(f"  平均推理时间: {perf['avg_inference_time']*1000:.2f} ms")
            lines.append(f"  推理时间范围: [{perf['min_inference_time']*1000:.2f}, {perf['max_inference_time']*1000:.2f}] ms")
            lines.append(f"  成功率: {perf['success_rate']*100:.1f}%")
            lines.append("")
        
        return "\n".join(lines)
    
    def run_complete_test(self, num_samples: int = 100, output_dir: str = "test_results") -> str:
        """
        运行完整测试流程
        
        Args:
            num_samples: 测试样本数量
            output_dir: 输出目录
            
        Returns:
            报告文件路径
        """
        print("🚀 开始ONNX模型输出格式测试")
        
        # 生成测试输入
        test_inputs = self.generate_test_inputs(num_samples)
        
        # 运行推理测试
        results = self.run_inference_tests(test_inputs)
        
        # 分析统计信息
        stats = self.analyze_output_statistics(results)
        
        # 验证输出格式
        validation = self.validate_output_format(stats)
        
        # 生成报告
        report_path = self.generate_report(stats, validation, output_dir)
        
        # 打印摘要
        print("\n" + "=" * 60)
        print("🎉 测试完成!")
        print("=" * 60)
        print(f"测试样本: {num_samples}")
        print(f"成功率: {results['success_rate']*100:.1f}%")
        print(f"验证结果: {'✅ 通过' if validation['overall_passed'] else '❌ 失败'}")
        
        if validation['issues']:
            print("发现的问题:")
            for issue in validation['issues'][:5]:  # 只显示前5个问题
                print(f"  - {issue}")
            if len(validation['issues']) > 5:
                print(f"  ... 还有 {len(validation['issues']) - 5} 个问题")
        
        return report_path


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ONNX模型输出格式测试程序')
    parser.add_argument('--model', '-m', type=str, default='models/onnx/jamming_model_ppo.onnx',
                        help='ONNX模型文件路径')
    parser.add_argument('--samples', '-s', type=int, default=100,
                        help='测试样本数量')
    parser.add_argument('--output-dir', '-o', type=str, default='test_results',
                        help='测试结果输出目录')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        return 1
    
    try:
        # 创建测试器
        tester = ONNXOutputTester(args.model)
        
        # 运行测试
        report_path = tester.run_complete_test(args.samples, args.output_dir)
        
        print(f"\n详细报告: {report_path}")
        return 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
