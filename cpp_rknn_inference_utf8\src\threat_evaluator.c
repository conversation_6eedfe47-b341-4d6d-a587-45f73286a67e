﻿#include "threat_evaluator.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/**
 * 威胁评估器实现
 */

// 威胁评估器内部结构
typedef struct {
    int initialized;
    double frequency_weight;
    double power_weight;
    double distance_weight;
    double mode_weight;
    
    // 统计信息
    int assessment_count;
    double total_assessment_time;
    double min_assessment_time;
    double max_assessment_time;
} ThreatEvaluatorImpl;

/**
 * 创建威胁评估器
 */
ThreatEvaluator* threat_evaluator_create(void) {
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)malloc(sizeof(ThreatEvaluatorImpl));
    if (!impl) {
        return NULL;
    }
    
    // 初始化权重参数
    impl->initialized = 1;
    impl->frequency_weight = 0.3;
    impl->power_weight = 0.4;
    impl->distance_weight = 0.2;
    impl->mode_weight = 0.1;
    
    // 初始化统计信息
    impl->assessment_count = 0;
    impl->total_assessment_time = 0.0;
    impl->min_assessment_time = 999999.0;
    impl->max_assessment_time = 0.0;
    
    printf("威胁评估器初始化成功\n");
    return (ThreatEvaluator*)impl;
}

/**
 * 销毁威胁评估器
 */
void threat_evaluator_destroy(ThreatEvaluator* evaluator) {
    if (evaluator) {
        free(evaluator);
        printf("威胁评估器已销毁\n");
    }
}

/**
 * 计算频率威胁度
 */
static double calculate_frequency_threat(double frequency_mhz) {
    // X波段 (8-12 GHz) - 高威胁
    if (frequency_mhz >= 8000.0 && frequency_mhz <= 12000.0) {
        return 0.9;
    }
    // Ku波段 (12-18 GHz) - 高威胁
    else if (frequency_mhz >= 12000.0 && frequency_mhz <= 18000.0) {
        return 0.8;
    }
    // C波段 (4-8 GHz) - 中等威胁
    else if (frequency_mhz >= 4000.0 && frequency_mhz <= 8000.0) {
        return 0.6;
    }
    // S波段 (2-4 GHz) - 中等威胁
    else if (frequency_mhz >= 2000.0 && frequency_mhz <= 4000.0) {
        return 0.5;
    }
    // L波段 (1-2 GHz) - 低威胁
    else if (frequency_mhz >= 1000.0 && frequency_mhz <= 2000.0) {
        return 0.3;
    }
    // 其他频段 - 极低威胁
    else {
        return 0.1;
    }
}

/**
 * 计算功率威胁度
 */
static double calculate_power_threat(double power_w) {
    // 对数尺度计算功率威胁度
    if (power_w <= 0) {
        return 0.0;
    }
    
    double log_power = log10(power_w);
    
    // 高功率 (>1MW) - 高威胁
    if (log_power >= 6.0) {
        return 0.9;
    }
    // 中高功率 (100kW-1MW) - 中高威胁
    else if (log_power >= 5.0) {
        return 0.7;
    }
    // 中等功率 (10kW-100kW) - 中等威胁
    else if (log_power >= 4.0) {
        return 0.5;
    }
    // 低功率 (1kW-10kW) - 低威胁
    else if (log_power >= 3.0) {
        return 0.3;
    }
    // 极低功率 (<1kW) - 极低威胁
    else {
        return 0.1;
    }
}

/**
 * 计算距离威胁度
 */
static double calculate_distance_threat(double distance_km) {
    // 距离越近威胁越大
    if (distance_km <= 0) {
        return 1.0;
    }
    
    // 极近距离 (<20km) - 极高威胁
    if (distance_km < 20.0) {
        return 1.0;
    }
    // 近距离 (20-50km) - 高威胁
    else if (distance_km < 50.0) {
        return 0.8;
    }
    // 中等距离 (50-100km) - 中等威胁
    else if (distance_km < 100.0) {
        return 0.6;
    }
    // 远距离 (100-200km) - 低威胁
    else if (distance_km < 200.0) {
        return 0.4;
    }
    // 极远距离 (>200km) - 极低威胁
    else {
        return 0.2;
    }
}

/**
 * 计算工作模式威胁度
 */
static double calculate_mode_threat(int work_mode) {
    switch (work_mode) {
        case WORK_MODE_TERMINAL:    // 终末制导模式
            return 1.0;
        case WORK_MODE_GUIDANCE:    // 制导模式
            return 0.9;
        case WORK_MODE_TRACK:       // 跟踪模式
            return 0.7;
        case WORK_MODE_SEARCH:      // 搜索模式
            return 0.4;
        case WORK_MODE_SILENT:      // 静默模式
            return 0.1;
        default:
            return 0.3;             // 未知模式
    }
}

/**
 * 执行威胁评估
 */
int threat_evaluator_assess(ThreatEvaluator* evaluator, 
                           const RadarParameters* radar_params,
                           ThreatAssessmentData* result) {
    if (!evaluator || !radar_params || !result) {
        return -1;
    }
    
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)evaluator;
    if (!impl->initialized) {
        return -2;
    }
    
    // 计算各项威胁度
    double freq_threat = calculate_frequency_threat(radar_params->frequency_mhz);
    double power_threat = calculate_power_threat(radar_params->power_w);
    double distance_threat = calculate_distance_threat(radar_params->distance_km);
    double mode_threat = calculate_mode_threat(radar_params->work_mode);
    
    // 计算综合威胁值
    double threat_value = 
        freq_threat * impl->frequency_weight +
        power_threat * impl->power_weight +
        distance_threat * impl->distance_weight +
        mode_threat * impl->mode_weight;
    
    // 确定威胁等级
    int threat_level;
    if (threat_value >= 0.8) {
        threat_level = THREAT_LEVEL_CRITICAL;
    } else if (threat_value >= 0.6) {
        threat_level = THREAT_LEVEL_HIGH;
    } else if (threat_value >= 0.4) {
        threat_level = THREAT_LEVEL_MEDIUM;
    } else if (threat_value >= 0.2) {
        threat_level = THREAT_LEVEL_LOW;
    } else {
        threat_level = THREAT_LEVEL_MINIMAL;
    }
    
    // 计算置信度和优先级
    double confidence = 0.7 + threat_value * 0.3;  // 基础置信度 + 威胁值调整
    double priority = threat_value;                 // 优先级等于威胁值
    
    // 填充结果
    result->threat_level = threat_level;
    result->threat_value = threat_value;
    result->confidence = confidence;
    result->priority = priority;
    result->frequency_threat = freq_threat;
    result->power_threat = power_threat;
    result->distance_threat = distance_threat;
    result->mode_threat = mode_threat;
    
    // 更新统计信息
    impl->assessment_count++;
    
    return 0;
}

/**
 * 获取威胁等级描述
 */
const char* threat_get_level_description(int threat_level) {
    switch (threat_level) {
        case THREAT_LEVEL_CRITICAL:
            return "极高威胁";
        case THREAT_LEVEL_HIGH:
            return "高威胁";
        case THREAT_LEVEL_MEDIUM:
            return "中等威胁";
        case THREAT_LEVEL_LOW:
            return "低威胁";
        case THREAT_LEVEL_MINIMAL:
            return "极低威胁";
        default:
            return "未知威胁等级";
    }
}

/**
 * 设置权重参数
 */
int threat_evaluator_set_weights(ThreatEvaluator* evaluator,
                                double freq_weight,
                                double power_weight, 
                                double distance_weight,
                                double mode_weight) {
    if (!evaluator) {
        return -1;
    }
    
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)evaluator;
    
    // 检查权重和是否为1
    double total_weight = freq_weight + power_weight + distance_weight + mode_weight;
    if (fabs(total_weight - 1.0) > 0.001) {
        printf("警告: 权重总和不等于1.0 (实际: %.3f)\n", total_weight);
        return -2;
    }
    
    impl->frequency_weight = freq_weight;
    impl->power_weight = power_weight;
    impl->distance_weight = distance_weight;
    impl->mode_weight = mode_weight;
    
    printf("威胁评估器权重已更新\n");
    return 0;
}

/**
 * 获取统计信息
 */
const char* threat_evaluator_get_stats(ThreatEvaluator* evaluator) {
    static char stats_buffer[256];
    
    if (!evaluator) {
        snprintf(stats_buffer, sizeof(stats_buffer), "威胁评估器无效");
        return stats_buffer;
    }
    
    ThreatEvaluatorImpl* impl = (ThreatEvaluatorImpl*)evaluator;
    
    snprintf(stats_buffer, sizeof(stats_buffer),
             "评估次数: %d, 权重配置: [频率:%.2f, 功率:%.2f, 距离:%.2f, 模式:%.2f]",
             impl->assessment_count,
             impl->frequency_weight,
             impl->power_weight,
             impl->distance_weight,
             impl->mode_weight);
    
    return stats_buffer;
}
