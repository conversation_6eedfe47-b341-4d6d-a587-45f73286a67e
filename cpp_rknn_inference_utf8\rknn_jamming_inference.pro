QT += core
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle

# 项目信息
TARGET = rknn_jamming_inference
TEMPLATE = app
VERSION = 1.0.0

# 编译器配置
QMAKE_CFLAGS += -std=c99
QMAKE_CXXFLAGS += -std=c++11

# UTF-8编码支持
win32 {
    QMAKE_CXXFLAGS += /utf-8
    QMAKE_CFLAGS += /utf-8
} else {
    QMAKE_CXXFLAGS += -fexec-charset=UTF-8
    QMAKE_CFLAGS += -fexec-charset=UTF-8
}

# 包含路径
INCLUDEPATH += $$PWD/include

# 源文件
SOURCES += \
    qt_main_c.cpp \
    src/threat_evaluator.c \
    src/rknn_inference.c \
    src/jamming_decision.c

# 头文件
HEADERS += \
    include/threat_evaluator.h \
    include/rknn_inference.h \
    include/jamming_decision.h

# 输出目录
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/debug
    OBJECTS_DIR = $$PWD/debug/obj
    MOC_DIR = $$PWD/debug/moc
    RCC_DIR = $$PWD/debug/rcc
    UI_DIR = $$PWD/debug/ui
} else {
    DESTDIR = $$PWD/release
    OBJECTS_DIR = $$PWD/release/obj
    MOC_DIR = $$PWD/release/moc
    RCC_DIR = $$PWD/release/rcc
    UI_DIR = $$PWD/release/ui
}

# 编译选项
QMAKE_CFLAGS += -Wall -Wextra
QMAKE_CXXFLAGS += -Wall -Wextra

# Debug模式特定配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG=1
    QMAKE_CFLAGS += -g
    QMAKE_CXXFLAGS += -g
}

# Release模式特定配置
CONFIG(release, debug|release) {
    DEFINES += NDEBUG
    QMAKE_CFLAGS += -O2
    QMAKE_CXXFLAGS += -O2
}

# 平台特定配置
win32 {
    CONFIG += console
    DEFINES += WIN32_LEAN_AND_MEAN
}

unix {
    CONFIG += link_pkgconfig
}

# 清理规则
QMAKE_CLEAN += $$DESTDIR/$$TARGET*

# 自定义目标
run.commands = $$DESTDIR/$$TARGET
run.depends = $$DESTDIR/$$TARGET
QMAKE_EXTRA_TARGETS += run

# 项目描述
QMAKE_TARGET_COMPANY = "雷达干扰系统"
QMAKE_TARGET_PRODUCT = "RKNN雷达干扰推理引擎"
QMAKE_TARGET_DESCRIPTION = "基于RKNN的雷达干扰决策推理系统"
QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"
