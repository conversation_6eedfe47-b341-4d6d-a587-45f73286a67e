# 雷达电子战智能干扰决策系统 - 依赖包管理

## 目录结构

```
packages/
├── training_packages/          # 训练阶段依赖包
│   ├── *.whl                  # Python包文件
│   ├── *.tar.gz              # 源码包文件
│   └── install_training_packages.sh  # 安装脚本
├── onnx_packages/             # ONNX转换阶段依赖包
│   ├── *.whl                  # Python包文件
│   └── install_onnx_packages.sh      # 安装脚本
├── rknn_packages/             # RKNN部署阶段依赖包
│   ├── *.whl                  # Python包文件
│   ├── RKNN_INSTALL_README.md # RKNN安装说明
│   └── install_rknn_packages.sh      # 安装脚本
└── install_all_dependencies.sh       # 主安装脚本
```

## 使用方法

### 1. 下载依赖包

```bash
# 下载所有阶段的依赖包
python manage_dependencies.py --download-all

# 下载特定阶段的依赖包
python manage_dependencies.py --stage training
python manage_dependencies.py --stage onnx
python manage_dependencies.py --stage rknn
```

### 2. 安装依赖包

```bash
# 交互式安装
bash install_all_dependencies.sh

# 直接安装特定阶段
bash training_packages/install_training_packages.sh
bash onnx_packages/install_onnx_packages.sh
bash rknn_packages/install_rknn_packages.sh
```

### 3. 离线安装

```bash
# 使用pip离线安装
pip install --find-links training_packages --no-index torch
pip install --find-links onnx_packages --no-index onnx
pip install --find-links rknn_packages --no-index opencv-python
```

## 各阶段依赖说明

### 训练阶段 (training_packages/)
- PyTorch深度学习框架
- CuPy GPU加速计算
- 科学计算库(NumPy, SciPy)
- 数据可视化库(Matplotlib)
- 进度条和工具库

### ONNX转换阶段 (onnx_packages/)
- ONNX核心库和运行时
- ONNX优化工具
- 模型转换工具
- 模型可视化工具

### RKNN部署阶段 (rknn_packages/)
- RKNN工具包(需手动下载)
- OpenCV图像处理
- 系统监控工具
- C++编译工具

## 注意事项

1. **RKNN工具包**: 需要从瑞芯微官网手动下载
2. **GPU支持**: CuPy需要对应的CUDA版本
3. **Python版本**: 推荐使用Python 3.8-3.10
4. **系统要求**: Linux x86_64 (RKNN工具包要求)

## 故障排除

### 常见问题

1. **包下载失败**: 检查网络连接和镜像源
2. **CUDA版本不匹配**: 安装对应版本的CuPy
3. **RKNN工具包缺失**: 从官网下载对应版本
4. **权限问题**: 使用sudo或虚拟环境

### 联系支持

如有问题，请查看项目文档或提交Issue。
