﻿#include <QCoreApplication>
#include <QTextCodec>
#include <cstdio>
#include <cstring>
#include "mainwindow.h"
#include "qapplication.h"
#include "workmodel/workmodel.h"

// 包含所有核心模块
#include "include/threat_evaluator.h"
#include "include/rknn_inference.h"
#include "include/jamming_decision.h"

// 全局模块句柄
static ThreatEvaluator* g_threat_evaluator = NULL;
static RKNNInference* g_rknn_inference = NULL;
static JammingDecision* g_jamming_decision = NULL;

// 模拟雷达输入数据
void simulate_radar_input(RadarParameters* radar, int scenario) {
    const char* scenario_names[] = {
        "高威胁场景", "中等威胁场景", "高频场景", "低威胁场景", "极低威胁场景"
    };

    switch (scenario) {
        case 0: // 高威胁场??
            radar->frequency_mhz = 8000.0;
            radar->pulse_width_us = 2.0;
            radar->prt_us = 2000.0;
            radar->power_w = 500000.0;
            radar->distance_km = 80.0;
            radar->speed_ms = 200.0;
            radar->direction_deg = 30.0;
            radar->work_mode = 1;
            break;

        case 1: // 中等威胁场景
            radar->frequency_mhz = 5000.0;
            radar->pulse_width_us = 5.0;
            radar->prt_us = 5000.0;
            radar->power_w = 200000.0;
            radar->distance_km = 120.0;
            radar->speed_ms = 100.0;
            radar->direction_deg = 15.0;
            radar->work_mode = 1;
            break;

        case 2: // 高频场景
            radar->frequency_mhz = 12000.0;
            radar->pulse_width_us = 0.5;
            radar->prt_us = 800.0;
            radar->power_w = 2000000.0;
            radar->distance_km = 30.0;
            radar->speed_ms = 500.0;
            radar->direction_deg = 60.0;
            radar->work_mode = 3;
            break;

        case 3: // 低威胁场??
            radar->frequency_mhz = 3000.0;
            radar->pulse_width_us = 10.0;
            radar->prt_us = 10000.0;
            radar->power_w = 50000.0;
            radar->distance_km = 200.0;
            radar->speed_ms = 50.0;
            radar->direction_deg = 0.0;
            radar->work_mode = 0;
            break;

        case 4: // 跟踪场景
            radar->frequency_mhz = 10000.0;
            radar->pulse_width_us = 1.0;
            radar->prt_us = 1000.0;
            radar->power_w = 1000000.0;
            radar->distance_km = 50.0;
            radar->speed_ms = 300.0;
            radar->direction_deg = 45.0;
            radar->work_mode = 2;
            break;
    }

    printf("当前场景: %s\n", scenario_names[scenario]);
    printf("接收到雷达参数:\n");
    printf("  频率: %.1f MHz\n", radar->frequency_mhz);
    printf("  脉宽: %.1f μs\n", radar->pulse_width_us);
    printf("  PRT: %.1f μs\n", radar->prt_us);
    printf("  功率: %.0f W\n", radar->power_w);
    printf("  距离: %.1f km\n", radar->distance_km);
    printf("  速度: %.1f m/s\n", radar->speed_ms);
    printf("  方向: %.1f°\n", radar->direction_deg);
    printf("  工作模式: %d\n", radar->work_mode);
}

// 显示完整决策结果
void display_complete_result(const ThreatAssessmentData* threat_data,
                           const JammingDecisionResult* jamming_result) {
    printf("=== 完整决策结果 ===\n");

    // 威胁评估结果
    printf("威胁评估:\n");
    printf("  威胁等级: %d (%s)\n", threat_data->threat_level,
           threat_get_level_description(threat_data->threat_level));

    // 干扰决策结果
    printf("干扰决策:\n");
    if (jamming_result->jamming_count > 0) {
        if (jamming_result->use_combination) {
            printf("  决策类型: 组合干扰 (%d??)\n", jamming_result->jamming_count);
        } else {
            printf("  决策类型: 单一干扰\n");
        }

        for (int i = 0; i < jamming_result->jamming_count; i++) {
            printf("  干扰%d: %s (概率: %.3f)\n",
                   i+1,
                   jamming_get_type_name(jamming_result->jamming_types[i]),
                   jamming_result->jamming_probs[i]);
        }

        printf("  策略描述: %s\n", jamming_result->strategy_description);
        printf("  决策置信??: %.3f\n", jamming_result->confidence);
    } else {
        printf("  决策类型: 无需干扰\n");
        printf("  原因: %s\n", jamming_result->strategy_description);
    }
}

// 运行完整测试场景
void run_complete_test_scenario(int scenario) {

    // 1. 模拟雷达输入
    RadarParameters radar_params;
    simulate_radar_input(&radar_params, scenario);

    // 2. 威胁评估
    ThreatAssessmentData threat_data;
    int threat_result = threat_evaluator_assess(g_threat_evaluator, &radar_params, &threat_data);

    // 3. 准备RKNN输入
    RKNNInput rknn_input;

    // 准备12维特征向量
    rknn_input.input_data[0] = (float)(radar_params.frequency_mhz / 10000.0);    // 频率归一化
    rknn_input.input_data[1] = (float)(radar_params.pulse_width_us / 10.0);      // 脉宽归一化
    rknn_input.input_data[2] = (float)(radar_params.prt_us / 10000.0);           // PRT归一化
    rknn_input.input_data[3] = (float)(radar_params.power_w / 1000000.0);        // 功率归一化
    rknn_input.input_data[4] = (float)(radar_params.distance_km / 300.0);        // 距离归一化
    rknn_input.input_data[5] = (float)(radar_params.speed_ms / 1000.0);          // 速度归一化
    rknn_input.input_data[6] = (float)(radar_params.direction_deg / 360.0);      // 方向归一化
    rknn_input.input_data[7] = (float)(radar_params.work_mode / 4.0);            // 工作模式归一化
    rknn_input.input_data[8] = (float)threat_data.threat_value;                  // 威胁值
    rknn_input.input_data[9] = (float)(threat_data.threat_level / 5.0);          // 威胁等级归一化
    rknn_input.input_data[10] = (float)threat_data.confidence;                   // 威胁置信度
    rknn_input.input_data[11] = (float)threat_data.priority;                     // 威胁优先级
    rknn_input.input_size = 12;

    // 4. RKNN推理
    RKNNOutput rknn_output;
    int rknn_result = rknn_inference_predict(g_rknn_inference, &rknn_input, &rknn_output);

    // 5. 干扰决策
    JammingDecisionResult jamming_result;
    int jamming_decision_result = jamming_decision_decide(g_jamming_decision, threat_data.threat_level,
                                                         threat_data.threat_value, &rknn_output,
                                                         radar_params.work_mode, &jamming_result);

    if (jamming_decision_result != 0) {
        printf("错误: 干扰决策失败，错误码: %d\n", jamming_decision_result);
        rknn_free_output(&rknn_output);
        return;
    }

    // 6. 显示完整结果
    display_complete_result(&threat_data, &jamming_result);

    // 7. 清理资源
    rknn_free_output(&rknn_output);
}

//初始化
int initialize_all_modules(const char* model_path) {
    // 初始化威胁评估器
    g_threat_evaluator = threat_evaluator_create();
    if (!g_threat_evaluator) {
        printf("错误: 威胁评估器初始化失败\n");
        return -1;
    }

    // 初始化RKNN推理
    g_rknn_inference = rknn_inference_create(model_path);
    if (!g_rknn_inference) {
        printf("错误: RKNN推理器初始化失败\n");
        threat_evaluator_destroy(g_threat_evaluator);
        return -2;
    }

    // 初始化干扰决策器
    g_jamming_decision = jamming_decision_create();
    if (!g_jamming_decision) {
        printf("错误: 干扰决策器初始化失败\n");
        rknn_inference_destroy(g_rknn_inference);
        threat_evaluator_destroy(g_threat_evaluator);
        return -3;
    }

    printf("所有核心模块初始化成功\n");
    return 0;
}

void destroy_all_modules(void) {
    if (g_jamming_decision) {
        jamming_decision_destroy(g_jamming_decision);
        g_jamming_decision = NULL;
    }

    if (g_rknn_inference) {
        rknn_inference_destroy(g_rknn_inference);
        g_rknn_inference = NULL;
    }

    if (g_threat_evaluator) {
        threat_evaluator_destroy(g_threat_evaluator);
        g_threat_evaluator = NULL;
    }

    printf("所有核心模块已销毁\n");
}


int main(int argc, char *argv[]) {
    int ret;

    QApplication app(argc, argv);
    Workmodel wm;
    MainWindow w;

    QObject::connect(&g_,&globalMannager::sig_commState, &g_, &globalMannager::slot_commMsg);

    qDebug()<<wm.Net_tcpserver_init(QHostAddress("************"), 8080);

//    QThread::sleep(1);
//    wm.Net_tcpclient_init(QHostAddress("************"), 12111);


    // 初始化模型路径
//const char* model_path = "models/jamming_decision.rknn";

//    // 运行测试
//    for (int i = 0; i < 5; i++) {
//        run_complete_test_scenario(i);
//    }

//    destroy_all_modules();
    w.show();
    ret = app.exec();

    wm.Net_tcpserver_close();

    return ret;
}
