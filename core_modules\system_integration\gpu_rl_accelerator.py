"""
GPU加速强化学习模块
使用PyTorch和CuPy加速Q-learning计算
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import time
import pickle

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    torch = None
    TORCH_AVAILABLE = False

try:
    import cupy as cp
    GPU_AVAILABLE = True
except ImportError:
    cp = np
    GPU_AVAILABLE = False


class ActorNetwork(nn.Module):
    """改进的Actor网络 - 解决梯度爆炸问题"""

    def __init__(self, state_dim: int, hidden_dims: List[int] = [128, 64]):
        super(ActorNetwork, self).__init__()

        # 简化的特征提取器 - 减少层数
        self.feature_extractor = nn.Sequential(
            nn.Linear(state_dim, hidden_dims[0]),
            nn.LayerNorm(hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(0.2),  # 增加dropout

            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.LayerNorm(hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(0.1)
        )

        # 移除威胁等级预测头 - 威胁等级由专门的威胁评估模块提供

        # 干扰类型头 - 简化结构
        self.jamming_type_head = nn.Sequential(
            nn.Linear(hidden_dims[1], 32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 5)  # 移除Softmax
        )

        # 标准格式决策头 - 输出标准格式
        self.threat_level_head = nn.Sequential(
            nn.Linear(hidden_dims[1], 32),
            nn.ReLU(),
            nn.Linear(32, 5)  # 威胁等级 1-5
        )

        self.jamming_count_head = nn.Sequential(
            nn.Linear(hidden_dims[1], 32),
            nn.ReLU(),
            nn.Linear(32, 4)  # 干扰个数 0-3
        )

        self.jamming_sequence_head = nn.Sequential(
            nn.Linear(hidden_dims[1], 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 15)  # 干扰序列：[干扰1类型(5) + 干扰2类型(5) + 干扰3类型(5)]
        )

        # 简化的干扰参数头
        self._build_simplified_param_heads(hidden_dims[1])

        # 保守的权重初始化
        self.apply(self._init_weights_conservative)

    def _build_simplified_param_heads(self, feature_dim):
        """构建稳定的干扰参数输出头 - 保持原有参数数量"""

        # 梳状谱参数 (25个参数) - 改进稳定性
        self.comb_params_head = nn.Sequential(
            nn.Linear(feature_dim, 64),  # 减少中间层大小
            nn.ReLU(),
            nn.Dropout(0.2),  # 增加dropout
            nn.Linear(64, 25),
            nn.Tanh()  # 输出-1到1，后续缩放
        )

        # 间歇采样转发参数 (6个参数)
        self.isrj_params_head = nn.Sequential(
            nn.Linear(feature_dim, 32),  # 减少中间层大小
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 6),
            nn.Tanh()
        )

        # 宽带阻塞噪声参数 (1个参数)
        self.broadband_params_head = nn.Sequential(
            nn.Linear(feature_dim, 16),  # 减少中间层大小
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Tanh()
        )

        # 灵巧噪声参数 (7个参数)
        self.smart_noise_params_head = nn.Sequential(
            nn.Linear(feature_dim, 32),  # 减少中间层大小
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 7),
            nn.Tanh()
        )

        # 拖引参数 (3个参数)
        self.deception_params_head = nn.Sequential(
            nn.Linear(feature_dim, 16),  # 减少中间层大小
            nn.ReLU(),
            nn.Linear(16, 3),
            nn.Tanh()
        )

    def _init_weights_conservative(self, module):
        """保守的权重初始化 - 防止梯度爆炸"""
        if isinstance(module, nn.Linear):
            # 使用更小的初始化范围
            torch.nn.init.xavier_normal_(module.weight, gain=0.5)
            if module.bias is not None:
                module.bias.data.fill_(0.0)  # 偏置初始化为0
        elif isinstance(module, nn.LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)

    def forward(self, state):
        # 添加数值稳定性检查
        if torch.isnan(state).any() or torch.isinf(state).any():
            print("警告: 输入状态包含NaN或无穷大值")
            state = torch.nan_to_num(state, nan=0.0, posinf=1.0, neginf=-1.0)

        features = self.feature_extractor(state)

        # 检查特征提取器输出
        if torch.isnan(features).any():
            print("警告: 特征提取器输出包含NaN")
            features = torch.nan_to_num(features, nan=0.0)

        # 输出干扰类型和参数 - 保留原有输出用于兼容性
        jamming_type_logits = self.jamming_type_head(features)

        # 标准格式输出
        threat_level_logits = self.threat_level_head(features)
        jamming_count_logits = self.jamming_count_head(features)
        jamming_sequence_logits = self.jamming_sequence_head(features)

        # 解析干扰序列
        jamming1_logits = jamming_sequence_logits[:, 0:5]   # 第1个干扰类型
        jamming2_logits = jamming_sequence_logits[:, 5:10]  # 第2个干扰类型
        jamming3_logits = jamming_sequence_logits[:, 10:15] # 第3个干扰类型

        # 使用稳定的softmax
        outputs = {
            # 原有输出（兼容性）
            'jamming_type_probs': torch.softmax(jamming_type_logits, dim=-1),
            'comb_params': self.comb_params_head(features),
            'isrj_params': self.isrj_params_head(features),
            'broadband_params': self.broadband_params_head(features),
            'smart_noise_params': self.smart_noise_params_head(features),
            'deception_params': self.deception_params_head(features),

            # 标准格式输出
            'threat_level_probs': torch.softmax(threat_level_logits, dim=-1),      # 威胁等级概率 [5维]
            'jamming_count_probs': torch.softmax(jamming_count_logits, dim=-1),    # 干扰个数概率 [4维: 0,1,2,3]
            'jamming1_probs': torch.softmax(jamming1_logits, dim=-1),              # 第1个干扰类型概率 [5维]
            'jamming2_probs': torch.softmax(jamming2_logits, dim=-1),              # 第2个干扰类型概率 [5维]
            'jamming3_probs': torch.softmax(jamming3_logits, dim=-1)               # 第3个干扰类型概率 [5维]
        }

        # 数值稳定性检查
        for key, value in outputs.items():
            if torch.isnan(value).any():
                print(f"警告: {key} 输出包含NaN")
                outputs[key] = torch.nan_to_num(value, nan=0.0)

        return outputs

    def predict_standard_format(self, state):
        """预测标准格式输出：威胁等级 + 干扰个数 + [干扰类型 + 参数] + [干扰类型 + 参数] + ..."""
        with torch.no_grad():
            if len(state.shape) == 1:
                state = state.unsqueeze(0)

            outputs = self.forward(state)

            # 解析标准格式输出
            threat_level = torch.argmax(outputs['threat_level_probs'][0]).item() + 1  # 1-5
            jamming_count = torch.argmax(outputs['jamming_count_probs'][0]).item()    # 0-3

            # 构建标准格式结果
            standard_output = [threat_level, jamming_count]

            # 根据干扰个数添加干扰类型和参数
            jamming_probs_list = [
                outputs['jamming1_probs'][0],
                outputs['jamming2_probs'][0],
                outputs['jamming3_probs'][0]
            ]

            for i in range(jamming_count):
                if i < len(jamming_probs_list):
                    jamming_type = torch.argmax(jamming_probs_list[i]).item()
                    standard_output.append(jamming_type)

                    # 添加对应的参数
                    if jamming_type == 1:  # 间歇采样
                        params = outputs['isrj_params'][0].cpu().numpy().tolist()
                    elif jamming_type == 2:  # 宽带噪声
                        params = outputs['broadband_params'][0].cpu().numpy().tolist()
                    elif jamming_type == 3:  # 灵巧噪声
                        params = outputs['smart_noise_params'][0].cpu().numpy().tolist()
                    elif jamming_type == 4:  # 拖引干扰
                        params = outputs['deception_params'][0].cpu().numpy().tolist()
                    else:  # 无干扰
                        params = [0.0]

                    standard_output.extend(params)

            return {
                'standard_output': standard_output,
                'parsed_result': {
                    'threat_level': threat_level,
                    'jamming_count': jamming_count,
                    'jamming_types': [torch.argmax(jamming_probs_list[i]).item()
                                    for i in range(min(jamming_count, len(jamming_probs_list)))],
                    'confidence': {
                        'threat_level': torch.max(outputs['threat_level_probs'][0]).item(),
                        'jamming_count': torch.max(outputs['jamming_count_probs'][0]).item(),
                        'jamming_types': [torch.max(jamming_probs_list[i]).item()
                                        for i in range(min(jamming_count, len(jamming_probs_list)))]
                    }
                },
                'raw_outputs': {
                    'threat_level_probs': outputs['threat_level_probs'][0].cpu().numpy().tolist(),
                    'jamming_count_probs': outputs['jamming_count_probs'][0].cpu().numpy().tolist(),
                    'jamming1_probs': outputs['jamming1_probs'][0].cpu().numpy().tolist(),
                    'jamming2_probs': outputs['jamming2_probs'][0].cpu().numpy().tolist(),
                    'jamming3_probs': outputs['jamming3_probs'][0].cpu().numpy().tolist()
                }
            }

    def predict_end_to_end_decision(self, state):
        """端到端决策预测 - 直接输出最终干扰决策"""
        with torch.no_grad():
            if len(state.shape) == 1:
                state = state.unsqueeze(0)

            outputs = self.forward(state)

            # 解析端到端决策
            should_jam = outputs['should_jam_prob'][0, 0].item() > 0.5
            primary_jamming_type = torch.argmax(outputs['primary_jamming_probs'][0]).item()
            secondary_jamming_type = torch.argmax(outputs['secondary_jamming_probs'][0]).item()
            jamming_power = outputs['jamming_power'][0, 0].item()
            combination_type = torch.argmax(outputs['combination_type_probs'][0]).item()

            # 构建干扰组合
            jamming_combinations = []
            if should_jam:
                if combination_type == 0:  # 无组合，单一干扰
                    if primary_jamming_type > 0:
                        jamming_combinations.append(primary_jamming_type)
                elif combination_type == 1:  # 双重组合
                    if primary_jamming_type > 0:
                        jamming_combinations.append(primary_jamming_type)
                    if secondary_jamming_type > 0 and secondary_jamming_type != primary_jamming_type:
                        jamming_combinations.append(secondary_jamming_type)
                elif combination_type == 2:  # 多重组合（暂时等同于双重）
                    if primary_jamming_type > 0:
                        jamming_combinations.append(primary_jamming_type)
                    if secondary_jamming_type > 0 and secondary_jamming_type != primary_jamming_type:
                        jamming_combinations.append(secondary_jamming_type)

            # 获取干扰参数
            jamming_combinations_with_params = []
            for jtype in jamming_combinations:
                if jtype == 1:  # 间歇采样
                    params = outputs['isrj_params'][0].cpu().numpy().tolist()
                elif jtype == 2:  # 宽带噪声
                    params = outputs['broadband_params'][0].cpu().numpy().tolist()
                elif jtype == 3:  # 灵巧噪声
                    params = outputs['smart_noise_params'][0].cpu().numpy().tolist()
                elif jtype == 4:  # 拖引干扰
                    params = outputs['deception_params'][0].cpu().numpy().tolist()
                else:
                    params = [0.0]
                jamming_combinations_with_params.append((jtype, params))

            return {
                'should_jam': should_jam,
                'jamming_type': primary_jamming_type if should_jam else 0,
                'jamming_power': jamming_power,
                'jamming_combinations': jamming_combinations_with_params,
                'combination_type': combination_type,
                'decision_confidence': {
                    'should_jam': outputs['should_jam_prob'][0, 0].item(),
                    'primary_type': torch.max(outputs['primary_jamming_probs'][0]).item(),
                    'secondary_type': torch.max(outputs['secondary_jamming_probs'][0]).item(),
                    'combination_type': torch.max(outputs['combination_type_probs'][0]).item()
                },
                'raw_outputs': {
                    'should_jam_prob': outputs['should_jam_prob'][0, 0].item(),
                    'primary_jamming_probs': outputs['primary_jamming_probs'][0].cpu().numpy().tolist(),
                    'secondary_jamming_probs': outputs['secondary_jamming_probs'][0].cpu().numpy().tolist(),
                    'jamming_power': jamming_power,
                    'combination_type_probs': outputs['combination_type_probs'][0].cpu().numpy().tolist()
                }
            }

    def predict_jamming_output(self, state):
        """预测标准输出格式: [威胁等级, 干扰个数, 干扰1类型, 干扰1参数..., 干扰2类型, 干扰2参数...]"""
        with torch.no_grad():
            if len(state.shape) == 1:
                state = state.unsqueeze(0)

            outputs = self.forward(state)

            # 威胁等级由外部威胁评估模块提供，这里不再预测
            threat_level = 3  # 默认中等威胁等级

            # 分析干扰类型概率，选择多个干扰
            jamming_probs = outputs['jamming_type_probs'][0]
            jamming_decisions = self._select_multiple_jamming_types(jamming_probs, outputs)

            # 构建标准输出格式: [威胁等级, 干扰个数, 干扰1类型, 干扰1参数..., 干扰2类型, 干扰2参数...]
            result = [threat_level, len(jamming_decisions)]

            for jamming_type, params in jamming_decisions:
                result.append(jamming_type)
                result.extend(params)

            return result

    def _select_multiple_jamming_types(self, jamming_probs, outputs, threshold=0.1, max_jammings=2):
        """
        根据概率选择多个干扰类型

        Args:
            jamming_probs: 干扰类型概率分布 (5,)
            outputs: 网络输出字典
            threshold: 概率阈值
            max_jammings: 最大干扰个数

        Returns:
            List[Tuple[int, List[float]]]: [(干扰类型, 参数列表), ...]
        """
        jamming_decisions = []

        # 获取概率排序的干扰类型
        probs_with_idx = [(i, prob.item()) for i, prob in enumerate(jamming_probs)]
        probs_with_idx.sort(key=lambda x: x[1], reverse=True)

        for jamming_type, prob in probs_with_idx:
            if prob > threshold:  # 包含所有干扰类型
                # 根据干扰类型提取对应参数
                if jamming_type == 0:  # 梳状谱
                    params = self._decode_comb_params(outputs['comb_params'][0])
                elif jamming_type == 1:  # 间歇采样
                    params = self._decode_isrj_params(outputs['isrj_params'][0])
                elif jamming_type == 2:  # 宽带噪声
                    params = self._decode_broadband_params(outputs['broadband_params'][0])
                elif jamming_type == 3:  # 灵巧噪声
                    params = self._decode_smart_noise_params(outputs['smart_noise_params'][0])
                elif jamming_type == 4:  # 拖引干扰
                    params = self._decode_deception_params(outputs['deception_params'][0])
                else:
                    params = [0.0]

                jamming_decisions.append((jamming_type, params))

                # 限制最大干扰个数
                if len(jamming_decisions) >= max_jammings:
                    break

        return jamming_decisions

    def _decode_comb_params(self, raw_params):
        """解码梳状谱参数"""
        params = []

        # 梳状谱个数 (1-8)
        count = int(raw_params[0] * 7) + 1
        params.append(float(count))

        # 频偏序列 (最多8个)
        for i in range(8):
            if i < count:
                freq_offset = raw_params[i + 1] * 200 - 100  # -100 to 100 MHz
            else:
                freq_offset = 0.0
            params.append(float(freq_offset))

        # 闪烁周期 (1-100 μs)
        flicker_period = raw_params[9] * 99 + 1
        params.append(float(flicker_period))

        # 保持时间序列 (最多15个)
        for i in range(15):
            if i < count:
                hold_time = raw_params[i + 10] * 9.5 + 0.5  # 0.5-10 μs
            else:
                hold_time = 0.0
            params.append(float(hold_time))

        return params

    def _decode_isrj_params(self, raw_params):
        """解码间歇采样转发参数"""
        return [
            float(raw_params[0] * 190 + 10),      # 重复转发时间间隔 10-200 μs
            float(int(raw_params[1] + 0.5)),      # 间歇采样开关 0/1
            float(raw_params[2] * 4.5 + 0.5),     # 间歇采样周期 0.5-5 μs
            float(raw_params[3] * 1.9 + 0.1),     # 间歇采样宽度 0.1-2 μs
            float(raw_params[4] * 90000 + 10000), # 干扰覆盖距离 10000-100000 m
            float(raw_params[5] * 2.5 + 0.5)      # 脉冲采样长度 0.5-3 μs
        ]

    def _decode_broadband_params(self, raw_params):
        """解码宽带阻塞噪声参数"""
        return [float(int(raw_params[0] * 20))]  # 噪声带宽选择 0-20

    def _decode_smart_noise_params(self, raw_params):
        """解码灵巧噪声参数"""
        return [
            float(int(raw_params[0] * 20)),        # 噪声带宽选择 0-20
            float(int(raw_params[1] * 2) + 1),     # 噪声源选择 1-3
            float(int(raw_params[2]) + 1),         # 多普勒闪烁模式 1-2
            float(raw_params[3] * 4.5 + 0.5),      # 闪烁出现时间 0.5-5 μs
            float(raw_params[4] * 4.5 + 0.5),      # 闪烁消失时间 0.5-5 μs
            float(raw_params[5] * 90 + 10),        # 多普勒噪声带宽 10-100 kHz
            float(raw_params[6] * 90 + 10)         # 多普勒噪声跳变周期 10-100 kHz
        ]

    def _decode_deception_params(self, raw_params):
        """解码拖引参数"""
        return [
            float(raw_params[0] * 10 + 1),    # 拖引距离偏移 1-11 km
            float(raw_params[1] * 500 + 50),  # 拖引速度偏移 50-550 m/s
            float(raw_params[2] * 2 + 0.5)    # 拖引持续时间 0.5-2.5 s
        ]


class CriticNetwork(nn.Module):
    """Critic网络 - 评估状态-动作对的价值"""

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128, 64]):
        super(CriticNetwork, self).__init__()

        # 状态编码器
        state_layers = []
        prev_dim = state_dim
        for hidden_dim in hidden_dims[:-1]:
            state_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        self.state_encoder = nn.Sequential(*state_layers)

        # 动作编码器 - 将动作参数编码为特征
        self.action_encoder = nn.Sequential(
            nn.Linear(action_dim, hidden_dims[-1]),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1], hidden_dims[-1])
        )

        # 价值输出层
        self.value_head = nn.Sequential(
            nn.Linear(prev_dim + hidden_dims[-1], hidden_dims[-1]),
            nn.ReLU(),
            nn.Linear(hidden_dims[-1], 1)
        )

        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                module.bias.data.fill_(0.01)

    def forward(self, state, action):
        state_features = self.state_encoder(state)
        action_features = self.action_encoder(action)

        # 拼接状态和动作特征
        combined_features = torch.cat([state_features, action_features], dim=-1)
        value = self.value_head(combined_features)

        return value


class PPOCriticNetwork(nn.Module):
    """PPO Critic网络 - V(s)状态价值函数"""

    def __init__(self, state_dim: int, hidden_dims: List[int] = [256, 128, 64]):
        super(PPOCriticNetwork, self).__init__()

        # 状态价值网络
        layers = []
        prev_dim = state_dim

        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim

        # 输出层
        layers.append(nn.Linear(prev_dim, 1))

        self.value_network = nn.Sequential(*layers)
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                module.bias.data.fill_(0.01)

    def forward(self, state):
        """输入状态，输出状态价值"""
        return self.value_network(state)


class GPUQNetwork(nn.Module):
    """GPU加速的Q网络 - 支持直接参数输出（保留兼容性）"""

    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128, 64],
                 direct_params: bool = False, use_attention: bool = False):
        super(GPUQNetwork, self).__init__()

        self.direct_params = direct_params
        self.action_dim = action_dim
        self.use_attention = use_attention

        # 共享特征提取器
        feature_layers = []
        prev_dim = state_dim

        for hidden_dim in hidden_dims:
            feature_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim

        self.feature_extractor = nn.Sequential(*feature_layers)

        # 注意力机制（可选）
        if self.use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=prev_dim,
                num_heads=8,
                dropout=0.1,
                batch_first=True
            )

        if direct_params:
            # 直接参数输出模式
            self._build_direct_param_heads(prev_dim)
        else:
            # 传统Q值输出模式
            self.q_head = nn.Linear(prev_dim, action_dim)

        # 初始化权重
        self.apply(self._init_weights)

    def _build_direct_param_heads(self, feature_dim):
        """构建直接参数输出头"""

        # 威胁等级输出 (1-5)
        self.threat_head = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 5),
            nn.Softmax(dim=1)
        )

        # 干扰决策输出 (是否干扰)
        self.jam_decision_head = nn.Sequential(
            nn.Linear(feature_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 2),
            nn.Softmax(dim=1)
        )

        # 干扰类型输出 (5种类型)
        self.jamming_type_head = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 5),
            nn.Softmax(dim=1)
        )

        # 梳状谱参数输出 (1个数量 + 8个频偏 + 8个周期 + 8个保持时间 = 25个参数)
        self.comb_params_head = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 25),
            nn.Sigmoid()  # 输出0-1，后续缩放到实际范围
        )

        # 间歇采样转发参数输出 (6个参数)
        self.isrj_params_head = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 6),
            nn.Sigmoid()
        )

        # 宽带阻塞噪声参数输出 (1个参数)
        self.broadband_params_head = nn.Sequential(
            nn.Linear(feature_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()
        )

        # 灵巧噪声参数输出 (7个参数)
        self.smart_noise_params_head = nn.Sequential(
            nn.Linear(feature_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 7),
            nn.Sigmoid()
        )

        # 拖引参数输出 (3个参数)
        self.deception_params_head = nn.Sequential(
            nn.Linear(feature_dim, 32),
            nn.ReLU(),
            nn.Linear(32, 3),
            nn.Sigmoid()
        )

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            module.bias.data.fill_(0.01)

    def forward(self, state):
        features = self.feature_extractor(state)

        # 应用注意力机制
        if self.use_attention:
            # 重塑为序列格式 (batch_size, seq_len=1, feature_dim)
            if len(features.shape) == 2:
                features_seq = features.unsqueeze(1)
            else:
                features_seq = features

            # 自注意力
            attended_features, attention_weights = self.attention(
                features_seq, features_seq, features_seq
            )
            features = attended_features.squeeze(1)

        if self.direct_params:
            return self._forward_direct_params(features)
        else:
            return self.q_head(features)

    def _forward_direct_params(self, features):
        """直接参数输出的前向传播"""
        outputs = {
            'threat_probs': self.threat_head(features),
            'jam_decision_probs': self.jam_decision_head(features),
            'jamming_type_probs': self.jamming_type_head(features),
            'comb_params': self.comb_params_head(features),
            'isrj_params': self.isrj_params_head(features),
            'broadband_params': self.broadband_params_head(features),
            'smart_noise_params': self.smart_noise_params_head(features),
            'deception_params': self.deception_params_head(features)
        }
        return outputs

    def predict_jamming_params(self, x):
        """预测完整的干扰参数 - 标准输出格式"""
        if not self.direct_params:
            raise ValueError("网络未配置为直接参数输出模式")

        with torch.no_grad():
            if len(x.shape) == 1:
                x = x.unsqueeze(0)

            outputs = self.forward(x)

            # 解析输出
            threat_level = torch.argmax(outputs['threat_probs'], dim=1).item() + 1
            should_jam = torch.argmax(outputs['jam_decision_probs'], dim=1).item() == 1

            if not should_jam:
                return torch.tensor([threat_level, 0, -1, -1], dtype=torch.float32)

            jamming_type = torch.argmax(outputs['jamming_type_probs'], dim=1).item()

            # 根据干扰类型生成具体参数
            if jamming_type == 0:  # 梳状谱
                params = self._decode_comb_params(outputs['comb_params'][0])
            elif jamming_type == 1:  # 间歇采样转发
                params = self._decode_isrj_params(outputs['isrj_params'][0])
            elif jamming_type == 2:  # 宽带阻塞噪声
                params = self._decode_broadband_params(outputs['broadband_params'][0])
            elif jamming_type == 3:  # 灵巧噪声
                params = self._decode_smart_noise_params(outputs['smart_noise_params'][0])
            elif jamming_type == 4:  # 拖引
                params = self._decode_deception_params(outputs['deception_params'][0])
            else:
                params = [1.0]

            # 构建标准输出格式
            result = [threat_level, 1, jamming_type] + params + [-1]
            return torch.tensor(result, dtype=torch.float32)

    def _decode_comb_params(self, raw_params):
        """解码梳状谱参数"""
        params = []

        # 梳状谱个数 (1-8)
        count = int(raw_params[0] * 7) + 1
        params.append(float(count))

        # 频偏序列 (8个) -1000到1000 kHz
        for i in range(8):
            if i < count:
                freq_offset = (raw_params[i + 1] - 0.5) * 2000  # -1000到1000
            else:
                freq_offset = 0.0
            params.append(float(freq_offset))

        # 闪烁周期序列 (8个) 10-110 μs
        for i in range(8):
            if i < count:
                flicker_period = raw_params[i + 9] * 100 + 10  # 10-110
            else:
                flicker_period = 0.0
            params.append(float(flicker_period))

        # 闪烁保持时间序列 (8个) 5-25 μs
        for i in range(8):
            if i < count:
                hold_time = raw_params[i + 17] * 20 + 5  # 5-25
            else:
                hold_time = 0.0
            params.append(float(hold_time))

        return params

    def _decode_isrj_params(self, raw_params):
        """解码间歇采样转发参数"""
        return [
            float(raw_params[0] * 190 + 10),      # 重复转发时间间隔 10-200 μs
            float(int(raw_params[1] + 0.5)),      # 间歇采样开关 0/1
            float(raw_params[2] * 4.5 + 0.5),     # 间歇采样周期 0.5-5 μs
            float(raw_params[3] * 1.9 + 0.1),     # 间歇采样宽度 0.1-2 μs
            float(raw_params[4] * 90000 + 10000), # 干扰覆盖距离 10000-100000 m
            float(raw_params[5] * 2.5 + 0.5)      # 脉冲采样长度 0.5-3 μs
        ]

    def _decode_broadband_params(self, raw_params):
        """解码宽带阻塞噪声参数"""
        return [float(int(raw_params[0] * 20))]  # 噪声带宽选择 0-20

    def _decode_smart_noise_params(self, raw_params):
        """解码灵巧噪声参数"""
        return [
            float(int(raw_params[0] * 20)),        # 噪声带宽选择 0-20
            float(int(raw_params[1] * 2) + 1),     # 噪声源选择 1-3
            float(int(raw_params[2]) + 1),         # 多普勒闪烁模式 1-2
            float(raw_params[3] * 19 + 1),         # 闪烁保持时间 1-20 μs
            float(raw_params[4] * 4.5 + 0.5),      # 闪烁消失时间 0.5-5 μs
            float(raw_params[5] * 90 + 10),        # 多普勒噪声带宽 10-100 kHz
            float(raw_params[6] * 90 + 10)         # 多普勒噪声跳变周期 10-100 kHz
        ]

    def _decode_deception_params(self, raw_params):
        """解码拖引参数"""
        return [
            float(raw_params[0] * 10 + 1),    # 拖引距离偏移 1-11 km
            float(raw_params[1] * 500 + 50),  # 拖引速度偏移 50-550 m/s
            float(raw_params[2] * 2 + 0.5)    # 拖引持续时间 0.5-2.5 s
        ]


class ActorCriticAccelerator:
    """PPO Actor-Critic GPU加速强化学习器 - 专为雷达电子战设计"""

    def __init__(self, config: Dict):
        """
        初始化PPO强化学习加速器

        Args:
            config: 配置字典
        """
        self.config = config
        self.gpu_enabled = config.get('enable_gpu', False) and TORCH_AVAILABLE and torch.cuda.is_available()
        self.device = torch.device(f"cuda:{config.get('gpu_device', 0)}") if self.gpu_enabled else torch.device('cpu')
        self.batch_size = config.get('batch_size', 32)
        self.use_mixed_precision = config.get('use_mixed_precision', False)

        # 网络参数
        self.state_dim = config.get('state_dim', 12)
        self.action_dim = config.get('action_dim', 71)  # 修正：5+24+42=71 (兼容5+标准格式24+参数42)
        self.actor_lr = config.get('actor_lr', 0.0001)
        self.critic_lr = config.get('critic_lr', 0.0002)
        self.gamma = config.get('gamma', 0.99)

        # PPO参数
        self.ppo_epochs = config.get('ppo_epochs', 4)
        self.ppo_clip = config.get('ppo_clip', 0.2)
        self.entropy_coef = config.get('entropy_coef', 0.01)
        self.value_coef = config.get('value_coef', 0.5)
        self.max_grad_norm = config.get('max_grad_norm', 0.5)

        # 初始化网络（无论是否启用GPU都需要初始化）
        self._initialize_ppo_networks()

        # PPO经验缓冲区（不同于经验回放）
        self.memory = []

        # 训练统计
        self.training_stats = {
            'total_steps': 0,
            'total_episodes': 0,
            'actor_loss': 0.0,
            'critic_loss': 0.0,
            'entropy_loss': 0.0,
            'average_reward': 0.0
        }

        print(f"PPO强化学习加速器初始化完成")
        print(f"设备: {self.device}")
        print(f"混合精度: {'开启' if self.use_mixed_precision else '关闭'}")
        print(f"批处理大小: {self.batch_size}")
        print(f"Actor学习率: {self.actor_lr}")
        print(f"Critic学习率: {self.critic_lr}")
        print(f"PPO轮数: {self.ppo_epochs}")
        print(f"PPO裁剪: {self.ppo_clip}")

    def _initialize_ppo_networks(self):
        """初始化PPO网络"""
        # Actor网络
        self.actor = ActorNetwork(self.state_dim).to(self.device)

        # Critic网络（PPO使用状态价值函数）
        self.critic = PPOCriticNetwork(self.state_dim).to(self.device)

        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.actor_lr)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=self.critic_lr)

        # 混合精度训练
        if self.use_mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()

        print(f"Actor网络参数: {sum(p.numel() for p in self.actor.parameters())}")
        print(f"Critic网络参数: {sum(p.numel() for p in self.critic.parameters())}")

    def _convert_actor_output_to_action_vector(self, actor_output):
        """将Actor输出转换为动作向量"""
        action_vector = []

        # 威胁等级不再由Actor网络预测，跳过

        # 干扰类型 (5维概率分布) - 兼容性保留
        jamming_probs = actor_output['jamming_type_probs']
        action_vector.append(jamming_probs)

        # 标准格式输出 (19维) - 新增
        threat_level_probs = actor_output['threat_level_probs']      # 5维
        jamming_count_probs = actor_output['jamming_count_probs']    # 4维
        jamming1_probs = actor_output['jamming1_probs']              # 5维
        jamming2_probs = actor_output['jamming2_probs']              # 5维
        jamming3_probs = actor_output['jamming3_probs']              # 5维

        action_vector.append(threat_level_probs)        # 5维
        action_vector.append(jamming_count_probs)       # 4维
        action_vector.append(jamming1_probs)            # 5维
        action_vector.append(jamming2_probs)            # 5维
        action_vector.append(jamming3_probs)            # 5维

        # 各种干扰参数
        action_vector.append(actor_output['comb_params'])        # 25维
        action_vector.append(actor_output['isrj_params'])        # 6维
        action_vector.append(actor_output['broadband_params'])   # 1维
        action_vector.append(actor_output['smart_noise_params']) # 7维
        action_vector.append(actor_output['deception_params'])   # 3维

        # 调试信息 - 更新期望维度
        total_expected = 5 + 5 + 4 + 5 + 5 + 5 + 25 + 6 + 1 + 7 + 3  # 71维
        actual_dims = [v.shape[-1] for v in action_vector]
        actual_total = sum(actual_dims)

        if actual_total != total_expected:
            print(f"警告：动作向量维度不匹配！")
            print(f"期望维度: {total_expected}")
            print(f"实际维度: {actual_dims} (总计: {actual_total})")
            labels = ['jamming_type', 'threat_level', 'jamming_count', 'jamming1', 'jamming2', 'jamming3', 'comb', 'isrj', 'broadband', 'smart_noise', 'deception']
            for i, (name, tensor) in enumerate(zip(labels, action_vector)):
                print(f"  {name}: {tensor.shape}")

        # 拼接所有动作参数
        result = torch.cat(action_vector, dim=-1)
        return result

    def select_action(self, state_dict: Dict, training: bool = True, threat_level: int = None) -> Dict:
        """使用Actor网络选择动作"""
        if not self.gpu_enabled:
            return self._select_action_cpu(state_dict, training)

        state_tensor = self.encode_state(state_dict).unsqueeze(0)

        # 从状态字典中提取工作模式
        work_mode = state_dict.get('work_mode', 1)  # 默认搜索模式

        with torch.no_grad():
            actor_output = self.actor(state_tensor)

            # 转换为动作字典，传入威胁等级和工作模式
            return self._convert_actor_output_to_action_dict(actor_output, state_tensor, threat_level, work_mode)

    def _convert_actor_output_to_action_dict(self, actor_output, state_tensor=None, threat_level=None, work_mode=None):
        """将Actor输出转换为动作字典 - 使用PPO Agent的原生组合干扰输出"""
        # 基于威胁等级的干扰决策
        if threat_level is None:
            threat_level = 3  # 默认中等威胁
        if work_mode is None:
            work_mode = 1  # 默认搜索模式

        # 获取PPO Agent的原生输出
        jamming_type_probs = actor_output['jamming_type_probs'].cpu().numpy().flatten()
        jamming_count_probs = actor_output['jamming_count_probs'].cpu().numpy().flatten()
        jamming1_probs = actor_output['jamming1_probs'].cpu().numpy().flatten()
        jamming2_probs = actor_output['jamming2_probs'].cpu().numpy().flatten()
        jamming3_probs = actor_output['jamming3_probs'].cpu().numpy().flatten()

        print(f"   [DEBUG] PPO原生输出:")
        print(f"   [DEBUG] 威胁等级: {threat_level}, 工作模式: {work_mode}")
        print(f"   [DEBUG] 干扰类型概率: {jamming_type_probs}")
        print(f"   [DEBUG] 干扰数量概率: {jamming_count_probs}")
        print(f"   [DEBUG] 干扰1概率: {jamming1_probs}")
        print(f"   [DEBUG] 干扰2概率: {jamming2_probs}")
        print(f"   [DEBUG] 干扰3概率: {jamming3_probs}")

        jamming_combinations = []  # 初始化多干扰组合

        # 威胁等级决策逻辑
        if threat_level >= 5:  # 威胁等级5：极低威胁，不干扰
            should_jam = False
            jamming_type = 0
            jamming_power = 0.0
        elif threat_level >= 4:  # 威胁等级4：低威胁，根据工作模式决策
            if work_mode == 0:  # 静默模式：不干扰
                print(f"   [DEBUG] 威胁等级4 + 静默模式 → 不干扰")
                should_jam = False
                jamming_type = 0
                jamming_power = 0.0
            else:  # 非静默模式：必须选择一种干扰
                print(f"   [DEBUG] 威胁等级4 + 工作模式{work_mode} → 必须选择一种干扰")
                jamming_type = np.argmax(jamming_type_probs)  # 0-4都是有效干扰类型
                should_jam = True  # 非静默模式下必须干扰
                jamming_power = 0.3  # 低功率
                print(f"   [DEBUG] 选择干扰类型{jamming_type}，置信度{jamming_type_probs[jamming_type]:.3f}")
        else:  # 威胁等级1-3：需要干扰
            should_jam = True

            # 使用PPO Agent的原生组合干扰输出
            print(f"   [DEBUG] 威胁等级{threat_level}，使用PPO原生组合输出")

            # 1. 首先确定干扰数量
            jamming_count = np.argmax(jamming_count_probs) + 1  # 1-4个干扰
            print(f"   [DEBUG] PPO建议干扰数量: {jamming_count}")

            # 2. 根据威胁等级调整干扰数量
            if threat_level <= 2:  # 高威胁：允许多干扰
                max_allowed = 2
            elif threat_level == 3:  # 中威胁：限制干扰数量
                max_allowed = 2
            else:  # 低威胁：单一干扰
                max_allowed = 1

            final_jamming_count = min(jamming_count, max_allowed)
            print(f"   [DEBUG] 调整后干扰数量: {final_jamming_count}")

            # 3. 选择具体的干扰类型
            if final_jamming_count == 1:
                # 单一干扰：使用主要干扰类型
                jamming_type = int(np.argmax(jamming1_probs))  # 0-4都是有效干扰类型
                jamming_combinations = []
                print(f"   [DEBUG] 单一干扰类型: {jamming_type} (0=梳状谱,1=间歇采样,2=宽带阻塞,3=灵巧噪声,4=拖引)")

            elif final_jamming_count == 2:
                # 双干扰组合
                jtype1 = int(np.argmax(jamming1_probs))  # 0-4都是有效干扰类型
                jtype2 = int(np.argmax(jamming2_probs))  # 0-4都是有效干扰类型

                # 确保两个干扰类型不同
                if jtype1 == jtype2:
                    # 选择第二高概率的类型
                    sorted_indices = np.argsort(jamming2_probs)[::-1]  # 从所有5种类型中选择
                    for idx in sorted_indices:
                        if int(idx) != jtype1:
                            jtype2 = int(idx)
                            break

                jamming_combinations = [(jtype1, []), (jtype2, [])]
                jamming_type = jtype1  # 主要干扰类型
                print(f"   [DEBUG] 双干扰组合: [{jtype1}, {jtype2}] (0=梳状谱,1=间歇采样,2=宽带阻塞,3=灵巧噪声,4=拖引)")

            else:  # final_jamming_count >= 3
                # 三干扰组合
                jtype1 = int(np.argmax(jamming1_probs))  # 0-4都是有效干扰类型
                jtype2 = int(np.argmax(jamming2_probs))  # 0-4都是有效干扰类型
                jtype3 = int(np.argmax(jamming3_probs))  # 0-4都是有效干扰类型

                # 确保三个干扰类型不同
                types = [jtype1, jtype2, jtype3]
                unique_types = []
                for jtype in types:
                    if jtype not in unique_types:
                        unique_types.append(jtype)

                # 如果不足3个不同类型，补充其他类型
                while len(unique_types) < 3:
                    for candidate in range(0, 5):  # 干扰类型0-4（5种）
                        if candidate not in unique_types:
                            unique_types.append(candidate)
                            break
                    break  # 防止无限循环

                jamming_combinations = [(jtype, []) for jtype in unique_types[:3]]
                jamming_type = unique_types[0]  # 主要干扰类型
                print(f"   [DEBUG] 三干扰组合: {unique_types[:3]} (0=梳状谱,1=间歇采样,2=宽带阻塞,3=灵巧噪声,4=拖引)")

            # 威胁等级越高，功率越大
            base_power = (6 - threat_level) / 5.0  # 威胁1→1.0, 威胁3→0.6
            # 不同干扰类型的功率调整
            type_multiplier = {1: 0.8, 2: 0.6, 3: 0.7, 4: 0.9}.get(jamming_type, 0.7)
            jamming_power = base_power * type_multiplier

        return {
            'jamming_type': jamming_type,
            'should_jam': should_jam,
            'jamming_power': jamming_power,
            'jamming_frequency': 10e9,  # 默认频率
            'actor_output': actor_output,
            'jamming_type_probs': jamming_type_probs.tolist(),
            'jamming_combinations': jamming_combinations,  # 多干扰组合
            'threat_level': threat_level,  # 添加威胁等级信息
            'standard_output': self.actor.predict_jamming_output(state_tensor.squeeze(0)) if state_tensor is not None else None  # 添加标准格式输出
        }

    def _select_multiple_jamming_types(self, jamming_probs, outputs, threshold=0.1, max_jammings=2):
        """
        基于效果学习的智能组合选择

        Args:
            jamming_probs: 干扰类型概率分布 (5,)
            outputs: 网络输出字典
            threshold: 概率阈值
            max_jammings: 最大干扰个数

        Returns:
            List[Tuple[int, List[float]]]: [(干扰类型, 参数列表), ...]
        """
        jamming_decisions = []

        # 获取概率排序的干扰类型
        if hasattr(jamming_probs, 'cpu'):
            jamming_probs = jamming_probs.cpu().numpy()

        # 1. 筛选候选干扰类型
        candidates = []
        for i, prob in enumerate(jamming_probs):
            if i > 0 and prob > threshold:  # 排除类型0（无干扰）
                candidates.append((i, float(prob)))

        print(f"   [DEBUG] 概率阈值: {threshold}")
        print(f"   [DEBUG] 所有概率: {[(i, float(p)) for i, p in enumerate(jamming_probs)]}")
        print(f"   [DEBUG] 候选干扰: {candidates}")

        if len(candidates) == 0:
            print(f"   [DEBUG] 没有候选干扰类型，返回空组合")
            return jamming_decisions

        # 2. 如果只有一个候选，直接选择
        if len(candidates) == 1 or max_jammings == 1:
            jamming_type = candidates[0][0]
            params = self._get_jamming_params(jamming_type, outputs)
            jamming_decisions.append((jamming_type, params))
            print(f"   [DEBUG] 单一候选或限制为1个，选择: {jamming_type}")
            return jamming_decisions

        # 3. 多干扰组合选择：基于协同效应评估
        print(f"   [DEBUG] 多候选干扰，进行组合选择")
        best_combination = self._select_best_combination(candidates, outputs, max_jammings)
        print(f"   [DEBUG] 最佳组合: {best_combination}")

        for jamming_type in best_combination:
            params = self._get_jamming_params(jamming_type, outputs)
            jamming_decisions.append((jamming_type, params))

        return jamming_decisions

    def _get_jamming_params(self, jamming_type: int, outputs: dict) -> list:
        """获取指定干扰类型的参数"""
        if jamming_type == 1:  # 间歇采样
            raw_params = outputs['isrj_params'].cpu().numpy().flatten()
            return raw_params.tolist()
        elif jamming_type == 2:  # 宽带噪声
            raw_params = outputs['broadband_params'].cpu().numpy().flatten()
            return raw_params.tolist()
        elif jamming_type == 3:  # 灵巧噪声
            raw_params = outputs['smart_noise_params'].cpu().numpy().flatten()
            return raw_params.tolist()
        elif jamming_type == 4:  # 拖引干扰
            raw_params = outputs['deception_params'].cpu().numpy().flatten()
            return raw_params.tolist()
        else:
            return [0.0]

    def _select_best_combination(self, candidates: list, outputs: dict, max_jammings: int) -> list:
        """基于模型学习的组合评分选择最佳组合"""
        # 组合索引映射（10种两两组合）
        combination_mapping = {
            0: (1, 2),  # 间歇采样 + 宽带噪声
            1: (1, 3),  # 间歇采样 + 灵巧噪声
            2: (1, 4),  # 间歇采样 + 拖引
            3: (2, 3),  # 宽带噪声 + 灵巧噪声
            4: (2, 4),  # 宽带噪声 + 拖引
            5: (3, 4),  # 灵巧噪声 + 拖引
            6: (1, 1),  # 间歇采样单独（占位）
            7: (2, 2),  # 宽带噪声单独（占位）
            8: (3, 3),  # 灵巧噪声单独（占位）
            9: (4, 4),  # 拖引单独（占位）
        }

        if max_jammings == 2 and len(candidates) >= 2:
            # 使用模型输出的组合评分
            combination_scores = outputs['combination_scores'].cpu().numpy().flatten()

            best_score = 0.0
            best_combination = []

            # 评估所有可能的两两组合
            for i in range(len(candidates)):
                for j in range(i + 1, len(candidates)):
                    type1, prob1 = candidates[i]
                    type2, prob2 = candidates[j]

                    # 查找对应的组合索引
                    combination_key = tuple(sorted([type1, type2]))
                    combination_idx = None
                    for idx, combo in combination_mapping.items():
                        if combo == combination_key:
                            combination_idx = idx
                            break

                    if combination_idx is not None:
                        # 使用模型学习的组合评分
                        model_score = combination_scores[combination_idx]
                        # 结合概率和模型评分
                        total_score = (prob1 * prob2) * (1.0 + model_score)

                        if total_score > best_score:
                            best_score = total_score
                            best_combination = [type1, type2]

            if best_combination:
                return best_combination

        # 回退到概率排序选择
        candidates.sort(key=lambda x: x[1], reverse=True)
        return [candidates[i][0] for i in range(min(max_jammings, len(candidates)))]

    def train_ppo(self) -> Dict:
        """PPO训练方法"""
        if not self.gpu_enabled or len(self.memory) < self.batch_size:
            return {'actor_loss': 0.0, 'critic_loss': 0.0, 'entropy_loss': 0.0}

        # 准备PPO训练数据
        states, actions, rewards, next_states, dones, old_log_probs, advantages, returns = self._prepare_ppo_data()

        total_actor_loss = 0.0
        total_critic_loss = 0.0
        total_entropy_loss = 0.0

        # PPO多轮更新
        for epoch in range(self.ppo_epochs):
            # 随机打乱数据
            indices = torch.randperm(len(states))

            for start in range(0, len(states), self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]

                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]

                # 计算当前策略的log概率
                actor_outputs = self.actor(batch_states)

                # 检查Actor输出是否包含NaN
                if any(torch.isnan(v).any() for v in actor_outputs.values()):
                    print("警告: Actor输出包含NaN，跳过此批次")
                    continue

                new_log_probs, entropy = self._compute_log_probs_and_entropy(actor_outputs, batch_actions)

                # 检查log概率和熵
                if torch.isnan(new_log_probs).any() or torch.isnan(entropy).any():
                    print("警告: log概率或熵包含NaN，跳过此批次")
                    continue

                # PPO裁剪损失 - 添加数值稳定性
                log_ratio = new_log_probs - batch_old_log_probs
                log_ratio = torch.clamp(log_ratio, -10, 10)  # 限制比率范围
                ratio = torch.exp(log_ratio)

                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.ppo_clip, 1 + self.ppo_clip) * batch_advantages
                actor_loss = -torch.min(surr1, surr2).mean()

                # 熵损失（鼓励探索）
                entropy_loss = -entropy.mean()

                # Critic损失
                values = self.critic(batch_states)
                critic_loss = F.mse_loss(values.squeeze(), batch_returns)

                # 检查损失是否为NaN
                if torch.isnan(actor_loss) or torch.isnan(critic_loss) or torch.isnan(entropy_loss):
                    print("警告: 损失包含NaN，跳过此批次")
                    continue

                # 总损失
                total_loss = actor_loss + self.value_coef * critic_loss + self.entropy_coef * entropy_loss

                # 反向传播
                self.actor_optimizer.zero_grad()
                self.critic_optimizer.zero_grad()
                total_loss.backward()

                # 检查梯度是否包含NaN
                actor_grad_norm = torch.nn.utils.clip_grad_norm_(self.actor.parameters(), self.max_grad_norm)
                critic_grad_norm = torch.nn.utils.clip_grad_norm_(self.critic.parameters(), self.max_grad_norm)

                if torch.isnan(actor_grad_norm) or torch.isnan(critic_grad_norm):
                    print("警告: 梯度包含NaN，跳过参数更新")
                    continue

                self.actor_optimizer.step()
                self.critic_optimizer.step()

                total_actor_loss += actor_loss.item()
                total_critic_loss += critic_loss.item()
                total_entropy_loss += entropy_loss.item()

        # 清空经验缓冲区（PPO是on-policy）
        self.memory.clear()

        num_updates = self.ppo_epochs * (len(states) // self.batch_size)
        return {
            'actor_loss': total_actor_loss / num_updates,
            'critic_loss': total_critic_loss / num_updates,
            'entropy_loss': total_entropy_loss / num_updates
        }



    def _prepare_ppo_data(self):
        """准备PPO训练数据"""
        states = []
        actions = []
        rewards = []
        next_states = []
        dones = []
        old_log_probs = []

        for exp in self.memory:
            states.append(self.encode_state(exp['state']))
            actions.append(self._encode_action_vector(exp['action']))
            rewards.append(exp['reward'])
            next_states.append(self.encode_state(exp['next_state']))
            dones.append(exp['done'])

            # 计算旧策略的log概率
            with torch.no_grad():
                actor_output = self.actor(self.encode_state(exp['state']).unsqueeze(0))
                action_vector = self._encode_action_vector(exp['action'])

                # 确保action_vector是正确的二维形状 [1, action_dim]
                if len(action_vector.shape) == 1:
                    action_vector = action_vector.unsqueeze(0)
                elif len(action_vector.shape) == 2 and action_vector.shape[0] == 1:
                    pass  # 已经是正确形状
                else:
                    # 如果是其他形状，取第一个样本并重塑
                    action_vector = action_vector.view(1, -1)

                log_prob, _ = self._compute_log_probs_and_entropy(
                    actor_output,
                    action_vector
                )
                old_log_probs.append(log_prob.squeeze())

        states = torch.stack(states)
        actions = torch.stack(actions)
        rewards = torch.tensor(rewards, dtype=torch.float32, device=self.device)
        next_states = torch.stack(next_states)
        dones = torch.tensor(dones, dtype=torch.bool, device=self.device)
        old_log_probs = torch.stack(old_log_probs)

        # 计算优势函数和回报
        advantages, returns = self._compute_gae(rewards, states, next_states, dones)

        return states, actions, rewards, next_states, dones, old_log_probs, advantages, returns

    def _compute_log_probs_and_entropy(self, actor_outputs, actions):
        """计算log概率和熵"""
        # 确保actions是正确的二维张量 [batch_size, action_dim]
        if len(actions.shape) == 1:
            actions = actions.unsqueeze(0)  # 添加批次维度: [action_dim] -> [1, action_dim]
        elif len(actions.shape) == 3:
            # 处理三维张量: [batch_size, 1, action_dim] -> [batch_size, action_dim]
            if actions.shape[1] == 1:
                actions = actions.squeeze(1)
            else:
                # 如果中间维度不是1，重塑为二维
                actions = actions.view(actions.shape[0], -1)
        elif len(actions.shape) > 3:
            # 处理更高维度的张量，重塑为二维
            actions = actions.view(actions.shape[0], -1)

        batch_size = actions.shape[0]
        action_dim = actions.shape[1]

        # 调试信息（已禁用以减少输出）
        # print(f"调试：动作张量形状 {actions.shape}, Actor输出键: {list(actor_outputs.keys())}")
        # for key, value in actor_outputs.items():
        #     print(f"  {key}: {value.shape}")

        # 确保动作向量维度正确
        if action_dim != self.action_dim:
            print(f"警告：动作向量维度 {action_dim} != {self.action_dim}，尝试修复...")
            if action_dim > self.action_dim:
                actions = actions[:, :self.action_dim]  # 截断
            else:
                # 填充 - 更安全的方式
                padded_actions = torch.zeros(batch_size, self.action_dim, device=actions.device, dtype=actions.dtype)
                # 确保actions的维度匹配
                if len(actions.shape) == 2:
                    padded_actions[:, :actions.shape[1]] = actions  # 修复：使用实际输入维度
                else:
                    # 如果actions是一维的，先扩展维度
                    actions_2d = actions.view(batch_size, -1)
                    padded_actions[:, :actions_2d.shape[1]] = actions_2d
                actions = padded_actions
            print(f"修复后动作向量形状: {actions.shape}")

        # 处理71维的情况
        # 新的动作维度分配：干扰类型(5) + 标准格式(24) + 连续参数(42)
        jamming_actions = actions[:, :5]      # 干扰类型（兼容性）
        end_to_end_actions = actions[:, 5:29]  # 标准格式决策 (5+4+5+5+5=24维)
        continuous_actions = actions[:, 29:]   # 连续参数

        # 威胁等级不再由Actor预测，跳过威胁等级log概率计算

        # 威胁等级不再由Actor预测，跳过威胁等级log概率计算

        # 干扰类型log概率
        jamming_probs = actor_outputs['jamming_type_probs']
        jamming_log_probs = torch.sum(jamming_actions * torch.log(jamming_probs + 1e-8), dim=1)
        jamming_entropy = -torch.sum(jamming_probs * torch.log(jamming_probs + 1e-8), dim=1)

        # 标准格式决策log概率
        # 分解标准格式动作
        threat_level_actions = end_to_end_actions[:, 0:5]      # 威胁等级 (5维)
        jamming_count_actions = end_to_end_actions[:, 5:9]     # 干扰个数 (4维)
        jamming1_actions = end_to_end_actions[:, 9:14]         # 干扰1类型 (5维)
        jamming2_actions = end_to_end_actions[:, 14:19]        # 干扰2类型 (5维)
        jamming3_actions = end_to_end_actions[:, 19:24]        # 干扰3类型 (5维)

        # 计算各部分的log概率
        threat_level_probs = actor_outputs['threat_level_probs']
        threat_level_log_probs = torch.sum(threat_level_actions * torch.log(threat_level_probs + 1e-8), dim=1)

        jamming_count_probs = actor_outputs['jamming_count_probs']
        jamming_count_log_probs = torch.sum(jamming_count_actions * torch.log(jamming_count_probs + 1e-8), dim=1)

        jamming1_probs = actor_outputs['jamming1_probs']
        jamming1_log_probs = torch.sum(jamming1_actions * torch.log(jamming1_probs + 1e-8), dim=1)

        jamming2_probs = actor_outputs['jamming2_probs']
        jamming2_log_probs = torch.sum(jamming2_actions * torch.log(jamming2_probs + 1e-8), dim=1)

        jamming3_probs = actor_outputs['jamming3_probs']
        jamming3_log_probs = torch.sum(jamming3_actions * torch.log(jamming3_probs + 1e-8), dim=1)

        # 标准格式决策总log概率
        standard_format_log_probs = (threat_level_log_probs + jamming_count_log_probs +
                                   jamming1_log_probs + jamming2_log_probs + jamming3_log_probs)

        # 标准格式决策熵
        threat_level_entropy = -torch.sum(threat_level_probs * torch.log(threat_level_probs + 1e-8), dim=1)
        jamming_count_entropy = -torch.sum(jamming_count_probs * torch.log(jamming_count_probs + 1e-8), dim=1)
        jamming1_entropy = -torch.sum(jamming1_probs * torch.log(jamming1_probs + 1e-8), dim=1)
        jamming2_entropy = -torch.sum(jamming2_probs * torch.log(jamming2_probs + 1e-8), dim=1)
        jamming3_entropy = -torch.sum(jamming3_probs * torch.log(jamming3_probs + 1e-8), dim=1)

        standard_format_entropy = (threat_level_entropy + jamming_count_entropy +
                                 jamming1_entropy + jamming2_entropy + jamming3_entropy)

        # 连续参数的log概率（简化版本）
        continuous_params = torch.cat([
            actor_outputs['comb_params'],
            actor_outputs['isrj_params'],
            actor_outputs['broadband_params'],
            actor_outputs['smart_noise_params'],
            actor_outputs['deception_params']
        ], dim=1)

        if continuous_actions.shape[1] != continuous_params.shape[1]:
            print(f"连续参数维度不匹配: {continuous_actions.shape[1]} vs {continuous_params.shape[1]}")
            continuous_log_probs = torch.zeros(batch_size, device=actions.device)
            continuous_entropy = torch.zeros(batch_size, device=actions.device)
        else:
            # 高斯分布的log概率
            std = 0.1
            continuous_log_probs = -0.5 * torch.sum(((continuous_actions - continuous_params) / std) ** 2, dim=1)
            continuous_entropy = torch.sum(torch.ones_like(continuous_params) * 0.5 * np.log(2 * np.pi * np.e * std**2), dim=1)

        # 总log概率和熵（包含标准格式决策）
        total_log_probs = jamming_log_probs + standard_format_log_probs + continuous_log_probs
        total_entropy = jamming_entropy + standard_format_entropy + continuous_entropy

        return total_log_probs, total_entropy

    def _compute_gae(self, rewards, states, next_states, dones, gae_lambda=0.95):
        """计算广义优势估计(GAE)"""
        with torch.no_grad():
            # 计算状态价值
            values = []
            next_values = []

            # 批量计算状态价值
            values = self.critic(states).squeeze()
            next_values = self.critic(next_states).squeeze()

            # 处理终止状态
            next_values = next_values * (~dones).float()

            # 计算TD误差
            deltas = rewards + self.gamma * next_values - values

            # 计算GAE
            advantages = []
            gae = 0
            for i in reversed(range(len(deltas))):
                gae = deltas[i] + self.gamma * gae_lambda * gae * (1 - dones[i].float())
                advantages.insert(0, gae)

            advantages = torch.stack(advantages)
            returns = advantages + values

            # 标准化优势
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

            return advantages, returns



    def _encode_action_vector(self, action_dict: Dict) -> torch.Tensor:
        """将动作字典编码为向量"""
        if 'actor_output' in action_dict:
            # 如果有Actor输出，直接使用
            action_vector = self._convert_actor_output_to_action_vector(action_dict['actor_output'])
            # 调试信息
            if action_vector.shape[-1] != self.action_dim:
                print(f"警告：动作向量维度不匹配！期望{self.action_dim}，实际{action_vector.shape[-1]}")
                # 截断或填充到正确维度
                if action_vector.shape[-1] > self.action_dim:
                    action_vector = action_vector[..., :self.action_dim]
                else:
                    # 创建正确维度的张量
                    if len(action_vector.shape) == 1:
                        padded = torch.zeros(self.action_dim, device=self.device)
                        padded[:action_vector.shape[0]] = action_vector
                    else:
                        batch_size = action_vector.shape[0]
                        padded = torch.zeros(batch_size, self.action_dim, device=self.device)
                        padded[:, :action_vector.shape[-1]] = action_vector
                    action_vector = padded
            return action_vector
        else:
            # 否则构造默认向量
            action_vector = torch.zeros(self.action_dim, device=self.device)

            # 威胁等级 one-hot编码 (5维)
            threat_level = action_dict.get('threat_level', 3) - 1  # 转换为0-4
            threat_level = max(0, min(4, threat_level))  # 确保在有效范围内
            action_vector[threat_level] = 1.0

            # 干扰类型 one-hot编码 (5维)
            jamming_type = action_dict.get('jamming_type', 0)
            jamming_type = max(0, min(4, jamming_type))  # 确保在有效范围内
            action_vector[5 + jamming_type] = 1.0

            # 其余参数设为默认值
            return action_vector

    def encode_state(self, state_dict: Dict) -> torch.Tensor:
        """编码雷达状态为张量"""
        # 提取并归一化雷达状态特征
        features = [
            # 基本雷达参数
            state_dict.get('frequency', 10e9) / 1e10,      # 频率归一化到0-2 (0-20GHz)
            state_dict.get('pw', 1e-6) * 1e6,              # 脉宽转换为微秒
            state_dict.get('prt', 1000) / 1000,            # PRT归一化到毫秒
            state_dict.get('power', 1e6) / 1e7,            # 功率归一化到10MW

            # 目标信息
            state_dict.get('distance', 50) / 100,          # 距离归一化到100km
            state_dict.get('speed', 0) / 500,              # 速度归一化到500m/s
            state_dict.get('direction', 0) / 180,          # 方向归一化到度

            # 工作模式
            state_dict.get('work_mode', 1) / 4,            # 工作模式归一化

            # 威胁评估信息 - 来自威胁评估模块
            state_dict.get('threat_level', 3) / 5,         # 威胁等级归一化 (1-5)
            state_dict.get('threat_value', 0.5),           # 威胁值 (0-1)

            # 额外的威胁评估特征
            state_dict.get('threat_confidence', 0.8),      # 威胁评估置信度
            state_dict.get('threat_urgency', 0.5),         # 威胁紧急程度
        ]

        state_tensor = torch.tensor(features, dtype=torch.float32)

        if self.gpu_enabled:
            state_tensor = state_tensor.to(self.device)

        return state_tensor

    def store_experience(self, state: Dict, action: Dict, reward: float,
                        next_state: Dict, done: bool):
        """存储经验到PPO缓冲区"""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done
        }
        self.memory.append(experience)

    def save_model(self, filepath: str):
        """保存PPO模型"""
        if self.gpu_enabled:
            torch.save({
                'actor_state_dict': self.actor.state_dict(),
                'critic_state_dict': self.critic.state_dict(),
                'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
                'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
                'training_stats': self.training_stats,
                'config': self.config
            }, filepath)
            print(f"PPO模型已保存到: {filepath}")

    def load_model(self, filepath: str) -> bool:
        """加载PPO模型"""
        try:
            if self.gpu_enabled:
                checkpoint = torch.load(filepath, map_location=self.device)
                self.actor.load_state_dict(checkpoint['actor_state_dict'])
                self.critic.load_state_dict(checkpoint['critic_state_dict'])
                self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
                self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])
                if 'training_stats' in checkpoint:
                    self.training_stats = checkpoint['training_stats']
                print(f"PPO模型已加载: {filepath}")
                return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False

    def _select_action_cpu(self, state_dict: Dict, training: bool) -> Dict:
        """CPU回退动作选择"""
        threat_level = state_dict.get('threat_level', 3)

        # 基于威胁等级的启发式决策
        should_jam = threat_level <= 4
        jamming_type = min(4, 5 - threat_level)
        jamming_power = (5 - threat_level) / 5.0

        return {
            'should_jam': should_jam,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power,
            'jamming_frequency': 10e9
        }

    def get_statistics(self) -> Dict:
        """获取训练统计"""
        stats = self.training_stats.copy()
        stats['noise_std'] = self.noise_std
        stats['memory_size'] = len(self.memory)
        stats['gpu_enabled'] = self.gpu_enabled
        return stats


class GPURLAccelerator:
    """GPU加速强化学习器 - DQN版本（保留兼容性）"""

    def __init__(self, config: Dict):
        """
        初始化GPU强化学习加速器

        Args:
            config: 配置字典
        """
        self.config = config
        self.gpu_enabled = config.get('enable_gpu', False) and TORCH_AVAILABLE and torch.cuda.is_available()
        self.device = torch.device(f"cuda:{config.get('gpu_device', 0)}") if self.gpu_enabled else torch.device('cpu')
        self.batch_size = config.get('batch_size', 32)
        self.use_mixed_precision = config.get('use_mixed_precision', False)

        # 网络参数
        self.state_dim = config.get('state_dim', 12)  # 更新为12维以匹配新的状态格式
        self.action_dim = config.get('action_dim', 71)
        self.learning_rate = config.get('learning_rate', 0.01)  # 提高学习率
        self.gamma = config.get('gamma', 0.99)
        self.epsilon = config.get('epsilon', 0.9)  # 提高初始探索率
        self.epsilon_decay = config.get('epsilon_decay', 0.9995)  # 减缓衰减速度
        self.epsilon_min = config.get('epsilon_min', 0.05)  # 提高最小探索率

        # 新增：直接参数输出模式
        self.direct_params = config.get('direct_params', False)

        # 初始化网络
        if self.gpu_enabled:
            self._initialize_gpu_networks()

        # 经验回放缓冲区
        self.memory_size = config.get('memory_size', 10000)
        self.memory = []
        self.memory_index = 0

        # 训练统计
        self.training_stats = {
            'total_steps': 0,
            'total_episodes': 0,
            'average_loss': 0.0,
            'average_q_value': 0.0
        }

        print(f"GPU强化学习加速器初始化完成")
        print(f"设备: {self.device}")
        print(f"混合精度: {'开启' if self.use_mixed_precision else '关闭'}")
        print(f"批处理大小: {self.batch_size}")
    
    def _initialize_gpu_networks(self):
        """初始化GPU网络"""
        # 主网络
        self.q_network = GPUQNetwork(
            self.state_dim, self.action_dim,
            direct_params=self.direct_params,
            use_attention=False
        ).to(self.device)

        # 目标网络
        self.target_network = GPUQNetwork(
            self.state_dim, self.action_dim,
            direct_params=self.direct_params,
            use_attention=False
        ).to(self.device)
        self.target_network.load_state_dict(self.q_network.state_dict())

        # 优化器
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=self.learning_rate)

        # 混合精度训练
        if self.use_mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()

        # 损失函数
        if self.direct_params:
            # 多任务损失函数
            self.criterion = self._create_multi_task_loss()
        else:
            self.criterion = nn.MSELoss()

        print(f"GPU网络初始化完成: {sum(p.numel() for p in self.q_network.parameters())} 参数")
        if self.direct_params:
            print("启用直接参数输出模式")

    def _create_multi_task_loss(self):
        """创建多任务损失函数"""
        def multi_task_loss(outputs, targets):
            """
            多任务损失函数
            outputs: 网络输出字典
            targets: 目标值字典
            """
            total_loss = 0.0

            # 威胁等级损失 (交叉熵)
            if 'threat_probs' in outputs and 'threat_target' in targets:
                threat_loss = nn.CrossEntropyLoss()(outputs['threat_probs'], targets['threat_target'])
                total_loss += threat_loss * 1.0  # 权重1.0

            # 干扰决策损失 (交叉熵)
            if 'jam_decision_probs' in outputs and 'jam_decision_target' in targets:
                jam_loss = nn.CrossEntropyLoss()(outputs['jam_decision_probs'], targets['jam_decision_target'])
                total_loss += jam_loss * 2.0  # 权重2.0，更重要

            # 干扰类型损失 (交叉熵)
            if 'jamming_type_probs' in outputs and 'jamming_type_target' in targets:
                type_loss = nn.CrossEntropyLoss()(outputs['jamming_type_probs'], targets['jamming_type_target'])
                total_loss += type_loss * 1.5  # 权重1.5

            # 参数损失 (MSE)
            param_weight = 0.5
            for param_type in ['comb_params', 'isrj_params', 'broadband_params', 'smart_noise_params', 'deception_params']:
                if param_type in outputs and f'{param_type}_target' in targets:
                    param_loss = nn.MSELoss()(outputs[param_type], targets[f'{param_type}_target'])
                    total_loss += param_loss * param_weight

            return total_loss

        return multi_task_loss
    
    def encode_state(self, state_dict: Dict) -> torch.Tensor:
        """编码状态为张量 - 支持12维输入"""
        # 提取状态特征（与ActorCriticAccelerator保持一致）
        features = [
            # 基本雷达参数
            state_dict.get('frequency', 10e9) / 1e10,      # 频率归一化到0-2 (0-20GHz)
            state_dict.get('pw', 1e-6) * 1e6,              # 脉宽转换为微秒
            state_dict.get('prt', 1000) / 1000,            # PRT归一化到毫秒
            state_dict.get('power', 1e6) / 1e7,            # 功率归一化到10MW

            # 目标信息
            state_dict.get('distance', 50) / 100,          # 距离归一化到100km
            state_dict.get('speed', 0) / 500,              # 速度归一化到500m/s
            state_dict.get('direction', 0) / 180,          # 方向归一化到度

            # 工作模式
            state_dict.get('work_mode', 1) / 4,            # 工作模式归一化

            # 威胁评估信息 - 来自威胁评估模块
            state_dict.get('threat_level', 3) / 5,         # 威胁等级归一化 (1-5)
            state_dict.get('threat_value', 0.5),           # 威胁值 (0-1)

            # 额外的威胁评估特征
            state_dict.get('threat_confidence', 0.8),      # 威胁评估置信度
            state_dict.get('threat_urgency', 0.5),         # 威胁紧急程度
        ]
        
        state_tensor = torch.tensor(features, dtype=torch.float32)
        
        if self.gpu_enabled:
            state_tensor = state_tensor.to(self.device)
        
        return state_tensor
    
    def encode_action(self, action_dict: Dict) -> int:
        """编码动作为整数"""
        # 简化的动作编码
        should_jam = action_dict.get('should_jam', False)
        jamming_type = action_dict.get('jamming_type', 0)
        power_level = int(action_dict.get('jamming_power', 0.5) * 10)  # 0-10级功率
        
        if not should_jam:
            return 0  # 不干扰
        
        # 干扰动作编码: 1-5(干扰类型) + 0-10(功率等级) * 5
        action_id = jamming_type + 1 + power_level * 5
        return min(action_id, self.action_dim - 1)
    
    def decode_action(self, action_id: int) -> Dict:
        """解码动作ID为动作字典"""
        if action_id == 0:
            return {
                'should_jam': False,
                'jamming_type': 0,
                'jamming_power': 0.0,
                'jamming_frequency': 10e9
            }
        
        # 解码干扰动作
        power_level = (action_id - 1) // 5
        jamming_type = (action_id - 1) % 5
        
        return {
            'should_jam': True,
            'jamming_type': jamming_type,
            'jamming_power': power_level / 10.0,
            'jamming_frequency': 10e9  # 默认频率
        }
    
    def select_action(self, state_dict: Dict, training: bool = True) -> Dict:
        """选择动作（ε-贪婪策略）- 支持直接参数输出"""
        if not self.gpu_enabled:
            # 回退到传统方法
            return self._select_action_cpu(state_dict, training)

        state_tensor = self.encode_state(state_dict).unsqueeze(0)

        if self.direct_params:
            # 直接参数输出模式
            return self._select_action_direct_params(state_tensor, training)
        else:
            # 传统Q值模式
            if training and np.random.random() < self.epsilon:
                # 探索：随机选择
                action_id = np.random.randint(0, self.action_dim)
            else:
                # 利用：选择最优动作
                with torch.no_grad():
                    q_values = self.q_network(state_tensor)
                    action_id = q_values.argmax().item()

            return self.decode_action(action_id)

    def _select_action_direct_params(self, state_tensor, training: bool = True):
        """直接参数输出模式的动作选择"""
        with torch.no_grad():
            if training and np.random.random() < self.epsilon:
                # 探索：随机生成参数
                return self._generate_random_action()
            else:
                # 利用：使用网络预测
                jamming_params = self.q_network.predict_jamming_params(state_tensor)
                return self._convert_params_to_action_dict(jamming_params)

    def _generate_random_action(self):
        """生成随机动作（用于探索）"""
        should_jam = np.random.random() > 0.3
        if not should_jam:
            return {
                'should_jam': False,
                'jamming_type': 0,
                'jamming_power': 0.0,
                'jamming_frequency': 10e9
            }

        return {
            'should_jam': True,
            'jamming_type': np.random.randint(0, 5),
            'jamming_power': np.random.random(),
            'jamming_frequency': 10e9
        }

    def _convert_params_to_action_dict(self, jamming_params):
        """将参数向量转换为动作字典"""
        params = jamming_params.cpu().numpy()

        threat_level = int(params[0])
        jamming_count = int(params[1])

        if jamming_count == 0:
            return {
                'should_jam': False,
                'jamming_type': 0,
                'jamming_power': 0.0,
                'jamming_frequency': 10e9,
                'direct_params': params.tolist()  # 保存完整参数
            }

        jamming_type = int(params[2])

        return {
            'should_jam': True,
            'jamming_type': jamming_type,
            'jamming_power': 0.8,  # 从参数中推断功率
            'jamming_frequency': 10e9,
            'direct_params': params.tolist()  # 保存完整参数
        }
    
    def store_experience(self, state: Dict, action: Dict, reward: float, 
                        next_state: Dict, done: bool):
        """存储经验到回放缓冲区"""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done
        }
        
        if len(self.memory) < self.memory_size:
            self.memory.append(experience)
        else:
            self.memory[self.memory_index] = experience
            self.memory_index = (self.memory_index + 1) % self.memory_size
    
    def train_batch(self) -> Dict:
        """批量训练"""
        if not self.gpu_enabled or len(self.memory) < self.batch_size:
            return {'loss': 0.0, 'avg_q': 0.0}
        
        # 采样批次
        batch_indices = np.random.choice(len(self.memory), self.batch_size, replace=False)
        batch = [self.memory[i] for i in batch_indices]
        
        # 准备批次数据
        states = torch.stack([self.encode_state(exp['state']) for exp in batch])
        actions = torch.tensor([self.encode_action(exp['action']) for exp in batch], 
                              dtype=torch.long, device=self.device)
        rewards = torch.tensor([exp['reward'] for exp in batch], 
                              dtype=torch.float32, device=self.device)
        next_states = torch.stack([self.encode_state(exp['next_state']) for exp in batch])
        dones = torch.tensor([exp['done'] for exp in batch], 
                            dtype=torch.bool, device=self.device)
        
        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # 计算目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # 计算损失
        loss = self.criterion(current_q_values.squeeze(), target_q_values)
        
        # 反向传播
        self.optimizer.zero_grad()
        
        if self.use_mixed_precision:
            with torch.cuda.amp.autocast():
                loss = self.criterion(current_q_values.squeeze(), target_q_values)
            self.scaler.scale(loss).backward()
            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            loss.backward()
            self.optimizer.step()
        
        # 更新统计
        avg_q = current_q_values.mean().item()
        
        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        return {
            'loss': loss.item(),
            'avg_q': avg_q,
            'epsilon': self.epsilon
        }
    
    def update_target_network(self):
        """更新目标网络"""
        if self.gpu_enabled:
            self.target_network.load_state_dict(self.q_network.state_dict())
    
    def save_model(self, filepath: str):
        """保存模型"""
        if self.gpu_enabled:
            torch.save({
                'q_network_state_dict': self.q_network.state_dict(),
                'target_network_state_dict': self.target_network.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'epsilon': self.epsilon,
                'training_stats': self.training_stats,
                'config': self.config
            }, filepath)
        else:
            # 保存传统Q表
            with open(filepath, 'wb') as f:
                pickle.dump({
                    'epsilon': self.epsilon,
                    'training_stats': self.training_stats,
                    'config': self.config
                }, f)
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            if self.gpu_enabled:
                checkpoint = torch.load(filepath, map_location=self.device)
                self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
                self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.epsilon = checkpoint['epsilon']
                self.training_stats = checkpoint['training_stats']
                return True
            else:
                with open(filepath, 'rb') as f:
                    data = pickle.load(f)
                    self.epsilon = data['epsilon']
                    self.training_stats = data['training_stats']
                    return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    
    def _select_action_cpu(self, state_dict: Dict, training: bool) -> Dict:
        """CPU回退动作选择"""
        # 简化的CPU实现
        threat_level = state_dict.get('threat_level', 3)
        
        if training and np.random.random() < self.epsilon:
            # 随机探索
            should_jam = np.random.random() > 0.3
            jamming_type = np.random.randint(0, 5)
            jamming_power = np.random.random()
        else:
            # 基于威胁等级的启发式决策（修正阈值：1-4级威胁执行干扰）
            should_jam = threat_level <= 4
            jamming_type = min(4, 5 - threat_level)
            jamming_power = (5 - threat_level) / 5.0
        
        return {
            'should_jam': should_jam,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power,
            'jamming_frequency': 10e9
        }
    
    def get_statistics(self) -> Dict:
        """获取训练统计"""
        stats = self.training_stats.copy()
        stats['epsilon'] = self.epsilon
        stats['memory_size'] = len(self.memory)
        stats['gpu_enabled'] = self.gpu_enabled
        return stats
