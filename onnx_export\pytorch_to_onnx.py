#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTorch模型转ONNX格式转换器
支持雷达干扰决策模型的标准化转换
"""

import os
import sys
import json
import torch
import torch.onnx
import numpy as np
from typing import Dict, List, Tuple, Optional
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_modules.system_integration.gpu_rl_accelerator import ActorNetwork


class ONNXExporter:
    """ONNX模型导出器"""
    
    def __init__(self, device: str = 'cpu'):
        """
        初始化ONNX导出器
        
        Args:
            device: 计算设备 ('cpu' 或 'cuda')
        """
        self.device = device
        self.opset_version = 11  # ONNX操作集版本
        
    def load_pytorch_model(self, model_path: str) -> Tu<PERSON>[torch.nn.Mo<PERSON><PERSON>, Dict]:
        """
        加载PyTorch模型
        
        Args:
            model_path: PyTorch模型文件路径
            
        Returns:
            (模型, 配置信息)
        """
        print(f"加载PyTorch模型: {model_path}")
        
        try:
            # 加载检查点
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 获取模型配置
            if 'config' in checkpoint:
                config = checkpoint['config']
            else:
                # 默认配置
                config = {
                    'state_dim': 12,
                    'action_dim': 57,
                    'actor_hidden_dims': [128, 64]
                }
            
            # 创建模型实例
            if 'actor_state_dict' in checkpoint:
                # PPO模型 (使用ActorNetwork)
                model = ActorNetwork(
                    state_dim=config['state_dim'],
                    hidden_dims=config.get('actor_hidden_dims', [128, 64])
                )
                model.load_state_dict(checkpoint['actor_state_dict'])
                print("加载PPO Actor网络")
            else:
                # 普通Actor网络
                model = ActorNetwork(
                    state_dim=config['state_dim'],
                    hidden_dims=config.get('actor_hidden_dims', [128, 64])
                )
                model.load_state_dict(checkpoint)
                print("加载Actor网络")
            
            model.to(self.device)
            model.eval()
            
            return model, config
            
        except Exception as e:
            print(f"加载模型失败: {e}")
            raise
    
    def create_dummy_input(self, config: Dict) -> torch.Tensor:
        """
        创建虚拟输入数据
        
        Args:
            config: 模型配置
            
        Returns:
            虚拟输入张量
        """
        state_dim = config['state_dim']
        # 创建批次大小为1的输入
        dummy_input = torch.randn(1, state_dim, device=self.device)
        return dummy_input
    
    def export_to_onnx(self, model: torch.nn.Module, dummy_input: torch.Tensor, 
                       output_path: str, config: Dict) -> bool:
        """
        导出模型到ONNX格式
        
        Args:
            model: PyTorch模型
            dummy_input: 虚拟输入
            output_path: 输出文件路径
            config: 模型配置
            
        Returns:
            是否成功
        """
        try:
            print(f"导出ONNX模型到: {output_path}")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 定义输入输出名称 (按照PyTorch模型的实际输出顺序)
            input_names = ['state']
            output_names = [
                'jamming_type_probs',      # 1. 干扰类型概率分布 (5)
                'comb_params',             # 2. 梳状谱干扰参数 (25)
                'isrj_params',             # 3. 间歇采样转发参数 (6)
                'broadband_params',        # 4. 宽带阻塞噪声参数 (1)
                'smart_noise_params',      # 5. 灵巧噪声参数 (7)
                'deception_params',        # 6. 拖引干扰参数 (3)
                'threat_level_probs',      # 7. 威胁等级概率分布 (5)
                'jamming_count_probs',     # 8. 干扰数量概率分布 (4)
                'jamming1_probs',          # 9. 第一干扰类型概率 (5)
                'jamming2_probs',          # 10. 第二干扰类型概率 (5)
                'jamming3_probs'           # 11. 第三干扰类型概率 (5)
            ]
            
            # 动态轴定义（支持不同批次大小）
            dynamic_axes = {
                'state': {0: 'batch_size'},
                'jamming_type_probs': {0: 'batch_size'},
                'comb_params': {0: 'batch_size'},
                'isrj_params': {0: 'batch_size'},
                'broadband_params': {0: 'batch_size'},
                'smart_noise_params': {0: 'batch_size'},
                'deception_params': {0: 'batch_size'},
                'threat_level_probs': {0: 'batch_size'},
                'jamming_count_probs': {0: 'batch_size'},
                'jamming1_probs': {0: 'batch_size'},
                'jamming2_probs': {0: 'batch_size'},
                'jamming3_probs': {0: 'batch_size'}
            }
            
            # 导出ONNX
            torch.onnx.export(
                model,
                dummy_input,
                output_path,
                export_params=True,
                opset_version=self.opset_version,
                do_constant_folding=True,
                input_names=input_names,
                output_names=output_names,
                dynamic_axes=dynamic_axes,
                verbose=False
            )
            
            print(f"ONNX模型导出成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"ONNX导出失败: {e}")
            return False
    
    def save_metadata(self, output_dir: str, config: Dict, onnx_path: str):
        """
        保存模型元数据
        
        Args:
            output_dir: 输出目录
            config: 模型配置
            onnx_path: ONNX文件路径
        """
        metadata = {
            "model_type": "radar_jamming_actor",
            "framework": "pytorch_to_onnx",
            "input_spec": {
                "name": "state",
                "shape": [None, config['state_dim']],
                "dtype": "float32",
                "description": "雷达状态向量"
            },
            "output_specs": [
                {
                    "name": "jamming_type_probs",
                    "shape": [None, 5],
                    "dtype": "float32",
                    "description": "干扰类型概率分布: [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]"
                },
                {
                    "name": "comb_params",
                    "shape": [None, 25],
                    "dtype": "float32",
                    "description": "梳状谱干扰参数: 25个参数"
                },
                {
                    "name": "isrj_params",
                    "shape": [None, 6],
                    "dtype": "float32",
                    "description": "间歇采样转发参数: [频率偏移, 延时, 功率比, 脉冲数, 重复频率, 调制深度]"
                },
                {
                    "name": "broadband_params",
                    "shape": [None, 1],
                    "dtype": "float32",
                    "description": "宽带阻塞噪声参数: [噪声功率]"
                },
                {
                    "name": "smart_noise_params",
                    "shape": [None, 7],
                    "dtype": "float32",
                    "description": "灵巧噪声参数: 7个智能噪声调制参数"
                },
                {
                    "name": "deception_params",
                    "shape": [None, 3],
                    "dtype": "float32",
                    "description": "拖引干扰参数: [距离拖引, 速度拖引, 角度拖引]"
                },
                {
                    "name": "threat_level_probs",
                    "shape": [None, 5],
                    "dtype": "float32",
                    "description": "威胁等级概率分布: [等级1, 等级2, 等级3, 等级4, 等级5]"
                },
                {
                    "name": "jamming_count_probs",
                    "shape": [None, 4],
                    "dtype": "float32",
                    "description": "干扰数量概率分布: [0个, 1个, 2个, 3个]"
                },
                {
                    "name": "jamming1_probs",
                    "shape": [None, 5],
                    "dtype": "float32",
                    "description": "第一干扰类型概率分布: [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]"
                },
                {
                    "name": "jamming2_probs",
                    "shape": [None, 5],
                    "dtype": "float32",
                    "description": "第二干扰类型概率分布: [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]"
                },
                {
                    "name": "jamming3_probs",
                    "shape": [None, 5],
                    "dtype": "float32",
                    "description": "第三干扰类型概率分布: [无干扰, 间歇采样, 宽带噪声, 灵巧噪声, 拖引]"
                }
            ],
            "model_config": config,
            "export_config": {
                "opset_version": self.opset_version,
                "tolerance": 1e-5
            }
        }
        
        metadata_path = os.path.join(output_dir, "metadata.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"元数据已保存: {metadata_path}")
    
    def convert_model(self, pytorch_model_path: str, output_dir: str) -> str:
        """
        转换模型到ONNX格式
        
        Args:
            pytorch_model_path: PyTorch模型路径
            output_dir: 输出目录
            
        Returns:
            ONNX模型文件路径
        """
        # 加载PyTorch模型
        model, config = self.load_pytorch_model(pytorch_model_path)
        
        # 创建虚拟输入
        dummy_input = self.create_dummy_input(config)
        
        # 确定输出文件名
        model_name = Path(pytorch_model_path).stem
        onnx_filename = f"{model_name}.onnx"
        onnx_path = os.path.join(output_dir, onnx_filename)
        
        # 导出ONNX
        success = self.export_to_onnx(model, dummy_input, onnx_path, config)
        
        if success:
            # 保存元数据
            self.save_metadata(output_dir, config, onnx_path)
            return onnx_path
        else:
            raise RuntimeError("ONNX导出失败")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PyTorch模型转ONNX格式')
    parser.add_argument('--input', '-i', type=str, default='./models/jamming_model_ppo.pth',
                        help='输入PyTorch模型文件路径')
    parser.add_argument('--output-dir', '-o', type=str, default='models/onnx',
                        help='输出目录')
    parser.add_argument('--device', type=str, default='cuda',
                        choices=['cpu', 'cuda'], help='计算设备')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return 1
    
    try:
        # 创建导出器
        exporter = ONNXExporter(device=args.device)
        
        # 转换模型
        onnx_path = exporter.convert_model(args.input, args.output_dir)
        
        print(f"\n转换完成!")
        print(f"ONNX模型: {onnx_path}")
        print(f"元数据: {os.path.join(args.output_dir, 'metadata.json')}")
        
        return 0
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
