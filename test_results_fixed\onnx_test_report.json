{"model_path": "models/onnx_fixed/jamming_model_ppo.onnx", "test_timestamp": "2025-07-19T09:27:21", "input_info": {"name": "state", "shape": ["batch_size", 12]}, "output_info": {"jamming_type_probs": ["batch_size", 5], "comb_params": ["batch_size", 25], "isrj_params": ["batch_size", 6], "broadband_params": ["batch_size", 1], "smart_noise_params": ["batch_size", 7], "deception_params": ["batch_size", 3], "threat_level_probs": ["batch_size", 5], "jamming_count_probs": ["batch_size", 4], "jamming1_probs": ["batch_size", 5], "jamming2_probs": ["batch_size", 5], "jamming3_probs": ["batch_size", 5]}, "statistics": {"jamming_type_probs": {"shape": [20, 5], "dtype": "float32", "min": 0.17060931026935577, "max": 0.24416287243366241, "mean": 0.20000000298023224, "std": 0.017228951677680016, "median": 0.19556722044944763, "q25": 0.18618643283843994, "q75": 0.21210236847400665, "is_probability": true, "sum_mean": 1.0, "sum_std": 4.214684778958144e-08, "range_type": "tanh_like (-1 to 1)"}, "comb_params": {"shape": [20, 25], "dtype": "float32", "min": -0.4600874185562134, "max": 0.5111384987831116, "mean": 0.07705003023147583, "std": 0.22501729428768158, "median": 0.1074955090880394, "q25": -0.05754426121711731, "q75": 0.25597965717315674, "range_type": "tanh_like (-1 to 1)"}, "isrj_params": {"shape": [20, 6], "dtype": "float32", "min": -0.3380948007106781, "max": 0.5133413672447205, "mean": 0.05932370945811272, "std": 0.239048570394516, "median": 0.09757924824953079, "q25": -0.19169455766677856, "q75": 0.21417061984539032, "range_type": "tanh_like (-1 to 1)"}, "broadband_params": {"shape": [20, 1], "dtype": "float32", "min": -0.6798575520515442, "max": -0.2310902625322342, "mean": -0.41123223304748535, "std": 0.125873863697052, "median": -0.3773530125617981, "q25": -0.48902013897895813, "q75": -0.335499107837677, "range_type": "tanh_like (-1 to 1)"}, "smart_noise_params": {"shape": [20, 7], "dtype": "float32", "min": -0.2354019582271576, "max": 0.35097524523735046, "mean": 0.04188704863190651, "std": 0.14014999568462372, "median": 0.055704399943351746, "q25": -0.05450519919395447, "q75": 0.11861246079206467, "range_type": "tanh_like (-1 to 1)"}, "deception_params": {"shape": [20, 3], "dtype": "float32", "min": -0.21980005502700806, "max": 0.32576125860214233, "mean": 0.06850447505712509, "std": 0.14670245349407196, "median": 0.07404576241970062, "q25": -0.04447806626558304, "q75": 0.17915108799934387, "range_type": "tanh_like (-1 to 1)"}, "threat_level_probs": {"shape": [20, 5], "dtype": "float32", "min": 0.16597811877727509, "max": 0.24971874058246613, "mean": 0.20000000298023224, "std": 0.024104787036776543, "median": 0.1962476670742035, "q25": 0.17853012681007385, "q75": 0.2186238169670105, "is_probability": true, "sum_mean": 1.0, "sum_std": 7.420718617368038e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming_count_probs": {"shape": [20, 4], "dtype": "float32", "min": 0.19986864924430847, "max": 0.3069174289703369, "mean": 0.25, "std": 0.028930721804499626, "median": 0.2455141395330429, "q25": 0.2274874746799469, "q75": 0.2720566689968109, "is_probability": true, "sum_mean": 1.0, "sum_std": 5.960464477539063e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming1_probs": {"shape": [20, 5], "dtype": "float32", "min": 0.18074560165405273, "max": 0.22033046185970306, "mean": 0.19999998807907104, "std": 0.010133764706552029, "median": 0.20084846019744873, "q25": 0.19056877493858337, "q75": 0.2078138291835785, "is_probability": true, "sum_mean": 1.0, "sum_std": 5.960464477539063e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming2_probs": {"shape": [20, 5], "dtype": "float32", "min": 0.16841308772563934, "max": 0.2349710911512375, "mean": 0.19999998807907104, "std": 0.01643630862236023, "median": 0.20109200477600098, "q25": 0.18605202436447144, "q75": 0.2117033749818802, "is_probability": true, "sum_mean": 1.0, "sum_std": 5.4952767669647073e-08, "range_type": "tanh_like (-1 to 1)"}, "jamming3_probs": {"shape": [20, 5], "dtype": "float32", "min": 0.16012778878211975, "max": 0.24047671258449554, "mean": 0.19999998807907104, "std": 0.018189024180173874, "median": 0.20514753460884094, "q25": 0.19593626260757446, "q75": 0.21198011934757233, "is_probability": true, "sum_mean": 1.0, "sum_std": 4.420398980187201e-08, "range_type": "tanh_like (-1 to 1)"}, "performance": {"avg_inference_time": 6.898641586303711e-05, "min_inference_time": 0.0, "max_inference_time": 0.0013797283172607422, "std_inference_time": 0.00030070481522405, "success_rate": 1.0}}, "validation": {"overall_passed": false, "issues": ["缺少输出: combination_scores"], "warnings": [], "details": {"jamming_type_probs": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "comb_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "isrj_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "broadband_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "smart_noise_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}, "deception_params": {"shape_correct": true, "range_correct": true, "sum_correct": true, "issues": []}}}}