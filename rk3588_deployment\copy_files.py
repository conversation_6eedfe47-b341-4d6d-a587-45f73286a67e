#!/usr/bin/env python3
"""
复制RK3588相关文件到部署文件夹
"""

import os
import shutil

def copy_rk3588_files():
    """复制所有RK3588相关文件"""
    
    # 源文件列表
    source_files = [
        "../rk3588_jamming_inference.cpp",
        "../example_usage.cpp", 
        "../convert_onnx_to_rknn.py",
        "../RK3588_DEPLOYMENT_GUIDE.md",
        "../threat_assessment_comparison.py"
    ]
    
    # 目标文件名（如果需要重命名）
    target_files = [
        "rk3588_jamming_inference.cpp",
        "example_usage.cpp",
        "convert_onnx_to_rknn.py", 
        "RK3588_DEPLOYMENT_GUIDE.md",
        "threat_assessment_comparison.py"
    ]
    
    print("复制RK3588相关文件...")
    
    for i, source_file in enumerate(source_files):
        target_file = target_files[i]
        
        if os.path.exists(source_file):
            try:
                shutil.copy2(source_file, target_file)
                print(f"✓ 复制成功: {source_file} -> {target_file}")
            except Exception as e:
                print(f"✗ 复制失败: {source_file} - {e}")
        else:
            print(f"⚠ 源文件不存在: {source_file}")
    
    print("\n文件复制完成！")

if __name__ == "__main__":
    copy_rk3588_files()
