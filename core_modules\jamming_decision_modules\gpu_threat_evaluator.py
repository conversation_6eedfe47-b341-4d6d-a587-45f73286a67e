"""
GPU加速威胁评估模块
使用CuPy进行GPU并行计算，提升威胁评估性能
"""

import numpy as np
from typing import Dict, Tuple, List, Optional
from enum import Enum

try:
    import cupy as cp
    GPU_AVAILABLE = True
except ImportError:
    cp = np
    GPU_AVAILABLE = False

from .simplified_threat_evaluator import SimplifiedThreatEvaluator, ThreatLevel


class GPUThreatEvaluator(SimplifiedThreatEvaluator):
    """GPU加速威胁评估器"""
    
    def __init__(self, enable_radar_platform: bool = False, gpu_device: int = 0):
        """
        初始化GPU威胁评估器
        
        Args:
            enable_radar_platform: 是否启用雷达平台威胁评估
            gpu_device: GPU设备ID
        """
        super().__init__(enable_radar_platform)
        
        self.gpu_enabled = GPU_AVAILABLE
        self.gpu_device = gpu_device
        
        if self.gpu_enabled:
            try:
                cp.cuda.Device(gpu_device).use()
                # 将权重和阈值转移到GPU
                self._transfer_to_gpu()
                print(f"威胁评估器GPU加速已启用 (设备: {gpu_device})")
            except Exception as e:
                print(f"GPU初始化失败，使用CPU: {e}")
                self.gpu_enabled = False
        
        # 设置计算后端
        self.xp = cp if self.gpu_enabled else np
    
    def _transfer_to_gpu(self):
        """将数据转移到GPU"""
        if self.gpu_enabled:
            self.gpu_weights = cp.asarray(self.weights)
            
            # 转移隶属度函数参数到GPU
            self.gpu_membership_params = {}
            for key, params in self.membership_params.items():
                if isinstance(params, dict):
                    gpu_params = {}
                    for param_key, param_value in params.items():
                        if isinstance(param_value, (list, np.ndarray)):
                            gpu_params[param_key] = cp.asarray(param_value)
                        else:
                            gpu_params[param_key] = param_value
                    self.gpu_membership_params[key] = gpu_params
                else:
                    self.gpu_membership_params[key] = params
    
    def calculate_membership_values_batch(self, radar_data_batch: List[Dict]) -> cp.ndarray:
        """
        批量计算隶属度值（GPU加速）
        
        Args:
            radar_data_batch: 雷达数据批次
            
        Returns:
            批量隶属度值数组 [batch_size, num_features]
        """
        if not self.gpu_enabled:
            # 回退到CPU批处理
            return self._calculate_membership_values_batch_cpu(radar_data_batch)
        
        batch_size = len(radar_data_batch)
        num_features = len(self.weights)
        
        # 在GPU上分配结果数组
        membership_batch = cp.zeros((batch_size, num_features), dtype=cp.float32)
        
        # 提取批次数据到GPU数组
        batch_arrays = self._extract_batch_arrays(radar_data_batch)
        
        # 并行计算每个特征的隶属度
        for i, feature_name in enumerate(self.feature_names):
            if feature_name in batch_arrays:
                feature_values = batch_arrays[feature_name]
                membership_values = self._calculate_feature_membership_gpu(
                    feature_values, feature_name
                )
                membership_batch[:, i] = membership_values
        
        return membership_batch
    
    def _extract_batch_arrays(self, radar_data_batch: List[Dict]) -> Dict[str, cp.ndarray]:
        """提取批次数据到GPU数组"""
        batch_arrays = {}
        
        # 特征提取映射
        feature_extractors = {
            'speed': lambda x: x.get('speed', 0),
            'distance': lambda x: x.get('distance', 50),
            'direction': lambda x: x.get('direction', 0),
            'prf': lambda x: x.get('pulse_repetition_freq', 1000),
            'frequency': lambda x: x.get('frequency', 10e9),
            'pulse_width': lambda x: x.get('pw', 1e-6),
            'work_mode': lambda x: x.get('work_mode', 1)
        }
        
        for feature_name, extractor in feature_extractors.items():
            values = [extractor(data) for data in radar_data_batch]
            batch_arrays[feature_name] = cp.asarray(values, dtype=cp.float32)
        
        return batch_arrays
    
    def _calculate_feature_membership_gpu(self, feature_values: cp.ndarray, 
                                        feature_name: str) -> cp.ndarray:
        """GPU并行计算特征隶属度"""
        if feature_name == 'speed':
            return self._speed_membership_gpu(feature_values)
        elif feature_name == 'distance':
            return self._distance_membership_gpu(feature_values)
        elif feature_name == 'direction':
            return self._direction_membership_gpu(feature_values)
        elif feature_name == 'prf':
            return self._prf_membership_gpu(feature_values)
        elif feature_name == 'frequency':
            return self._frequency_membership_gpu(feature_values)
        elif feature_name == 'pulse_width':
            return self._pulse_width_membership_gpu(feature_values)
        elif feature_name == 'work_mode':
            return self._work_mode_membership_gpu(feature_values)
        else:
            return cp.ones_like(feature_values) * 0.5
    
    def _speed_membership_gpu(self, speeds: cp.ndarray) -> cp.ndarray:
        """GPU加速速度威胁度计算"""
        # 使用GPU并行计算梯形隶属度函数
        # 高威胁: 速度 > 300 m/s
        high_threat = cp.where(speeds > 300, 1.0, 
                              cp.where(speeds > 200, (speeds - 200) / 100, 0.0))
        
        # 中威胁: 100-300 m/s
        mid_threat = cp.where((speeds >= 100) & (speeds <= 300), 
                             cp.minimum((speeds - 100) / 100, (300 - speeds) / 100), 0.0)
        
        # 低威胁: < 100 m/s
        low_threat = cp.where(speeds < 100, 1.0 - speeds / 100, 0.0)
        
        # 综合威胁度
        return high_threat * 0.9 + mid_threat * 0.5 + low_threat * 0.1
    
    def _distance_membership_gpu(self, distances: cp.ndarray) -> cp.ndarray:
        """GPU加速距离威胁度计算"""
        # 距离越近威胁越大
        normalized_dist = cp.clip(distances / 200.0, 0.0, 1.0)  # 归一化到200km
        return 1.0 - normalized_dist
    
    def _direction_membership_gpu(self, directions: cp.ndarray) -> cp.ndarray:
        """GPU加速航向威胁度计算"""
        # 正面来袭威胁最大
        abs_directions = cp.abs(directions)
        return cp.where(abs_directions <= 30, 1.0,
                       cp.where(abs_directions <= 90, (90 - abs_directions) / 60, 0.1))
    
    def _prf_membership_gpu(self, prfs: cp.ndarray) -> cp.ndarray:
        """GPU加速PRF威胁度计算"""
        # 高PRF通常表示跟踪或制导雷达
        return cp.where(prfs > 5000, 0.9,
                       cp.where(prfs > 1000, 0.5 + (prfs - 1000) / 10000, 0.2))
    
    def _frequency_membership_gpu(self, frequencies: cp.ndarray) -> cp.ndarray:
        """GPU加速频率威胁度计算"""
        # X波段(8-12GHz)威胁较高
        freq_ghz = frequencies / 1e9
        return cp.where((freq_ghz >= 8) & (freq_ghz <= 12), 0.8,
                       cp.where((freq_ghz >= 4) & (freq_ghz <= 8), 0.6, 0.4))
    
    def _pulse_width_membership_gpu(self, pulse_widths: cp.ndarray) -> cp.ndarray:
        """GPU加速脉宽威胁度计算"""
        # 短脉冲通常表示精确制导
        pw_us = pulse_widths * 1e6  # 转换为微秒
        return cp.where(pw_us < 1, 0.9,
                       cp.where(pw_us < 5, 0.7 - (pw_us - 1) / 10, 0.3))
    
    def _work_mode_membership_gpu(self, work_modes: cp.ndarray) -> cp.ndarray:
        """GPU加速工作模式威胁度计算"""
        # 模式映射: 1-搜索, 2-跟踪, 3-制导
        mode_threats = cp.array([0.3, 0.7, 0.95])  # 对应威胁度
        return mode_threats[cp.clip(work_modes.astype(int) - 1, 0, 2)]
    
    def calculate_threat_values_batch(self, radar_data_batch: List[Dict]) -> cp.ndarray:
        """
        批量计算威胁值（GPU加速）
        
        Args:
            radar_data_batch: 雷达数据批次
            
        Returns:
            威胁值数组
        """
        membership_batch = self.calculate_membership_values_batch(radar_data_batch)
        
        if self.gpu_enabled:
            # GPU矩阵乘法
            threat_values = cp.dot(membership_batch, self.gpu_weights)
        else:
            threat_values = np.dot(membership_batch, self.weights)
        
        return threat_values
    
    def evaluate_threat_batch(self, radar_data_batch: List[Dict]) -> List[Tuple[float, ThreatLevel, Dict]]:
        """
        批量威胁评估（GPU加速）
        
        Args:
            radar_data_batch: 雷达数据批次
            
        Returns:
            威胁评估结果列表
        """
        threat_values = self.calculate_threat_values_batch(radar_data_batch)
        
        # 转回CPU进行后处理
        if self.gpu_enabled:
            threat_values = cp.asnumpy(threat_values)
        
        results = []
        for i, threat_value in enumerate(threat_values):
            threat_level = self.determine_threat_level(threat_value)
            
            # 构建详细信息
            details = {
                'membership_values': self._get_membership_details(radar_data_batch[i]),
                'threat_components': self._analyze_threat_components(radar_data_batch[i]),
                'confidence': min(1.0, threat_value + 0.1)
            }
            
            results.append((threat_value, threat_level, details))
        
        return results
    
    def _calculate_membership_values_batch_cpu(self, radar_data_batch: List[Dict]) -> np.ndarray:
        """CPU批处理回退方法"""
        results = []
        for radar_data in radar_data_batch:
            membership_values = self.calculate_membership_values(radar_data)
            results.append(membership_values)
        return np.array(results)
    
    def _get_membership_details(self, radar_data: Dict) -> Dict:
        """获取隶属度详细信息"""
        return {
            '速度威胁度': self._speed_membership_single(radar_data.get('speed', 0)),
            '距离威胁度': self._distance_membership_single(radar_data.get('distance', 50)),
            '航向威胁度': self._direction_membership_single(radar_data.get('direction', 0)),
            '重频威胁度': self._prf_membership_single(radar_data.get('pulse_repetition_freq', 1000)),
            '载频威胁度': self._frequency_membership_single(radar_data.get('frequency', 10e9)),
            '脉宽威胁度': self._pulse_width_membership_single(radar_data.get('pw', 1e-6)),
            '工作模式威胁度': self._work_mode_membership_single(radar_data.get('work_mode', 1))
        }
    
    def _speed_membership_single(self, speed: float) -> float:
        """单个速度威胁度计算"""
        if speed > 300:
            return 0.9
        elif speed > 200:
            return 0.5 + (speed - 200) / 200
        elif speed > 100:
            return 0.3 + (speed - 100) / 500
        else:
            return 0.1 + speed / 1000
    
    def _distance_membership_single(self, distance: float) -> float:
        """单个距离威胁度计算"""
        return max(0.1, 1.0 - distance / 200.0)
    
    def _direction_membership_single(self, direction: float) -> float:
        """单个航向威胁度计算"""
        abs_dir = abs(direction)
        if abs_dir <= 30:
            return 0.9
        elif abs_dir <= 90:
            return 0.5 + (90 - abs_dir) / 120
        else:
            return 0.1
    
    def _prf_membership_single(self, prf: float) -> float:
        """单个PRF威胁度计算"""
        if prf > 5000:
            return 0.9
        elif prf > 1000:
            return 0.3 + (prf - 1000) / 10000
        else:
            return 0.2
    
    def _frequency_membership_single(self, frequency: float) -> float:
        """单个频率威胁度计算"""
        freq_ghz = frequency / 1e9
        if 8 <= freq_ghz <= 12:
            return 0.8
        elif 4 <= freq_ghz <= 8:
            return 0.6
        else:
            return 0.4
    
    def _pulse_width_membership_single(self, pulse_width: float) -> float:
        """单个脉宽威胁度计算"""
        pw_us = pulse_width * 1e6
        if pw_us < 1:
            return 0.9
        elif pw_us < 5:
            return 0.7 - (pw_us - 1) / 10
        else:
            return 0.3
    
    def _work_mode_membership_single(self, work_mode: int) -> float:
        """单个工作模式威胁度计算"""
        mode_threats = {1: 0.3, 2: 0.7, 3: 0.95}
        return mode_threats.get(work_mode, 0.5)
    
    def _analyze_threat_components(self, radar_data: Dict) -> Dict:
        """分析威胁组成"""
        return {
            'primary_threat_factor': '距离' if radar_data.get('distance', 50) < 30 else '工作模式',
            'threat_trend': 'increasing' if radar_data.get('speed', 0) > 200 else 'stable',
            'critical_parameters': ['distance', 'work_mode', 'prf']
        }
