# 训练模型依赖包
# 用于PPO强化学习训练和GPU加速

# 核心深度学习框架
torch>=1.8.0
torchvision>=0.9.0
torchaudio>=0.8.0

# 数值计算和科学计算
numpy>=1.19.0
scipy>=1.6.0

# GPU加速计算
cupy-cuda11x>=9.0.0  # 根据CUDA版本选择对应的cupy版本
# cupy-cuda10x>=9.0.0  # CUDA 10.x版本
# cupy-cuda12x>=9.0.0  # CUDA 12.x版本

# 数据可视化
matplotlib>=3.3.0
seaborn>=0.11.0

# 进度条和用户界面
tqdm>=4.60.0

# 数据处理
pandas>=1.2.0

# 配置文件处理
pyyaml>=5.4.0

# 日志记录
loguru>=0.5.0

# 内存优化
psutil>=5.8.0

# 数学优化
cvxpy>=1.1.0  # 用于权重优化

# 信号处理
librosa>=0.8.0  # 音频信号处理

# 机器学习工具
scikit-learn>=0.24.0

# 随机数生成
randomgen>=1.19.0

# 并行计算
joblib>=1.0.0

# 内存映射
h5py>=3.1.0

# 时间序列处理
statsmodels>=0.12.0

# 优化算法
optuna>=2.7.0  # 超参数优化

# 模型保存和加载
cloudpickle>=1.6.0

# 数据验证
cerberus>=1.3.0

# 环境变量管理
python-dotenv>=0.17.0

# 系统监控
nvidia-ml-py3>=7.352.0  # GPU监控

# 数据结构
dataclasses-json>=0.5.0

# 类型检查
typing-extensions>=3.7.0

# 注意: 以下是Python内置模块，无需安装
# warnings, os, sys, time, json, argparse, pathlib, pickle, gc
# threading, multiprocessing, subprocess, shutil, glob, re
# collections, itertools, functools, operator, copy, math, random
