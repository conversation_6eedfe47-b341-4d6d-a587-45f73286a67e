function [membership_speed,membership_distance,membership_azi] = move_membership(speed,distance,direction,l1,l2)
% 计算运动信息的隶属度
% 输入:
%   speed - 速度值 (单位: Ma)
%   distance - 距离值 (单位: km)
%   direction - 运动方向 (单位: 弧度)
% 输出:
%   membership - 隶属度值

% 根据速度确定隶属度
if speed <= 3
    membership_speed = speed / 3;
else
    membership_speed = 1;  
end

%距离
if distance <= l1
    membership_distance = 1.0;
elseif distance < l2
    membership_distance = (l2 - distance) / (l2 - l1);
else
    membership_distance = 0;
end

%方向
if direction >= 0 && direction <= pi
    membership_azi = 1 / (1 + (1/pi * direction)^2);
elseif direction >= -pi && direction < 0
    membership_azi = 1 / (1 + (1/pi * abs(direction))^2);
else
    membership_azi = 0;
end

end