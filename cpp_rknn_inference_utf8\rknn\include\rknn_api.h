﻿/**
 * RKNN API 头文件
 * 基于瑞芯微RKNN Toolkit标准API
 */

#ifndef RKNN_API_H
#define RKNN_API_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stddef.h>

// RKNN版本信息
#define RKNN_VERSION_MAJOR 1
#define RKNN_VERSION_MINOR 4
#define RKNN_VERSION_PATCH 0

// 返回码定义
typedef enum {
    RKNN_SUCC = 0,                      // 成功
    RKNN_ERR_FAIL = -1,                 // 失败
    RKNN_ERR_TIMEOUT = -2,              // 超时
    RKNN_ERR_DEVICE_UNAVAILABLE = -3,   // 设备不可用
    RKNN_ERR_MALLOC_FAIL = -4,          // 内存分配失败
    RKNN_ERR_PARAM_INVALID = -5,        // 参数无效
    RKNN_ERR_MODEL_INVALID = -6,        // 模型无效
    RKNN_ERR_CTX_INVALID = -7,          // 上下文无效
    RKNN_ERR_INPUT_INVALID = -8,        // 输入无效
    RKNN_ERR_OUTPUT_INVALID = -9,       // 输出无效
} rknn_result_t;

// 张量数据类型
typedef enum {
    RKNN_TENSOR_FLOAT32 = 0,            // float32
    RKNN_TENSOR_FLOAT16,                 // float16
    RKNN_TENSOR_INT8,                    // int8
    RKNN_TENSOR_UINT8,                   // uint8
    RKNN_TENSOR_INT16,                   // int16
    RKNN_TENSOR_UINT16,                  // uint16
    RKNN_TENSOR_INT32,                   // int32
    RKNN_TENSOR_UINT32,                  // uint32
    RKNN_TENSOR_INT64,                   // int64
    RKNN_TENSOR_BOOL,                    // bool
} rknn_tensor_type;

// 张量数据格式
typedef enum {
    RKNN_TENSOR_NCHW = 0,               // NCHW格式
    RKNN_TENSOR_NHWC,                   // NHWC格式
    RKNN_TENSOR_NC1HWC2,                // NC1HWC2格式
    RKNN_TENSOR_UNDEFINED,              // 未定义格式
} rknn_tensor_format;

// 查询命令
typedef enum {
    RKNN_QUERY_IN_OUT_NUM = 0,          // 查询输入输出数量
    RKNN_QUERY_INPUT_ATTR,              // 查询输入属性
    RKNN_QUERY_OUTPUT_ATTR,             // 查询输出属性
    RKNN_QUERY_PERF_DETAIL,             // 查询性能详情
    RKNN_QUERY_PERF_RUN,                // 查询运行性能
    RKNN_QUERY_SDK_VERSION,             // 查询SDK版本
} rknn_query_cmd;

// RKNN上下文句柄
typedef uint64_t rknn_context;

// 输入输出数量结构
typedef struct {
    uint32_t n_input;                   // 输入数量
    uint32_t n_output;                  // 输出数量
} rknn_input_output_num;

// 张量属性结构
typedef struct {
    uint32_t index;                     // 张量索引
    uint32_t n_dims;                    // 维度数量
    uint32_t dims[16];                  // 各维度大小
    char name[256];                     // 张量名称
    uint32_t n_elems;                   // 元素总数
    uint32_t size;                      // 字节大小
    rknn_tensor_format fmt;             // 数据格式
    rknn_tensor_type type;              // 数据类型
    uint8_t qnt_type;                   // 量化类型
    int8_t fl;                          // 小数位数
    int32_t zp;                         // 零点
    float scale;                        // 缩放因子
} rknn_tensor_attr;

// 输入数据结构
typedef struct {
    uint32_t index;                     // 输入索引
    void* buf;                          // 数据缓冲区
    uint32_t size;                      // 数据大小
    uint8_t pass_through;               // 是否直通
    rknn_tensor_type type;              // 数据类型
    rknn_tensor_format fmt;             // 数据格式
} rknn_input;

// 输出数据结构
typedef struct {
    uint8_t want_float;                 // 是否需要float输出
    uint8_t is_prealloc;                // 是否预分配
    uint32_t index;                     // 输出索引
    void* buf;                          // 数据缓冲区
    uint32_t size;                      // 数据大小
} rknn_output;

// 性能信息结构
typedef struct {
    char* perf_data;                    // 性能数据
    uint64_t data_len;                  // 数据长度
} rknn_perf_detail;

// 运行性能结构
typedef struct {
    int64_t run_duration;               // 运行时长(微秒)
} rknn_perf_run;

// SDK版本信息
typedef struct {
    char api_version[256];              // API版本
    char drv_version[256];              // 驱动版本
} rknn_sdk_version;

// 初始化扩展参数
typedef struct {
    rknn_context ctx;                   // 上下文
    uint8_t real_model;                 // 是否真实模型
    uint8_t reserved[7];                // 保留字段
} rknn_init_extend;

// 运行扩展参数
typedef struct {
    uint64_t frame_id;                  // 帧ID
    int32_t non_block;                  // 非阻塞模式
    int32_t timeout_ms;                 // 超时时间(毫秒)
    rknn_context async_ctx;             // 异步上下文
} rknn_run_extend;

/**
 * API函数声明
 */

// 初始化RKNN上下文
int rknn_init(rknn_context* ctx, void* model, uint32_t size, uint32_t flag, rknn_init_extend* extend);

// 销毁RKNN上下文
int rknn_destroy(rknn_context ctx);

// 查询RKNN信息
int rknn_query(rknn_context ctx, rknn_query_cmd cmd, void* info, uint32_t size);

// 设置输入数据
int rknn_inputs_set(rknn_context ctx, uint32_t n_inputs, rknn_input inputs[]);

// 执行推理
int rknn_run(rknn_context ctx, rknn_run_extend* extend);

// 获取输出数据
int rknn_outputs_get(rknn_context ctx, uint32_t n_outputs, rknn_output outputs[], rknn_run_extend* extend);

// 释放输出数据
int rknn_outputs_release(rknn_context ctx, uint32_t n_outputs, rknn_output outputs[]);

#ifdef __cplusplus
}
#endif

#endif // RKNN_API_H
