#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包管理脚本
统一管理训练、ONNX转换、RKNN部署三个阶段的依赖包
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_script(script_name, args_list):
    """运行子脚本"""
    cmd = [sys.executable, script_name] + args_list
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"执行失败: {script_name}")
        print(f"错误: {e.stderr}")
        return False

def download_all_dependencies(base_dir, index_url):
    """下载所有阶段的依赖包"""
    print("=" * 60)
    print("开始下载所有阶段的依赖包")
    print("=" * 60)
    
    stages = [
        {
            "name": "训练阶段",
            "script": "download_training_packages.py",
            "requirements": "training_requirements.txt",
            "download_dir": os.path.join(base_dir, "training_packages")
        },
        {
            "name": "ONNX转换阶段", 
            "script": "download_onnx_packages.py",
            "requirements": "onnx_requirements.txt",
            "download_dir": os.path.join(base_dir, "onnx_packages")
        },
        {
            "name": "RKNN部署阶段",
            "script": "download_rknn_packages.py", 
            "requirements": "rknn_requirements.txt",
            "download_dir": os.path.join(base_dir, "rknn_packages")
        }
    ]
    
    success_count = 0
    
    for stage in stages:
        print(f"\n{'='*20} {stage['name']} {'='*20}")
        
        args = [
            "--requirements", stage["requirements"],
            "--download-dir", stage["download_dir"],
            "--index-url", index_url,
            "--create-script"
        ]
        
        if stage["name"] == "ONNX转换阶段":
            args.append("--include-tools")
        elif stage["name"] == "RKNN部署阶段":
            args.append("--include-opencv")
        
        success = run_script(stage["script"], args)
        if success:
            success_count += 1
            print(f"✅ {stage['name']} 依赖包下载完成")
        else:
            print(f"❌ {stage['name']} 依赖包下载失败")
    
    print(f"\n{'='*60}")
    print(f"依赖包下载完成: {success_count}/{len(stages)} 个阶段成功")
    print(f"{'='*60}")
    
    return success_count == len(stages)

def create_directory_structure(base_dir):
    """创建目录结构"""
    directories = [
        os.path.join(base_dir, "training_packages"),
        os.path.join(base_dir, "onnx_packages"), 
        os.path.join(base_dir, "rknn_packages"),
        os.path.join(base_dir, "docs"),
        os.path.join(base_dir, "scripts")
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")

def create_master_install_script(base_dir):
    """创建主安装脚本"""
    script_content = f"""#!/bin/bash
# 雷达电子战智能干扰决策系统 - 依赖包安装脚本

echo "雷达电子战智能干扰决策系统 - 依赖包安装"
echo "============================================"

# 设置基础目录
BASE_DIR="{base_dir}"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 选择安装阶段
echo ""
echo "请选择要安装的依赖包:"
echo "1) 训练阶段依赖包"
echo "2) ONNX转换阶段依赖包"
echo "3) RKNN部署阶段依赖包"
echo "4) 安装所有依赖包"
echo "5) 退出"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "安装训练阶段依赖包..."
        cd $BASE_DIR/training_packages
        bash install_training_packages.sh
        ;;
    2)
        echo "安装ONNX转换阶段依赖包..."
        cd $BASE_DIR/onnx_packages
        bash install_onnx_packages.sh
        ;;
    3)
        echo "安装RKNN部署阶段依赖包..."
        cd $BASE_DIR/rknn_packages
        bash install_rknn_packages.sh
        ;;
    4)
        echo "安装所有依赖包..."
        
        echo "1/3 安装训练阶段依赖包..."
        cd $BASE_DIR/training_packages
        bash install_training_packages.sh
        
        echo "2/3 安装ONNX转换阶段依赖包..."
        cd $BASE_DIR/onnx_packages
        bash install_onnx_packages.sh
        
        echo "3/3 安装RKNN部署阶段依赖包..."
        cd $BASE_DIR/rknn_packages
        bash install_rknn_packages.sh
        
        echo "所有依赖包安装完成!"
        ;;
    5)
        echo "退出安装"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo "依赖包安装完成!"
"""
    
    script_path = os.path.join(base_dir, "install_all_dependencies.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print(f"创建主安装脚本: {script_path}")

def create_readme(base_dir):
    """创建README文档"""
    readme_content = f"""# 雷达电子战智能干扰决策系统 - 依赖包管理

## 目录结构

```
{base_dir}/
├── training_packages/          # 训练阶段依赖包
│   ├── *.whl                  # Python包文件
│   ├── *.tar.gz              # 源码包文件
│   └── install_training_packages.sh  # 安装脚本
├── onnx_packages/             # ONNX转换阶段依赖包
│   ├── *.whl                  # Python包文件
│   └── install_onnx_packages.sh      # 安装脚本
├── rknn_packages/             # RKNN部署阶段依赖包
│   ├── *.whl                  # Python包文件
│   ├── RKNN_INSTALL_README.md # RKNN安装说明
│   └── install_rknn_packages.sh      # 安装脚本
└── install_all_dependencies.sh       # 主安装脚本
```

## 使用方法

### 1. 下载依赖包

```bash
# 下载所有阶段的依赖包
python manage_dependencies.py --download-all

# 下载特定阶段的依赖包
python manage_dependencies.py --stage training
python manage_dependencies.py --stage onnx
python manage_dependencies.py --stage rknn
```

### 2. 安装依赖包

```bash
# 交互式安装
bash install_all_dependencies.sh

# 直接安装特定阶段
bash training_packages/install_training_packages.sh
bash onnx_packages/install_onnx_packages.sh
bash rknn_packages/install_rknn_packages.sh
```

### 3. 离线安装

```bash
# 使用pip离线安装
pip install --find-links training_packages --no-index torch
pip install --find-links onnx_packages --no-index onnx
pip install --find-links rknn_packages --no-index opencv-python
```

## 各阶段依赖说明

### 训练阶段 (training_packages/)
- PyTorch深度学习框架
- CuPy GPU加速计算
- 科学计算库(NumPy, SciPy)
- 数据可视化库(Matplotlib)
- 进度条和工具库

### ONNX转换阶段 (onnx_packages/)
- ONNX核心库和运行时
- ONNX优化工具
- 模型转换工具
- 模型可视化工具

### RKNN部署阶段 (rknn_packages/)
- RKNN工具包(需手动下载)
- OpenCV图像处理
- 系统监控工具
- C++编译工具

## 注意事项

1. **RKNN工具包**: 需要从瑞芯微官网手动下载
2. **GPU支持**: CuPy需要对应的CUDA版本
3. **Python版本**: 推荐使用Python 3.8-3.10
4. **系统要求**: Linux x86_64 (RKNN工具包要求)

## 故障排除

### 常见问题

1. **包下载失败**: 检查网络连接和镜像源
2. **CUDA版本不匹配**: 安装对应版本的CuPy
3. **RKNN工具包缺失**: 从官网下载对应版本
4. **权限问题**: 使用sudo或虚拟环境

### 联系支持

如有问题，请查看项目文档或提交Issue。
"""
    
    readme_path = os.path.join(base_dir, "README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"创建README文档: {readme_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='依赖包管理脚本')
    parser.add_argument('--download-all', action='store_true',
                       help='下载所有阶段的依赖包')
    parser.add_argument('--stage', type=str, choices=['training', 'onnx', 'rknn'],
                       help='下载特定阶段的依赖包')
    parser.add_argument('--base-dir', type=str, default='packages',
                       help='依赖包基础目录')
    parser.add_argument('--index-url', type=str,
                       default='https://pypi.tuna.tsinghua.edu.cn/simple/',
                       help='PyPI镜像源URL')
    parser.add_argument('--create-structure', action='store_true',
                       help='创建目录结构和文档')
    
    args = parser.parse_args()
    
    # 创建基础目录
    Path(args.base_dir).mkdir(parents=True, exist_ok=True)
    
    if args.create_structure:
        # 创建目录结构
        create_directory_structure(args.base_dir)
        create_master_install_script(args.base_dir)
        create_readme(args.base_dir)
        print("目录结构和文档创建完成!")
        return 0
    
    if args.download_all:
        # 下载所有依赖包
        success = download_all_dependencies(args.base_dir, args.index_url)
        if success:
            create_master_install_script(args.base_dir)
            create_readme(args.base_dir)
        return 0 if success else 1
    
    if args.stage:
        # 下载特定阶段的依赖包
        stage_configs = {
            'training': {
                'script': 'download_training_packages.py',
                'requirements': 'training_requirements.txt',
                'download_dir': os.path.join(args.base_dir, 'training_packages')
            },
            'onnx': {
                'script': 'download_onnx_packages.py', 
                'requirements': 'onnx_requirements.txt',
                'download_dir': os.path.join(args.base_dir, 'onnx_packages')
            },
            'rknn': {
                'script': 'download_rknn_packages.py',
                'requirements': 'rknn_requirements.txt', 
                'download_dir': os.path.join(args.base_dir, 'rknn_packages')
            }
        }
        
        config = stage_configs[args.stage]
        script_args = [
            '--requirements', config['requirements'],
            '--download-dir', config['download_dir'],
            '--index-url', args.index_url,
            '--create-script'
        ]
        
        if args.stage == 'onnx':
            script_args.append('--include-tools')
        elif args.stage == 'rknn':
            script_args.append('--include-opencv')
        
        success = run_script(config['script'], script_args)
        return 0 if success else 1
    
    # 如果没有指定操作，显示帮助
    parser.print_help()
    return 0

if __name__ == "__main__":
    sys.exit(main())
