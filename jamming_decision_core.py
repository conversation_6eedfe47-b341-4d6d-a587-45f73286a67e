"""
雷达电子战干扰决策核心模块
提供简洁的接口用于威胁评估模块集成
"""

import sys
import os
import numpy as np
from typing import Dict, List, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core_modules.system_integration.gpu_rl_accelerator import ActorCriticAccelerator


class JammingDecisionData:
    """干扰决策数据类"""

    def __init__(self, should_jam: bool, jamming_type: int, jamming_power: float,
                 jamming_frequency: float, jamming_params: Dict = None,
                 confidence: float = 0.8, decision_reason: str = "",
                 priority: int = 3, duration: float = 5.0,
                 standard_output: List = None):
        """
        初始化干扰决策数据

        Args:
            should_jam: 是否需要干扰
            jamming_type: 干扰类型 (0-4)
            jamming_power: 干扰功率 (0-1)
            jamming_frequency: 干扰频率 (Hz)
            jamming_params: 干扰参数字典
            confidence: 决策置信度 (0-1)
            decision_reason: 决策原因
            priority: 优先级 (1-5)
            duration: 持续时间 (秒)
            standard_output: 标准格式输出
        """
        self.should_jam = should_jam
        self.jamming_type = jamming_type
        self.jamming_power = jamming_power
        self.jamming_frequency = jamming_frequency
        self.jamming_params = jamming_params or {}
        self.confidence = confidence
        self.decision_reason = decision_reason
        self.priority = priority
        self.duration = duration
        self.standard_output = standard_output

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'should_jam': self.should_jam,
            'jamming_type': self.jamming_type,
            'jamming_power': self.jamming_power,
            'jamming_frequency': self.jamming_frequency,
            'jamming_params': self.jamming_params,
            'confidence': self.confidence,
            'decision_reason': self.decision_reason,
            'priority': self.priority,
            'duration': self.duration,
            'standard_output': self.standard_output
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"JammingDecision(jam={self.should_jam}, type={self.jamming_type}, power={self.jamming_power:.2f})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"JammingDecisionData(should_jam={self.should_jam}, "
                f"jamming_type={self.jamming_type}, jamming_power={self.jamming_power}, "
                f"confidence={self.confidence}, reason='{self.decision_reason}')")


class JammingDecisionData:
    """干扰决策数据类"""

    def __init__(self, should_jam: bool, jamming_type: int, jamming_power: float,
                 jamming_frequency: float, jamming_params: Dict = None,
                 confidence: float = 0.8, decision_reason: str = "",
                 priority: int = 3, duration: float = 5.0,
                 standard_output: List = None):
        """
        初始化干扰决策数据

        Args:
            should_jam: 是否需要干扰
            jamming_type: 干扰类型 (0-4)
            jamming_power: 干扰功率 (0-1)
            jamming_frequency: 干扰频率 (Hz)
            jamming_params: 干扰参数字典
            confidence: 决策置信度 (0-1)
            decision_reason: 决策原因
            priority: 优先级 (1-5)
            duration: 持续时间 (秒)
            standard_output: 标准格式输出
        """
        self.should_jam = should_jam
        self.jamming_type = jamming_type
        self.jamming_power = jamming_power
        self.jamming_frequency = jamming_frequency
        self.jamming_params = jamming_params or {}
        self.confidence = confidence
        self.decision_reason = decision_reason
        self.priority = priority
        self.duration = duration
        self.standard_output = standard_output

    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'should_jam': self.should_jam,
            'jamming_type': self.jamming_type,
            'jamming_power': self.jamming_power,
            'jamming_frequency': self.jamming_frequency,
            'jamming_params': self.jamming_params,
            'confidence': self.confidence,
            'decision_reason': self.decision_reason,
            'priority': self.priority,
            'duration': self.duration,
            'standard_output': self.standard_output
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"JammingDecision(jam={self.should_jam}, type={self.jamming_type}, power={self.jamming_power:.2f})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"JammingDecisionData(should_jam={self.should_jam}, "
                f"jamming_type={self.jamming_type}, jamming_power={self.jamming_power}, "
                f"confidence={self.confidence}, reason='{self.decision_reason}')")


class JammingDecisionCore:
    """雷达干扰决策核心类"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        初始化干扰决策核心
        
        Args:
            model_path: 预训练模型路径
        """
        self.config = {
            'enable_gpu': True,
            'gpu_device': 0,
            'use_mixed_precision': True,
            'batch_size': 64,
            'state_dim': 12,   # 包含威胁评估输入
            'action_dim': 71,  # 兼容(5) + 标准格式(24) + 参数(42)
            'actor_lr': 0.0001,
            'critic_lr': 0.0002,
            'gamma': 0.99,
            'tau': 0.005,
            'noise_std': 0.05,  # 推理时使用小噪声
            'noise_decay': 0.9995,
            'noise_min': 0.01,
            'memory_size': 50000,
        }
        
        self.ac_accelerator = ActorCriticAccelerator(self.config)
        
        # 加载预训练模型
        if model_path and os.path.exists(model_path):
            self.ac_accelerator.load_model(model_path)
    
    def make_decision(self, radar_state: Dict, threat_assessment: Optional[Dict] = None) -> List:
        """
        做出干扰决策
        
        Args:
            radar_state: 雷达状态字典
                - frequency: 载频 (MHz)
                - pw: 脉宽 (μs)
                - prt: 脉冲重复周期 (μs)
                - power: 功率 (W)
                - distance: 距离 (km)
                - speed: 速度 (m/s)
                - direction: 方向 (度)
                - work_mode: 工作模式 (0-4)
            
            threat_assessment: 威胁评估结果 (可选)
                - threat_level: 威胁等级 (1-5)
                - threat_value: 威胁值 (0-1)
                - confidence: 评估置信度 (0-1)
                - urgency: 威胁紧急程度 (0-1)
        
        Returns:
            [威胁等级, 干扰类型, 干扰参数...]
        """
        # 标准化输入
        normalized_state = self._normalize_input(radar_state, threat_assessment)
        
        # 获取决策
        action = self.ac_accelerator.select_action(normalized_state, training=False)
        
        # 生成标准输出
        return self._generate_output(action)
    
    def _normalize_input(self, radar_state: Dict, threat_assessment: Optional[Dict] = None) -> Dict:
        """标准化输入数据"""
        normalized = {}
        
        # 基本雷达参数
        normalized['frequency'] = radar_state.get('frequency', 10000)
        normalized['pw'] = radar_state.get('pw', 1.0)
        normalized['prt'] = radar_state.get('prt', 100)
        normalized['power'] = radar_state.get('power', 1e6)
        normalized['distance'] = radar_state.get('distance', 50)
        normalized['speed'] = radar_state.get('speed', 300)
        normalized['direction'] = radar_state.get('direction', 0)
        normalized['work_mode'] = radar_state.get('work_mode', 1)
        
        # 威胁评估信息
        if threat_assessment is not None:
            normalized['threat_level'] = threat_assessment.get('threat_level', 3)
            normalized['threat_value'] = threat_assessment.get('threat_value', 0.5)
            normalized['threat_confidence'] = threat_assessment.get('confidence', 0.8)
            normalized['threat_urgency'] = threat_assessment.get('urgency', 0.5)
        else:
            # 自动评估威胁等级
            normalized['threat_level'] = self._auto_assess_threat(normalized)
            normalized['threat_value'] = normalized['threat_level'] / 5.0
            normalized['threat_confidence'] = 0.7
            normalized['threat_urgency'] = min(1.0, normalized['threat_level'] / 5.0)
        
        return normalized
    
    def _auto_assess_threat(self, radar_state: Dict) -> int:
        """自动评估威胁等级"""
        threat_score = 0
        
        # 工作模式威胁
        mode_threat = {0: 1, 1: 2, 2: 3, 3: 3, 4: 5}
        threat_score += mode_threat.get(radar_state['work_mode'], 2)
        
        # 距离威胁
        distance = radar_state['distance']
        if distance < 20:
            threat_score += 2
        elif distance < 50:
            threat_score += 1
        
        # 频率威胁
        frequency = radar_state['frequency']
        if 8000 <= frequency <= 12000 or 14000 <= frequency <= 18000:
            threat_score += 1
        
        # 功率威胁
        if radar_state['power'] > 5e6:
            threat_score += 1
        
        return min(5, max(1, threat_score))
    
    def _generate_output(self, action: Dict) -> List:
        """生成标准输出格式"""
        threat_level = action.get('threat_level', 3)
        jamming_type = action.get('jamming_type', 0)
        
        # 提取干扰参数
        if 'actor_output' in action:
            jamming_params = self._extract_parameters(action['actor_output'], jamming_type)
        else:
            jamming_params = self._get_default_parameters(jamming_type)
        
        return [threat_level, jamming_type] + jamming_params
    
    def _extract_parameters(self, actor_output: Dict, jamming_type: int) -> List:
        """从Actor输出中提取干扰参数"""
        import torch
        
        if jamming_type == 0:  # 梳状谱
            params = actor_output.get('comb_params', torch.zeros(25))
            if hasattr(params, 'cpu'):
                params = params.cpu().numpy().flatten()
            
            count = int(params[0] * 7) + 1
            freq_offset = (params[1] - 0.5) * 2000
            flicker_period = params[9] * 100 + 10
            hold_time = params[17] * 20 + 5
            
            return [count, freq_offset, flicker_period, hold_time]
            
        elif jamming_type == 1:  # 间歇采样转发
            params = actor_output.get('isrj_params', torch.zeros(6))
            if hasattr(params, 'cpu'):
                params = params.cpu().numpy().flatten()
            
            return [
                params[0] * 190 + 10,        # 重复转发时间间隔
                int(params[1] + 0.5),        # 间歇采样开关
                params[2] * 4.5 + 0.5,       # 间歇采样周期
                params[3] * 1.9 + 0.1,       # 间歇采样宽度
                params[4] * 90000 + 10000,   # 干扰覆盖距离
                params[5] * 2.5 + 0.5        # 脉冲采样长度
            ]
            
        elif jamming_type == 2:  # 宽带阻塞噪声
            params = actor_output.get('broadband_params', torch.zeros(1))
            if hasattr(params, 'cpu'):
                params = params.cpu().numpy().flatten()
            
            return [int(params[0] * 20)]
            
        elif jamming_type == 3:  # 灵巧噪声
            params = actor_output.get('smart_noise_params', torch.zeros(7))
            if hasattr(params, 'cpu'):
                params = params.cpu().numpy().flatten()
            
            return [
                int(params[0] * 20),         # 噪声带宽选择
                int(params[1] * 2) + 1,      # 噪声源选择
                int(params[2]) + 1,          # 多普勒闪烁模式
                params[3] * 19 + 1,          # 闪烁保持时间
                params[4] * 4.5 + 0.5,       # 闪烁消失时间
                params[5] * 90 + 10,         # 多普勒噪声带宽
                params[6] * 90 + 10          # 多普勒噪声跳变周期
            ]
            
        elif jamming_type == 4:  # 拖引
            params = actor_output.get('deception_params', torch.zeros(3))
            if hasattr(params, 'cpu'):
                params = params.cpu().numpy().flatten()
            
            return [
                params[0] * 10 + 1,      # 拖引距离偏移
                params[1] * 500 + 50,    # 拖引速度偏移
                params[2] * 2 + 0.5      # 拖引持续时间
            ]
        
        return [0.0]
    
    def _get_default_parameters(self, jamming_type: int) -> List:
        """获取默认干扰参数"""
        defaults = {
            0: [1, 0.0, 50.0, 10.0],  # 梳状谱
            1: [100.0, 1, 2.5, 1.0, 50000.0, 1.5],  # 间歇采样转发
            2: [10],  # 宽带阻塞噪声
            3: [10, 2, 1, 10.0, 2.5, 50.0, 50.0],  # 灵巧噪声
            4: [5.0, 300.0, 1.5]  # 拖引
        }
        return defaults.get(jamming_type, [0.0])
    
    def batch_process(self, radar_states: List[Dict], threat_assessments: Optional[List[Dict]] = None) -> List[List]:
        """批量处理多个雷达状态"""
        results = []
        
        for i, radar_state in enumerate(radar_states):
            threat_assessment = threat_assessments[i] if threat_assessments else None
            result = self.make_decision(radar_state, threat_assessment)
            results.append(result)
        
        return results
    
    def get_jamming_type_name(self, jamming_type: int) -> str:
        """获取干扰类型名称"""
        names = {
            0: "梳状谱干扰",
            1: "间歇采样转发干扰",
            2: "宽带阻塞噪声干扰",
            3: "灵巧噪声干扰",
            4: "拖引干扰"
        }
        return names.get(jamming_type, "未知干扰类型")


def example_usage():
    """使用示例"""
    # 初始化决策核心
    core = JammingDecisionCore()
    
    # 雷达状态
    radar_state = {
        'frequency': 10000,  # MHz
        'pw': 1.0,          # μs
        'prt': 100,         # μs
        'power': 2e6,       # W
        'distance': 25,     # km
        'speed': 400,       # m/s
        'direction': 45,    # 度
        'work_mode': 4      # 制导模式
    }
    
    # 威胁评估结果（可选）
    threat_assessment = {
        'threat_level': 4,
        'threat_value': 0.8,
        'confidence': 0.9,
        'urgency': 0.7
    }
    
    # 做出决策
    decision = core.make_decision(radar_state, threat_assessment)
    
    print(f"决策结果: {decision}")
    print(f"威胁等级: {decision[0]}")
    print(f"干扰类型: {decision[1]} ({core.get_jamming_type_name(decision[1])})")
    print(f"干扰参数: {decision[2:]}")


if __name__ == "__main__":
    example_usage()
