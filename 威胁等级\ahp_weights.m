function [weights, CI_values] = ahp_weights(enable_radar_platform)
% 使用层次分析法计算各指标权重
% 输入:
%   enable_radar_platform - 情报信息的有无
% 输出:
%   weights - 各指标的权重
%   CI_values - 一致性检测

if nargin < 1
    enable_radar_platform = true;
end

% 辐射源类型和平台类型的判断矩阵A
A = [1, 2; 1/2, 1];

% 速度、距离、方向的判断矩阵B
B = [1, 1/3, 1/2; 3, 1, 2; 2, 1/2, 1];

% 脉冲重复频率、频率、脉宽、工作模式的判断矩阵C
C = [1, 6, 4, 2; 1/6, 1, 1/3, 1/5; 1/4, 3, 1, 1/3; 1/2, 5, 3, 1];

% 辐射源类型、运动要素、辐射源参数及工作模式的判断矩阵D
D = [1, 1/3, 1/3; 3, 1, 1/2; 3, 2, 1];

% 计算各矩阵的权重
[w_A, CI_A] = ahp_weight(A);
[w_B, CI_B] = ahp_weight(B);
[w_C, CI_C] = ahp_weight(C);
[w_D, CI_D] = ahp_weight(D);


% 计算最终的指标权重
if enable_radar_platform
    weights = zeros(1, 9);
    weights(1:2) = w_A * w_D(1);
    weights(3:5) = w_B * w_D(2);
    weights(6:9) = w_C * w_D(3);
else
    D_modified = [1, 1/2; 2, 1];  
    [w_D_modified, ~] = ahp_weight(D_modified);
    weights = zeros(1, 7);
    weights(1:3) = w_B * w_D_modified(1);  
    weights(4:7) = w_C * w_D_modified(2);  
end

% 归一化
weights = weights / sum(weights);

CI_values = [CI_A, CI_B, CI_C, CI_D];

end

function [weights, CI] = ahp_weight(A)

n = size(A, 1);
[V, D] = eig(A);
[lambda_max, idx] = max(diag(D));
eigenvector = V(:, idx);

weights = eigenvector / sum(eigenvector);
weights = weights';

%一致化检验
CI = (lambda_max - n) / (n - 1);

RI_values = [0, 0, 0.58, 0.90, 1.12, 1.24, 1.32, 1.41, 1.45, 1.49];
if n <= 10
    RI = RI_values(n);
else
    RI = 1.5;
end

CR = CI / RI;

if CR > 0.1 && n > 2
    warning('判断矩阵不满足一致性要求，CR = %.4f > 0.1', CR);
end

end
