#!/bin/bash
# 训练依赖包安装脚本 (优化版)

echo "安装训练依赖包..."

# 设置包目录 (支持跨目录查找)
TRAINING_DIR="."
ONNX_DIR="../onnx_packages"
RKNN_DIR="../rknn_packages"
FIND_LINKS="--find-links $TRAINING_DIR --find-links $ONNX_DIR --find-links $RKNN_DIR"

echo "1/5 安装核心计算库..."
pip install $FIND_LINKS --no-index numpy scipy matplotlib pandas

echo "2/5 安装PyTorch..."
pip install $FIND_LINKS --no-index torch torchvision torchaudio

echo "3/5 安装GPU加速库..."
pip install $FIND_LINKS --no-index cupy-cuda11x || echo "CuPy安装失败，可能需要对应的CUDA版本"

echo "4/5 安装机器学习库..."
pip install $FIND_LINKS --no-index scikit-learn optuna statsmodels

echo "5/5 安装工具库..."
pip install $FIND_LINKS --no-index tqdm pyyaml loguru joblib h5py psutil
pip install $FIND_LINKS --no-index cerberus python-dotenv typing-extensions

echo "训练依赖包安装完成!"
