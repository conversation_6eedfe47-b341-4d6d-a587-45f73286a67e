"""
简化威胁等级评估
"""

import numpy as np
from typing import Dict, List, Tuple
from enum import Enum

# 导入完整的威胁评估模块
from .membership_functions import TypeMembership, MovementMembership, WorkModeMembership
from .weight_calculator import AHPWeightCalculator, EntropyWeightCalculator


class ThreatLevel(Enum):
    """威胁等级枚举"""
    LEVEL_1 = 1  # 一级威胁（终末制导）
    LEVEL_2 = 2  # 二级威胁（成像模式）
    LEVEL_3 = 3  # 三级威胁（搜索转跟踪）
    LEVEL_4 = 4  # 四级威胁（雷达搜索模式）
    LEVEL_5 = 5  # 五级威胁（雷达未激活）


class SimplifiedThreatEvaluator:
    """
    简化威胁等级评估器
    
    专注于雷达发射参数的威胁等级评估，不考虑干扰效果影响
    """
    
    # 威胁等级阈值（调整为更均匀的分布）
    THREAT_THRESHOLDS = {
        ThreatLevel.LEVEL_1: (0.8, 1.000),   # 一级威胁（极高）
        ThreatLevel.LEVEL_2: (0.6, 0.8),     # 二级威胁（高）
        ThreatLevel.LEVEL_3: (0.4, 0.6),     # 三级威胁（中）
        ThreatLevel.LEVEL_4: (0.2, 0.4),     # 四级威胁（低）
        ThreatLevel.LEVEL_5: (0.0, 0.2),     # 五级威胁（极低）
    }
    
    def __init__(self, enable_radar_platform: bool = False, l1: float = 30, l2: float = 300):
        """
        初始化简化威胁评估器
        
        Args:
            enable_radar_platform: 是否启用雷达类型和平台类型
            l1: 最大威胁程度对应距离 (km)
            l2: 最小威胁程度对应距离 (km)
        """
        self.enable_radar_platform = enable_radar_platform
        self.l1 = l1
        self.l2 = l2
        
        # 计算权重
        self._calculate_weights()
        
        print(f"简化威胁评估器初始化完成")
        print(f"   雷达平台评估: {'启用' if enable_radar_platform else '禁用'}")
        print(f"   威胁距离范围: {l1}km - {l2}km")
    
    def _calculate_weights(self):
        """计算综合权重"""
        # 层次分析法权重
        w_ahp, self.ci_values = AHPWeightCalculator.calculate_weights(self.enable_radar_platform)

        # 熵权法权重
        sample_data = EntropyWeightCalculator.generate_sample_data(self.enable_radar_platform, self.l1, self.l2)
        w_entropy = EntropyWeightCalculator.calculate_weights(sample_data)

        # 综合权重（几何平均）
        combined = np.sqrt(w_ahp * w_entropy)
        self.weights = combined / np.sum(combined)

        # 初始化隶属度函数参数（为GPU版本提供支持）
        self._initialize_membership_params()

        # if self.enable_radar_platform:
        #     print(f"   AHP权重: {w_ahp}")
        #     print(f"   熵权法权重: {w_entropy}")
        #     print(f"   综合权重: {self.weights}")
        # else:
        #     print(f"   AHP权重: {w_ahp}")
        #     print(f"   熵权法权重: {w_entropy}")
        #     print(f"   综合权重: {self.weights}")

    def _initialize_membership_params(self):
        """初始化隶属度函数参数"""
        self.membership_params = {
            'speed': {
                'high_threshold': 300,
                'medium_threshold': 200,
                'low_threshold': 100
            },
            'distance': {
                'max_distance': 200.0,  # km
                'min_threat': 0.1
            },
            'direction': {
                'high_threat_angle': 30,  # degrees
                'medium_threat_angle': 90
            },
            'prf': {
                'high_threshold': 5000,  # Hz
                'medium_threshold': 1000
            },
            'frequency': {
                'bands': {
                    'X': 8e9,   # X波段阈值
                    'C': 4e9,   # C波段阈值
                    'S': 2e9,   # S波段阈值
                    'L': 1e9    # L波段阈值
                }
            },
            'pulse_width': {
                'short_threshold': 1e-6,   # 短脉冲阈值
                'medium_threshold': 5e-6   # 中等脉冲阈值
            },
            'work_mode': {
                'threat_levels': {
                    1: 0.2,  # 搜索模式
                    2: 0.5,  # 跟踪模式
                    3: 0.8,  # 制导模式
                    4: 1.0   # 终末制导模式
                }
            }
        }

        # 初始化特征名称列表（为GPU版本提供支持）
        if self.enable_radar_platform:
            self.feature_names = ['radar_type', 'platform_type', 'speed', 'distance',
                                'direction', 'prf', 'frequency', 'pulse_width', 'work_mode']
        else:
            self.feature_names = ['speed', 'distance', 'direction', 'prf',
                                'frequency', 'pulse_width', 'work_mode']
    
    def calculate_membership_values(self, radar_data: Dict) -> np.ndarray:
        """
        计算隶属度值

        Args:
            radar_data: 雷达数据字典 (统一单位标准)
                - radar_type: 雷达类型
                - platform_type: 平台类型
                - speed: 速度 (m/s)
                - distance: 距离 (km)
                - direction: 航向角 (度)
                - prt: 脉冲重复周期 (μs) 或 pulse_repetition_freq: 脉冲重复频率 (Hz，兼容旧版)
                - frequency: 载频 (MHz)
                - pw: 脉宽 (μs)
                - work_mode: 工作模式

        Returns:
            隶属度值数组
        """
        # 处理脉冲重复周期/频率参数兼容性
        if 'prt' in radar_data:
            prt_us = radar_data['prt']  # 标准单位：μs
        elif 'pulse_repetition_freq' in radar_data:
            # 兼容旧版：从Hz转换为μs
            prf_hz = radar_data['pulse_repetition_freq']
            prt_us = 1e6 / prf_hz  # Hz -> μs
        else:
            prt_us = 125.0  # 默认值：125μs (对应8kHz PRF)

        # 计算基础隶属度
        x1, x2 = TypeMembership.calculate(radar_data.get('radar_type', 1),
                                         radar_data.get('platform_type', 1))
        x3, x4, x5 = MovementMembership.calculate(radar_data.get('speed', 0),
                                                 radar_data.get('distance', 100),
                                                 radar_data.get('direction', 0),
                                                 self.l1, self.l2)
        x6, x7, x8, x9 = WorkModeMembership.calculate(prt_us,
                                                     radar_data.get('frequency', 10000),  # 默认10GHz = 10000MHz
                                                     radar_data.get('pw', 1.0),  # 默认1μs
                                                     radar_data.get('work_mode', 1))

        if self.enable_radar_platform:
            membership_values = np.array([x1, x2, x3, x4, x5, x6, x7, x8, x9])
        else:
            membership_values = np.array([x3, x4, x5, x6, x7, x8, x9])

        return membership_values
    
    def calculate_threat_value(self, radar_data: Dict) -> float:
        """
        计算威胁度值
        
        Args:
            radar_data: 雷达数据字典
            
        Returns:
            威胁度值 [0, 1]
        """
        membership_values = self.calculate_membership_values(radar_data)
        threat_value = np.sum(membership_values * self.weights)
        return threat_value
    
    def determine_threat_level(self, threat_value: float) -> ThreatLevel:
        """
        确定威胁等级
        按照MATLAB代码中的威胁等级阈值进行判断

        Args:
            threat_value: 威胁度值

        Returns:
            威胁等级
        """
        # 按照MATLAB代码中的威胁等级阈值：[0, 0.21, 0.421, 0.655, 0.792, 1]
        if threat_value > 0.792 and threat_value <= 1.000:
            return ThreatLevel.LEVEL_1  # 一级威胁（极高威胁）
        elif threat_value > 0.655 and threat_value <= 0.792:
            return ThreatLevel.LEVEL_2  # 二级威胁（高威胁）
        elif threat_value > 0.421 and threat_value <= 0.655:
            return ThreatLevel.LEVEL_3  # 三级威胁（中等威胁）
        elif threat_value > 0.210 and threat_value <= 0.421:
            return ThreatLevel.LEVEL_4  # 四级威胁（低威胁）
        elif threat_value >= 0.000 and threat_value <= 0.210:
            return ThreatLevel.LEVEL_5  # 五级威胁（极低威胁）
        else:
            # 威胁度值超出范围，返回默认值
            return ThreatLevel.LEVEL_5
    
    def evaluate_threat(self, radar_data: Dict) -> Tuple[float, ThreatLevel, Dict]:
        """
        完整的威胁评估
        
        Args:
            radar_data: 雷达数据字典
            
        Returns:
            (威胁度值, 威胁等级, 详细信息字典)
        """
        # 计算隶属度值
        membership_values = self.calculate_membership_values(radar_data)
        
        # 计算威胁度值
        threat_value = np.sum(membership_values * self.weights)
        
        # 确定威胁等级
        threat_level = self.determine_threat_level(threat_value)
        
        # 构建详细信息
        if self.enable_radar_platform:
            membership_names = ['雷达类型威胁度', '平台类型威胁度', '速度威胁度', '距离威胁度',
                              '航向威胁度', '重频威胁度', '载频威胁度', '脉宽威胁度', '工作模式威胁度']
        else:
            membership_names = ['速度威胁度', '距离威胁度', '航向威胁度', '重频威胁度',
                              '载频威胁度', '脉宽威胁度', '工作模式威胁度']

        details = {
            'threat_value': threat_value,
            'threat_level': threat_level,
            'membership_values': dict(zip(membership_names, membership_values)),
            'weights': dict(zip(membership_names, self.weights)),
            'radar_data': radar_data.copy()
        }

        return threat_value, threat_level, details
    
    def evaluate_multiple_threats(self, radar_data_list: List[Dict]) -> List[Tuple[float, ThreatLevel, Dict]]:
        """
        评估多个威胁目标
        
        Args:
            radar_data_list: 雷达数据字典列表
            
        Returns:
            评估结果列表
        """
        results = []
        for radar_data in radar_data_list:
            result = self.evaluate_threat(radar_data)
            results.append(result)
        
        return results
    
    def get_threat_ranking(self, radar_data_list: List[Dict]) -> List[Tuple[int, float, ThreatLevel]]:
        """
        获取威胁等级排序
        
        Args:
            radar_data_list: 雷达数据字典列表
            
        Returns:
            排序结果列表 [(索引, 威胁度值, 威胁等级), ...]
        """
        results = self.evaluate_multiple_threats(radar_data_list)
        
        # 创建排序数据
        ranking_data = []
        for i, (threat_value, threat_level, _) in enumerate(results):
            ranking_data.append((i, threat_value, threat_level))
        
        # 按威胁度值降序排序
        ranking_data.sort(key=lambda x: x[1], reverse=True)
        
        return ranking_data
    
    @staticmethod
    def get_threat_level_description(threat_level: ThreatLevel) -> str:
        """
        获取威胁等级描述
        
        Args:
            threat_level: 威胁等级
            
        Returns:
            威胁等级描述字符串
        """
        descriptions = {
            ThreatLevel.LEVEL_1: "一级威胁（终末制导）",
            ThreatLevel.LEVEL_2: "二级威胁（成像模式）",
            ThreatLevel.LEVEL_3: "三级威胁（搜索转跟踪）",
            ThreatLevel.LEVEL_4: "四级威胁（雷达搜索模式）",
            ThreatLevel.LEVEL_5: "五级威胁（雷达未激活）"
        }
        return descriptions.get(threat_level, "未知威胁等级")


# 测试示例
if __name__ == "__main__":
    # 创建简化威胁评估器
    evaluator = SimplifiedThreatEvaluator(enable_radar_platform=True)
    
    # 测试雷达数据
    test_radar_data = {
        'radar_type': 1,
        'platform_type': 2,
        'speed': 200,  # m/s
        'distance': 50,  # km
        'direction': 45,  # 度
        'pulse_repetition_freq': 1000,  # Hz
        'frequency': 10e9,  # Hz
        'pw': 1e-6,  # s
        'work_mode': 3
    }
    
    # 进行威胁评估
    threat_value, threat_level, details = evaluator.evaluate_threat(test_radar_data)
    
    print(f"\n威胁评估结果:")
    print(f"   威胁度值: {threat_value:.4f}")
    print(f"   威胁等级: {evaluator.get_threat_level_description(threat_level)}")
    print(f"   各项威胁度: {details['membership_values']}")
