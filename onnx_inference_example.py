#!/usr/bin/env python3
"""
使用ONNX模型进行雷达干扰决策推理的示例
"""

import os
import sys
import numpy as np
from typing import Dict, List, Tuple, Optional

try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    print("请安装onnxruntime: pip install onnxruntime")
    ONNX_AVAILABLE = False

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ONNXJammingDecision:
    """基于ONNX的雷达干扰决策推理器"""
    
    def __init__(self, actor_onnx_path: str, critic_onnx_path: Optional[str] = None):
        """
        初始化ONNX推理器
        
        Args:
            actor_onnx_path: Actor网络ONNX模型路径
            critic_onnx_path: Critic网络ONNX模型路径（可选）
        """
        if not ONNX_AVAILABLE:
            raise ImportError("ONNX Runtime不可用")
        
        self.actor_session = None
        self.critic_session = None
        
        # 加载Actor模型
        if os.path.exists(actor_onnx_path):
            self.actor_session = ort.InferenceSession(actor_onnx_path)
            print(f"✓ Actor ONNX模型已加载: {actor_onnx_path}")
        else:
            raise FileNotFoundError(f"Actor ONNX模型不存在: {actor_onnx_path}")
        
        # 加载Critic模型（可选）
        if critic_onnx_path and os.path.exists(critic_onnx_path):
            self.critic_session = ort.InferenceSession(critic_onnx_path)
            print(f"✓ Critic ONNX模型已加载: {critic_onnx_path}")
        
        # 干扰类型映射
        self.jamming_type_names = {
            0: "无干扰",
            1: "间歇采样",
            2: "宽带噪声", 
            3: "灵巧噪声",
            4: "拖引干扰"
        }
    
    def preprocess_radar_input(self, radar_input: Dict) -> np.ndarray:
        """
        预处理雷达输入，转换为模型需要的状态向量
        
        Args:
            radar_input: 雷达输入字典
            
        Returns:
            归一化的状态向量 (1, 12)
        """
        # 模拟威胁评估（简化版本）
        threat_level = self._estimate_threat_level(radar_input)
        threat_value = threat_level / 5.0
        confidence = 0.8
        urgency = min(1.0, (6 - threat_level) / 5.0)
        
        # 构建12维状态向量
        state = [
            # 威胁信息 (4维)
            threat_level / 5.0,                                    # [0] 威胁等级
            threat_value,                                           # [1] 威胁值
            confidence,                                             # [2] 置信度
            urgency,                                                # [3] 紧急度
            
            # 雷达参数 (8维)
            radar_input.get('frequency', 10000.0) / 20000.0,      # [4] 频率
            radar_input.get('pw', 1.0) / 10.0,                    # [5] 脉宽
            radar_input.get('prt', 100.0) / 1000.0,               # [6] PRT
            radar_input.get('power', 1e6) / 1e7,                  # [7] 功率
            radar_input.get('distance', 50.0) / 300.0,            # [8] 距离
            radar_input.get('speed', 300.0) / 1000.0,             # [9] 速度
            radar_input.get('direction', 0.0) / 360.0,            # [10] 方向
            radar_input.get('work_mode', 1) / 4.0                 # [11] 工作模式
        ]
        
        return np.array(state, dtype=np.float32).reshape(1, -1)
    
    def _estimate_threat_level(self, radar_input: Dict) -> int:
        """简化的威胁等级估算"""
        distance = radar_input.get('distance', 50.0)
        work_mode = radar_input.get('work_mode', 1)
        power = radar_input.get('power', 1e6)
        
        # 基于距离的威胁评估
        if distance < 20:
            base_threat = 1  # 高威胁
        elif distance < 50:
            base_threat = 2  # 中高威胁
        elif distance < 100:
            base_threat = 3  # 中等威胁
        elif distance < 200:
            base_threat = 4  # 低威胁
        else:
            base_threat = 5  # 极低威胁
        
        # 工作模式调整
        if work_mode == 4:  # 制导模式
            base_threat = max(1, base_threat - 1)
        elif work_mode == 0:  # 静默模式
            base_threat = min(5, base_threat + 1)
        
        return base_threat
    
    def predict(self, radar_input: Dict) -> Dict:
        """
        进行干扰决策预测
        
        Args:
            radar_input: 雷达输入参数
            
        Returns:
            决策结果字典
        """
        # 预处理输入
        state_vector = self.preprocess_radar_input(radar_input)
        
        # Actor推理
        actor_inputs = {self.actor_session.get_inputs()[0].name: state_vector}
        actor_outputs = self.actor_session.run(None, actor_inputs)
        
        # 解析Actor输出
        threat_level_probs = actor_outputs[0][0]  # (5,)
        jamming_type_probs = actor_outputs[1][0]  # (5,)
        comb_params = actor_outputs[2][0]         # (25,)
        isrj_params = actor_outputs[3][0]         # (6,)
        broadband_params = actor_outputs[4][0]    # (1,)
        smart_noise_params = actor_outputs[5][0]  # (7,)
        deception_params = actor_outputs[6][0]    # (3,)
        
        # 决策结果
        predicted_threat_level = np.argmax(threat_level_probs) + 1
        jamming_type = np.argmax(jamming_type_probs)
        should_jam = jamming_type > 0
        
        # 计算干扰功率
        if should_jam:
            base_power = (6 - predicted_threat_level) / 5.0
            type_multiplier = {0: 0.0, 1: 0.8, 2: 0.6, 3: 0.7, 4: 0.9}.get(jamming_type, 0.5)
            jamming_power = base_power * type_multiplier
        else:
            jamming_power = 0.0
        
        # Critic推理（如果可用）
        state_value = None
        if self.critic_session:
            critic_inputs = {self.critic_session.get_inputs()[0].name: state_vector}
            critic_outputs = self.critic_session.run(None, critic_inputs)
            state_value = critic_outputs[0][0][0]
        
        return {
            'should_jam': should_jam,
            'jamming_type': jamming_type,
            'jamming_type_name': self.jamming_type_names.get(jamming_type, "未知"),
            'jamming_power': jamming_power,
            'predicted_threat_level': predicted_threat_level,
            'threat_level_probs': threat_level_probs.tolist(),
            'jamming_type_probs': jamming_type_probs.tolist(),
            'state_value': state_value,
            'jamming_params': {
                'comb_params': comb_params.tolist(),
                'isrj_params': isrj_params.tolist(),
                'broadband_params': broadband_params.tolist(),
                'smart_noise_params': smart_noise_params.tolist(),
                'deception_params': deception_params.tolist()
            }
        }

def demo_onnx_inference():
    """演示ONNX推理"""
    print("ONNX雷达干扰决策推理演示")
    print("=" * 50)
    
    # ONNX模型路径
    actor_onnx_path = "models/onnx/ppo_actor.onnx"
    critic_onnx_path = "models/onnx/ppo_critic.onnx"
    
    # 检查模型文件
    if not os.path.exists(actor_onnx_path):
        print(f"✗ Actor ONNX模型不存在: {actor_onnx_path}")
        print("请先运行 convert_to_onnx.py 转换模型")
        return
    
    try:
        # 初始化推理器
        predictor = ONNXJammingDecision(actor_onnx_path, critic_onnx_path)
        
        # 测试场景
        test_scenarios = [
            {
                "name": "高威胁制导雷达",
                "radar_input": {
                    'frequency': 9500.0,
                    'pw': 0.5,
                    'prt': 50.0,
                    'power': 5e6,
                    'distance': 15.0,
                    'speed': 250.0,
                    'direction': 30.0,
                    'work_mode': 4
                }
            },
            {
                "name": "低威胁搜索雷达",
                "radar_input": {
                    'frequency': 3000.0,
                    'pw': 2.0,
                    'prt': 1000.0,
                    'power': 1e6,
                    'distance': 120.0,
                    'speed': 100.0,
                    'direction': 180.0,
                    'work_mode': 1
                }
            },
            {
                "name": "中威胁跟踪雷达",
                "radar_input": {
                    'frequency': 5500.0,
                    'pw': 1.0,
                    'prt': 200.0,
                    'power': 2e6,
                    'distance': 60.0,
                    'speed': 200.0,
                    'direction': 90.0,
                    'work_mode': 2
                }
            }
        ]
        
        # 执行推理
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n场景 {i}: {scenario['name']}")
            print("-" * 30)
            
            # 显示输入
            radar_input = scenario['radar_input']
            print("雷达参数:")
            print(f"  频率: {radar_input['frequency']:.1f} MHz")
            print(f"  距离: {radar_input['distance']:.1f} km")
            print(f"  工作模式: {radar_input['work_mode']}")
            
            # 推理
            result = predictor.predict(radar_input)
            
            # 显示结果
            print("\n决策结果:")
            print(f"  是否干扰: {'是' if result['should_jam'] else '否'}")
            print(f"  干扰类型: {result['jamming_type_name']}")
            print(f"  干扰功率: {result['jamming_power']:.3f}")
            print(f"  预测威胁等级: {result['predicted_threat_level']}")
            
            if result['state_value'] is not None:
                print(f"  状态价值: {result['state_value']:.3f}")
            
            # 显示概率分布
            print(f"  威胁等级概率: {[f'{p:.3f}' for p in result['threat_level_probs']]}")
            print(f"  干扰类型概率: {[f'{p:.3f}' for p in result['jamming_type_probs']]}")
        
        print(f"\n{'='*50}")
        print("✅ ONNX推理演示完成！")
        print("\n优势:")
        print("• 推理速度快，适合实时应用")
        print("• 跨平台部署，支持多种编程语言")
        print("• 模型文件小，便于嵌入式部署")
        print("• 无需PyTorch依赖，降低部署复杂度")
        
    except Exception as e:
        print(f"✗ ONNX推理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_onnx_inference()
