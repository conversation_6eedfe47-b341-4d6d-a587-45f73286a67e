"""
权重计算模块
"""

import numpy as np
import warnings
from typing import Tu<PERSON>, Dict, List


class AHPWeightCalculator:
    """层次分析法权重计算器"""
    
    # 随机一致性指标RI值表
    RI_VALUES = [0, 0, 0.58, 0.90, 1.12, 1.24, 1.32, 1.41, 1.45, 1.49]
    
    @staticmethod
    def calculate_weights(enable_radar_platform: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        使用层次分析法计算各指标权重
        
        Args:
            enable_radar_platform: 是否启用雷达类型和平台类型评估
            
        Returns:
            (权重向量, 一致性检验结果字典)
        """
        # 定义判断矩阵
        # 辐射源类型和平台类型的判断矩阵A
        A = np.array([
            [1, 2],
            [1/2, 1]
        ])
        
        # 速度、距离、方向的判断矩阵B (调整方向权重，使其更平衡)
        B = np.array([
            [1, 1/4, 1],      # 速度：与距离比1/4，与方向比1/1
            [4, 1, 3],        # 距离：与速度比4/1，与方向比3/1 (距离最重要)
            [1, 1/3, 1]       # 方向：与速度比1/1，与距离比1/3 (降低方向权重)
        ])
        
        # 脉冲重复频率、频率、脉宽、工作模式的判断矩阵C
        C = np.array([
            [1, 6, 4, 2],
            [1/6, 1, 1/3, 1/5],
            [1/4, 3, 1, 1/3],
            [1/2, 5, 3, 1]
        ])
        
        # 计算各矩阵的权重
        w_A, CI_A = AHPWeightCalculator._calculate_matrix_weight(A)
        w_B, CI_B = AHPWeightCalculator._calculate_matrix_weight(B)
        w_C, CI_C = AHPWeightCalculator._calculate_matrix_weight(C)
        
        if enable_radar_platform:
            # 辐射源类型、运动要素、辐射源参数及工作模式的判断矩阵D
            D = np.array([
                [1, 1/3, 1/3],
                [3, 1, 1/2],
                [3, 2, 1]
            ])
            w_D, CI_D = AHPWeightCalculator._calculate_matrix_weight(D)
            
            # 计算最终的指标权重（9个指标）
            weights = np.zeros(9)
            weights[0:2] = w_A * w_D[0]  # 雷达类型、平台类型
            weights[2:5] = w_B * w_D[1]  # 速度、距离、方向
            weights[5:9] = w_C * w_D[2]  # PRF、频率、脉宽、工作模式
            
            CI_values = {'CI_A': CI_A, 'CI_B': CI_B, 'CI_C': CI_C, 'CI_D': CI_D}
        else:
            # 运动要素、辐射源参数及工作模式的判断矩阵D_modified
            D_modified = np.array([
                [1, 1/2],
                [2, 1]
            ])
            w_D_modified, CI_D_modified = AHPWeightCalculator._calculate_matrix_weight(D_modified)
            
            # 计算最终的指标权重（7个指标）
            weights = np.zeros(7)
            weights[0:3] = w_B * w_D_modified[0]  # 速度、距离、方向
            weights[3:7] = w_C * w_D_modified[1]  # PRF、频率、脉宽、工作模式
            
            CI_values = {'CI_B': CI_B, 'CI_C': CI_C, 'CI_D_modified': CI_D_modified}
        
        # 归一化权重
        weights = weights / np.sum(weights)
        
        return weights, CI_values
    
    @staticmethod
    def _calculate_matrix_weight(matrix: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        计算单个判断矩阵的权重向量和一致性指标
        
        Args:
            matrix: 判断矩阵
            
        Returns:
            (权重向量, 一致性指标CI)
        """
        n = matrix.shape[0]
        
        # 计算特征值和特征向量
        eigenvalues, eigenvectors = np.linalg.eig(matrix)
        
        # 找到最大特征值及其对应的特征向量
        max_eigenvalue_idx = np.argmax(eigenvalues.real)
        lambda_max = eigenvalues[max_eigenvalue_idx].real
        eigenvector = eigenvectors[:, max_eigenvalue_idx].real
        
        # 归一化特征向量得到权重
        weights = eigenvector / np.sum(eigenvector)
        
        # 一致性检验
        CI = (lambda_max - n) / (n - 1) if n > 1 else 0
        
        # 计算一致性比率CR
        if n <= len(AHPWeightCalculator.RI_VALUES):
            RI = AHPWeightCalculator.RI_VALUES[n-1] if n > 0 else 1.5
        else:
            RI = 1.5
        
        CR = CI / RI if RI > 0 else 0
        
        # 一致性检验警告
        if CR > 0.1 and n > 2:
            warnings.warn(f'判断矩阵不满足一致性要求，CR = {CR:.4f} > 0.1')
        
        return weights, CI


class EntropyWeightCalculator:
    """熵权法权重计算器"""
    
    @staticmethod
    def calculate_weights(data: np.ndarray) -> np.ndarray:
        """
        使用熵权法计算权重
        
        Args:
            data: 样本数据矩阵 (m×n，m个样本，n个指标)
            
        Returns:
            权重向量
        """
        m, n = data.shape
        
        # 数据标准化
        normalized_data = np.zeros((m, n))
        for j in range(n):
            min_val = np.min(data[:, j])
            max_val = np.max(data[:, j])
            if max_val == min_val:
                normalized_data[:, j] = np.ones(m)
            else:
                normalized_data[:, j] = (data[:, j] - min_val) / (max_val - min_val)
        
        # 计算指标比重
        P = np.zeros((m, n))
        for j in range(n):
            sum_col = np.sum(normalized_data[:, j])
            if sum_col == 0:
                P[:, j] = np.ones(m) / m
            else:
                P[:, j] = normalized_data[:, j] / sum_col
        
        # 计算熵值
        e = np.zeros(n)
        k = 1 / np.log(m) if m > 1 else 1
        
        for j in range(n):
            P_nonzero = P[:, j].copy()
            P_nonzero[P_nonzero == 0] = 1e-10  # 避免log(0)
            e[j] = -k * np.sum(P_nonzero * np.log(P_nonzero))
        
        # 计算信息熵冗余度
        d = 1 - e
        
        # 计算各指标权重
        if np.sum(d) == 0:
            weights = np.ones(n) / n
        else:
            weights = d / np.sum(d)
        
        return weights
    
    @staticmethod
    def generate_sample_data(enable_radar_platform: bool = True, 
                           l1: float = 30, l2: float = 300, 
                           num_samples: int = 50) -> np.ndarray:
        """
        生成用于熵权法计算的样本数据
        
        Args:
            enable_radar_platform: 是否启用雷达类型和平台类型
            l1: 最大威胁程度对应距离 (km)
            l2: 最小威胁程度对应距离 (km)
            num_samples: 样本数量
            
        Returns:
            样本数据矩阵
        """
        np.random.seed(42)  # 设置随机种子以确保结果可重现
        
        if enable_radar_platform:
            # 9个指标的样本数据
            data = np.zeros((num_samples, 9))
            
            # 雷达类型威胁度 (0-1)
            data[:, 0] = np.random.choice([0.1, 0.5, 0.8, 1.0], num_samples, 
                                        p=[0.2, 0.3, 0.3, 0.2])
            
            # 平台类型威胁度 (0-1)
            data[:, 1] = np.random.choice([0.5, 0.7, 0.9, 1.0], num_samples,
                                        p=[0.25, 0.25, 0.25, 0.25])
            
            # 速度威胁度 (0-1) - 模拟隶属度计算
            speeds = np.random.uniform(0, 5, num_samples)  # Ma (内部计算单位)
            data[:, 2] = np.minimum(speeds / 3, 1.0)

            # 距离威胁度 (0-1) - 模拟隶属度计算
            distances = np.random.uniform(10, 400, num_samples)  # km (内部计算单位)
            data[:, 3] = np.maximum(0, np.minimum(1.0, (l2 - distances) / (l2 - l1)))

            # 方向威胁度 (0-1) - 模拟隶属度计算
            directions = np.random.uniform(-np.pi, np.pi, num_samples)  # 弧度 (内部计算单位)
            data[:, 4] = 1 / (1 + (directions / np.pi) ** 2)

            # PRF威胁度 (0-1) - 模拟隶属度计算
            prfs = np.random.uniform(0.05, 2.0, num_samples)  # kHz (内部计算单位)
            data[:, 5] = np.where(prfs <= 0.1, 0, 1 - np.exp(-5 * (prfs - 0.1) ** 2))

            # 频率威胁度 (0-1) - 模拟隶属度计算
            freqs = np.random.uniform(0.01, 15, num_samples)  # GHz (内部计算单位)
            data[:, 6] = np.where(freqs > 8, 1.0,
                         np.where(freqs > 2, 0.6,
                         np.where(freqs > 0.3, 0.3,
                         np.where(freqs > 0.03, 0.1, 0.0))))

            # 脉宽威胁度 (0-1) - 模拟隶属度计算
            pws = np.random.uniform(0.5, 50, num_samples)  # μs (内部计算单位)
            data[:, 7] = 1 / (1 + (0.1 * pws) ** 2)
            
            # 工作模式威胁度 (0-1)
            data[:, 8] = np.random.choice([0.2, 0.5, 0.8, 1.0], num_samples,
                                        p=[0.3, 0.2, 0.3, 0.2])
        else:
            # 7个指标的样本数据（不包括雷达类型和平台类型）
            data = np.zeros((num_samples, 7))
            
            # 速度威胁度 (0-1)
            speeds = np.random.uniform(0, 5, num_samples)  # Ma
            data[:, 0] = np.minimum(speeds / 3, 1.0)
            
            # 距离威胁度 (0-1)
            distances = np.random.uniform(10, 400, num_samples)  # km
            data[:, 1] = np.maximum(0, np.minimum(1.0, (l2 - distances) / (l2 - l1)))
            
            # 方向威胁度 (0-1)
            directions = np.random.uniform(-np.pi, np.pi, num_samples)  # 弧度
            data[:, 2] = 1 / (1 + (directions / np.pi) ** 2)
            
            # PRF威胁度 (0-1)
            prfs = np.random.uniform(0.05, 2.0, num_samples)  # kHz
            data[:, 3] = np.where(prfs <= 0.1, 0, 1 - np.exp(-5 * (prfs - 0.1) ** 2))
            
            # 频率威胁度 (0-1)
            freqs = np.random.uniform(0.01, 15, num_samples)  # GHz
            data[:, 4] = np.where(freqs > 8, 1.0,
                         np.where(freqs > 2, 0.6,
                         np.where(freqs > 0.3, 0.3,
                         np.where(freqs > 0.03, 0.1, 0.0))))
            
            # 脉宽威胁度 (0-1)
            pws = np.random.uniform(0.5, 50, num_samples)  # μs
            data[:, 5] = 1 / (1 + (0.1 * pws) ** 2)
            
            # 工作模式威胁度 (0-1)
            data[:, 6] = np.random.choice([0.2, 0.5, 0.8, 1.0], num_samples,
                                        p=[0.3, 0.2, 0.3, 0.2])
        
        return data


# 测试函数
if __name__ == "__main__":
    print("=== AHP权重计算测试 ===")
    
    # 测试启用雷达平台的情况
    weights_with_platform, ci_values_with = AHPWeightCalculator.calculate_weights(True)
    print("启用雷达平台权重:", weights_with_platform)
    print("一致性检验:", ci_values_with)
    
    # 测试不启用雷达平台的情况
    weights_without_platform, ci_values_without = AHPWeightCalculator.calculate_weights(False)
    print("不启用雷达平台权重:", weights_without_platform)
    print("一致性检验:", ci_values_without)
    
    print("\n=== 熵权法测试 ===")
    
    # 生成样本数据并计算熵权法权重
    sample_data = EntropyWeightCalculator.generate_sample_data(True)
    entropy_weights = EntropyWeightCalculator.calculate_weights(sample_data)
    #print("熵权法权重:", entropy_weights)
    
    # 综合权重计算
    combined_weights = np.sqrt(weights_with_platform * entropy_weights)
    combined_weights = combined_weights / np.sum(combined_weights)
    #print("综合权重:", combined_weights)
