#ifndef DATA_STRUCTURES_H
#define DATA_STRUCTURES_H

// 定义M_PI常量（Windows可能没有定义）
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 雷达参数结构体
typedef struct {
    double frequency;      // 频率 (MHz)
    double pulse_width;    // 脉宽 (us)
    double prt;           // 脉冲重复周期 (us)
    double power;         // 功率 (W)
    double distance;      // 距离 (km)
    double speed;         // 速度 (m/s)
    double direction;     // 方向 (度)
    int work_mode;        // 工作模式
} RadarParams;

// 外部接口雷达参数结构体
typedef struct {
    double frequency;      // 频率 (MHz)
    double pulse_width;    // 脉宽 (us)
    double prt;           // 脉冲重复周期 (us)
    double power;         // 功率 (W)
    double distance;      // 距离 (km)
    double speed;         // 速度 (m/s)
    double direction;     // 方向 (度)
    int work_mode;        // 工作模式
} ExternalRadarParams;

// 威胁评估结果结构体
typedef struct {
    double threat_value;   // 威胁值 (0-1)
    int threat_level;      // 威胁等级 (0-4)
    double confidence;     // 置信度 (0-1)
    double priority;       // 优先级 (0-1)
} ThreatAssessment;

// 干扰决策结果结构体
typedef struct {
    int jamming_count;                    // 干扰策略数量
    int jamming_types[8];                 // 干扰类型数组
    double jamming_params[8][6];          // 干扰参数数组
    double confidence;                    // 总体置信度
    double threat_value;                  // 威胁值
    int threat_level;                     // 威胁等级
    int status;                          // 状态码
    char error_message[256];             // 错误信息
} JammingDecision;

// 外部接口决策结果结构体
typedef struct {
    int jamming_count;                    // 干扰策略数量
    int jamming_types[8];                 // 干扰类型数组
    double jamming_params[8][6];          // 干扰参数数组
    double confidence;                    // 总体置信度
    double threat_value;                  // 威胁值
    int threat_level;                     // 威胁等级
    int status;                          // 状态码
    char error_message[256];             // 错误信息
} ExternalDecisionResult;

// ONNX推理输入结构体
typedef struct {
    float input_data[16];     // 输入特征数据
    int input_size;           // 输入数据大小
} ONNXInput;

// ONNX推理输出结构体
typedef struct {
    float output_data[32];    // 输出数据
    int output_size;          // 输出数据大小
    float confidence;         // 置信度
} ONNXOutput;

// 错误代码枚举
typedef enum {
    RADAR_SUCCESS = 0,
    RADAR_ERROR_INVALID_PARAM = -1,
    RADAR_ERROR_MEMORY_ALLOC = -2,
    RADAR_ERROR_FILE_NOT_FOUND = -3,
    RADAR_ERROR_MODEL_LOAD = -4,
    RADAR_ERROR_INFERENCE = -5,
    RADAR_ERROR_ENGINE_NOT_READY = -6,
    RADAR_ERROR_UNKNOWN = -99
} RadarErrorCode;

// 威胁等级枚举
typedef enum {
    THREAT_LEVEL_NONE = 0,     // 无威胁
    THREAT_LEVEL_LOW = 1,      // 低威胁
    THREAT_LEVEL_MEDIUM = 2,   // 中等威胁
    THREAT_LEVEL_HIGH = 3,     // 高威胁
    THREAT_LEVEL_CRITICAL = 4  // 严重威胁
} ThreatLevel;

// 干扰类型枚举
typedef enum {
    JAMMING_TYPE_NONE = 0,        // 无干扰
    JAMMING_TYPE_NOISE = 1,       // 噪声干扰
    JAMMING_TYPE_DECEPTION = 2,   // 欺骗干扰
    JAMMING_TYPE_BARRAGE = 3,     // 阻塞干扰
    JAMMING_TYPE_SWEEP = 4,       // 扫频干扰
    JAMMING_TYPE_PULSE = 5,       // 脉冲干扰
    JAMMING_TYPE_CHAFF = 6,       // 箔条干扰
    JAMMING_TYPE_FLARE = 7        // 红外干扰
} JammingType;

// 工作模式枚举
typedef enum {
    WORK_MODE_SILENT = 0,      // 静默模式
    WORK_MODE_SEARCH = 1,      // 搜索模式
    WORK_MODE_TRACK = 2,       // 跟踪模式
    WORK_MODE_GUIDANCE = 3     // 制导模式
} WorkMode;

#ifdef __cplusplus
}
#endif

#endif // DATA_STRUCTURES_H