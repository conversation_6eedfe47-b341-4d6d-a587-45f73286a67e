#include "data_structures.h"
#include <string.h>

// 这个文件主要包含数据结构的定义
// 大部分结构体定义在头文件中
// 这里可以添加一些辅助函数

// 初始化雷达参数结构体
void init_radar_params(RadarParams* params) {
    if (params) {
        memset(params, 0, sizeof(RadarParams));
    }
}

// 初始化威胁评估结构体
void init_threat_assessment(ThreatAssessment* assessment) {
    if (assessment) {
        memset(assessment, 0, sizeof(ThreatAssessment));
    }
}

// 初始化干扰决策结构体
void init_jamming_decision(JammingDecision* decision) {
    if (decision) {
        memset(decision, 0, sizeof(JammingDecision));
        strcpy(decision->error_message, "Not initialized");
    }
}