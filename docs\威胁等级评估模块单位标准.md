# 威胁等级评估智能决策模块输入参数单位标准

## 概述

本文档定义了威胁等级评估智能决策模块输入参数的统一单位标准，确保所有模块间的数据交换和计算的一致性。

## 标准单位定义

### 1. 雷达基本参数

| 参数名称 | 英文名称 | 标准单位 | 说明 |
|---------|----------|----------|------|
| 载频 | frequency | MHz | 雷达载波频率，使用兆赫兹为基本单位 |
| 脉宽 | pulse_width | μs | 脉冲宽度，使用微秒为基本单位 |
| 脉冲重复周期 | prt | μs | 脉冲重复周期，使用微秒为基本单位 |

### 2. 目标运动参数

| 参数名称 | 英文名称 | 标准单位 | 说明 |
|---------|----------|----------|------|
| 距离 | distance | km | 目标距离，使用千米为基本单位 |
| 速度 | speed | m/s | 目标速度，使用米每秒为基本单位 |
| 方向角 | direction | 度 | 目标方向角，使用度为基本单位，范围[-180, 180] |

### 3. 工作模式参数

| 参数名称 | 英文名称 | 标准单位 | 说明 |
|---------|----------|----------|------|
| 工作模式 | work_mode | 整数编码 | 0=静默, 1=搜索, 2=跟踪, 3=成像, 4=制导 |

## 单位转换规则

### 1. 载频转换
- **标准输入单位**: MHz
- **输入范围**: 1MHz - 100,000MHz (100GHz)
- **威胁评估内部转换**:
  - 转换为GHz进行隶属度计算: `freq_ghz = freq_mhz / 1000`
  - 按照MATLAB算法逻辑进行威胁度计算

### 2. 脉冲重复周期转换
- **标准输入单位**: μs
- **输入范围**: 10μs - 10,000μs
- **威胁评估内部转换**:
  - 转换为kHz进行隶属度计算: `prf_khz = 1000 / prt_us`
  - 按照MATLAB算法逻辑进行威胁度计算

### 3. 脉宽转换
- **标准输入单位**: μs
- **输入范围**: 0.1μs - 100μs
- **威胁评估内部转换**:
  - 直接使用μs进行隶属度计算
  - 按照MATLAB算法逻辑进行威胁度计算

### 4. 速度转换
- **标准输入单位**: m/s
- **输入范围**: 0 - 2000 m/s
- **威胁评估内部转换**:
  - 转换为马赫数: `speed_ma = speed_ms / 340.0`
  - 按照MATLAB算法逻辑进行威胁度计算

### 5. 方向角转换
- **标准输入单位**: 度
- **输入范围**: -180° - 180°
- **威胁评估内部转换**:
  - 转换为弧度: `direction_rad = direction_deg * π / 180`
  - 按照MATLAB算法逻辑进行威胁度计算

### 6. 距离转换
- **标准输入单位**: km
- **输入范围**: 1km - 1000km
- **威胁评估内部转换**:
  - 直接使用km进行隶属度计算
  - 按照MATLAB算法逻辑进行威胁度计算

## 数据结构标准

### SimplifiedRadarData 标准格式
```python
@dataclass
class SimplifiedRadarData:
    # 基本发射参数 (标准单位)
    frequency: float           # 载频 (MHz)
    pulse_width: float         # 脉宽 (μs)
    prt: float                # 脉冲重复周期 (μs)

    # 工作状态 (标准单位)
    operating_mode: int       # 工作模式 (0-4)

    # 目标信息 (标准单位)
    target_range: float       # 目标距离 (km)
    target_velocity: float    # 目标速度 (m/s)
```

### 威胁评估输入字典标准格式
```python
radar_data = {
    'frequency': float,           # 载频 (MHz)
    'pulse_width': float,         # 脉宽 (μs)
    'prt': float,                # 脉冲重复周期 (μs)
    'speed': float,               # 速度 (m/s)
    'distance': float,            # 距离 (km)
    'direction': float,           # 方向角 (度)
    'work_mode': int,             # 工作模式 (0-4)
}
```

### 训练数据生成标准格式
```python
scenario = {
    'frequency': float,           # 载频 (MHz)
    'pulse_width': float,         # 脉宽 (μs)
    'prt': float,                # 脉冲重复周期 (μs)
    'distance': float,            # 距离 (km)
    'speed': float,               # 速度 (m/s)
    'direction': float,           # 方向角 (度)
    'work_mode': int,             # 工作模式 (0-4)
}
```

## 实现要求

### 1. 输入验证
- 所有模块在接收输入参数时，必须验证单位是否符合标准
- 对于不符合标准的输入，应进行自动转换并记录警告

### 2. 单位转换函数
- 实现统一的单位转换函数库
- 所有模块使用相同的转换函数，确保转换逻辑一致

### 3. 文档注释
- 所有函数和类的参数必须明确标注单位
- 数据结构定义中必须包含单位说明

### 4. 测试验证
- 实现单位转换的单元测试
- 验证不同输入格式的正确转换

## 兼容性说明

### 与MATLAB版本的兼容性
- 保持与现有MATLAB威胁评估算法的计算逻辑一致
- 确保单位转换后的计算结果与MATLAB版本相同

### 向后兼容性
- 支持旧版本的输入格式，但会发出弃用警告
- 提供迁移指南帮助更新现有代码

## 更新日志

- **v1.0** (2024-07-16): 初始版本，定义基本单位标准
