close all;
clear all;
clc;
%% 设置参数
enable_radar_platform = false; % 是否启用雷达类型和平台类型
l1 = 30;  % 最大威胁程度对应距离 (km)
l2 = 300; % 最小威胁程度对应距离 (km)
test_cases = {
    struct('radar_type', "引导指挥雷达", 'platform_type', "战斗舰艇", 'speed', 0.05, ...
    'distance', 15, 'direction', pi/3, 'pulse_repetition_freq', 0.3, ...
    'frequency', 3.1, 'pw', 6.4, 'work_mode', "跟踪模式"),

    struct('radar_type', "火控雷达", 'platform_type', "战斗机", 'speed', 6, ...
    'distance', 15, 'direction', 0, 'pulse_repetition_freq', 0.8, ...
    'frequency', 15, 'pw', 2, 'work_mode', "制导模式"),

    struct('radar_type', "导航雷达", 'platform_type', "勤务舰船", 'speed', 0.5, ...
    'distance', 250, 'direction', pi, 'pulse_repetition_freq', 0.05, ...
    'frequency', 0.2, 'pw', 20, 'work_mode', "搜索模式")
    };%三个例子，大概判断的威胁等级为三级 一级和五级
%% 层次分析法
[w_ahp, CI_values] = ahp_weights(enable_radar_platform);
%% 熵权法
sample_data = generate_sample_data(enable_radar_platform,l1,l2);
w_entropy = shangquan_weights(sample_data);
%% 综合权重
combined = sqrt(w_ahp .* w_entropy);
weights = combined / sum(combined);
%% 计算不同例子的威胁度和威胁等级
 threat_value=zeros(1,length(test_cases));
for i = 1:length(test_cases)

    case_data = test_cases{i};

    [x1,x2] = type_membership(case_data.radar_type,case_data.platform_type);
    [x3,x4,x5] = move_membership(case_data.speed,case_data.distance,case_data.direction,l1,l2);
    [x6,x7,x8,x9] = work_membership(case_data.pulse_repetition_freq,case_data.frequency,case_data.pw,case_data.work_mode);

    if enable_radar_platform
        membership_values = [x1, x2, x3, x4, x5, x6, x7, x8, x9];
    else
        membership_values = [x3, x4, x5, x6, x7, x8, x9];
    end

    threat_value(i) = sum(membership_values .* weights);

    threat_level = determine_threat_level(threat_value(i));

    fprintf('例%d的威胁度\n',i);
    fprintf('雷达类型威胁度：%4f\n',x1);
    fprintf('平台类型威胁度：%4f\n',x2);
    fprintf('速度威胁度：%4f\n',x3);
    fprintf('距离威胁度：%4f\n',x4);
    fprintf('航向威胁度：%4f\n',x5);
    fprintf('重频威胁度：%4f\n',x6);
    fprintf('载频威胁度：%4f\n',x7);
    fprintf('脉宽威胁度：%4f\n',x8);
    fprintf('工作模式威胁度：%4f\n',x9);
    fprintf('例子%d的威胁等级为：%s\n',i,threat_level)
    fprintf('===================\n');

end
%% 威胁等级排序
[sorted_values, sorted_indices] = sort(threat_value, 'descend');

fprintf('威胁等级排序\n');
fprintf('排名\t例子\t威胁度\n');
for i = 1:length(sorted_indices)
    fprintf('%d\t例%d\t%.4f\n',i, sorted_indices(i),sorted_values(i));
end
%% 可视化
figure;
threat_labels = {'五级威胁', '四级威胁', '三级威胁', '二级威胁', '一级威胁'};
threat_ranges=[0,0.21,0.421,0.655,0.792,1];
colors = [0, 0.7, 0; 0.5, 0.8, 0; 1, 1, 0; 1, 0.5, 0; 1, 0, 0];
hold on;

for i = 1:length(threat_ranges)-1
    x = [threat_ranges(i), threat_ranges(i+1), threat_ranges(i+1), threat_ranges(i)];
    y = [0, 0, 1, 1];
    fill(x, y, colors(i,:), 'EdgeColor', 'none', 'FaceAlpha', 0.5);
    text((threat_ranges(i) + threat_ranges(i+1))/2, 0.5, threat_labels{i}, ...
        'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

for i = 1:length(test_cases)

    case_data = test_cases{i};

    [x1,x2] = type_membership(case_data.radar_type,case_data.platform_type);
    [x3,x4,x5] = move_membership(case_data.speed,case_data.distance,case_data.direction,l1,l2);
    [x6,x7,x8,x9] = work_membership(case_data.pulse_repetition_freq,case_data.frequency,case_data.pw,case_data.work_mode);

    if enable_radar_platform
        membership_values = [x1, x2, x3, x4, x5, x6, x7, x8, x9];
    else
        membership_values = [x3, x4, x5, x6, x7, x8, x9];
    end
    current_threat_value = sum(membership_values .* weights);

    plot(current_threat_value, 0.5, 'ko', 'MarkerFaceColor', 'k', 'MarkerSize', 10);
    text(current_threat_value, 0.6, ['例', num2str(i)], 'HorizontalAlignment', 'center');
end

xlim([0, 1]);
ylim([0, 1]);
title_str = '威胁等级分布';
title(title_str);
xlabel('威胁度');
set(gca, 'YTick', []);
grid on;
hold off;


