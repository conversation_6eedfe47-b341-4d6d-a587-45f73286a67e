"""
隶属度函数模块
"""

import numpy as np
from typing import Tuple, Union


class TypeMembership:
    """雷达类型和平台类型隶属度计算"""
    
    @staticmethod
    def calculate(radar_type: Union[str, int], platform_type: Union[str, int]) -> Tuple[float, float]:
        """
        计算雷达类型和平台类型的隶属度
        
        Args:
            radar_type: 雷达类型（字符串或数字编码）
            platform_type: 平台类型（字符串或数字编码）
            
        Returns:
            (雷达类型隶属度, 平台类型隶属度)
        """
        # 雷达类型隶属度计算
        if isinstance(radar_type, str):
            membership_radar = TypeMembership._get_radar_membership_by_name(radar_type)
        else:
            membership_radar = TypeMembership._get_radar_membership_by_code(radar_type)
        
        # 平台类型隶属度计算
        if isinstance(platform_type, str):
            membership_platform = TypeMembership._get_platform_membership_by_name(platform_type)
        else:
            membership_platform = TypeMembership._get_platform_membership_by_code(platform_type)
        
        return membership_radar, membership_platform
    
    @staticmethod
    def _get_radar_membership_by_name(radar_type: str) -> float:
        """根据雷达类型名称计算隶属度"""
        radar_type = radar_type.lower()
        
        if any(keyword in radar_type for keyword in ["火控雷达", "制导雷达", "火控", "制导"]):
            return 1.0
        elif any(keyword in radar_type for keyword in ["引导指挥雷达", "引导", "指挥"]):
            return 0.8
        elif any(keyword in radar_type for keyword in ["搜索雷达", "搜索"]):
            return 0.5
        elif any(keyword in radar_type for keyword in ["导航雷达", "导航"]):
            return 0.1
        else:
            return 0.0
    
    @staticmethod
    def _get_radar_membership_by_code(radar_type: int) -> float:
        """根据雷达类型编码计算隶属度"""
        radar_mapping = {
            1: 0.1,   # 导航雷达
            2: 0.5,   # 搜索雷达
            3: 0.8,   # 引导指挥雷达
            4: 1.0,   # 火控雷达
            5: 1.0    # 制导雷达
        }
        return radar_mapping.get(radar_type, 0.0)
    
    @staticmethod
    def _get_platform_membership_by_name(platform_type: str) -> float:
        """根据平台类型名称计算隶属度"""
        platform_type = platform_type.lower()
        
        if any(keyword in platform_type for keyword in ["导弹"]):
            return 1.0
        elif any(keyword in platform_type for keyword in ["轰炸机", "战斗机", "战斗舰艇"]):
            return 0.9
        elif any(keyword in platform_type for keyword in ["预警机", "反潜机", "侦察船"]):
            return 0.7
        elif any(keyword in platform_type for keyword in ["空中加油机", "勤务舰船"]):
            return 0.5
        else:
            return 0.0
    
    @staticmethod
    def _get_platform_membership_by_code(platform_type: int) -> float:
        """根据平台类型编码计算隶属度"""
        platform_mapping = {
            1: 0.5,   # 勤务舰船/空中加油机
            2: 0.7,   # 预警机/反潜机/侦察船
            3: 0.9,   # 轰炸机/战斗机/战斗舰艇
            4: 1.0    # 导弹
        }
        return platform_mapping.get(platform_type, 0.0)


class MovementMembership:
    """运动信息隶属度计算"""
    
    @staticmethod
    def calculate(speed: float, distance: float, direction: float,
                 l1: float = 30, l2: float = 300) -> Tuple[float, float, float]:
        """
        计算运动信息的隶属度

        Args:
            speed: 速度值 (单位: m/s，标准输入单位)
            distance: 距离值 (单位: km，标准输入单位)
            direction: 运动方向 (单位: 度，标准输入单位)
            l1: 最大威胁程度对应距离 (km)
            l2: 最小威胁程度对应距离 (km)

        Returns:
            (速度隶属度, 距离隶属度, 方向隶属度)
        """
        # 速度隶属度计算（按照MATLAB代码逻辑）
        # 输入为m/s，转换为Ma (1 Ma ≈ 340 m/s)
        speed_ma = speed / 340.0

        # 按照MATLAB代码逻辑：if speed <= 3, membership_speed = speed / 3; else membership_speed = 1
        if speed_ma <= 3:
            membership_speed = speed_ma / 3.0
        else:
            membership_speed = 1.0

        # 距离隶属度计算（按照MATLAB代码逻辑）
        if distance <= l1:
            membership_distance = 1.0
        elif distance < l2:
            # 按照MATLAB代码：membership_distance = (l2 - distance) / (l2 - l1)
            membership_distance = (l2 - distance) / (l2 - l1)
        else:
            membership_distance = 0.0

        # 方向隶属度计算（按照MATLAB代码逻辑）
        # 输入为度，统一转换为弧度进行计算
        direction_rad = direction * np.pi / 180.0

        # 按照MATLAB代码逻辑
        if direction_rad >= 0 and direction_rad <= np.pi:
            membership_azi = 1 / (1 + (1/np.pi * direction_rad) ** 2)
        elif direction_rad >= -np.pi and direction_rad < 0:
            membership_azi = 1 / (1 + (1/np.pi * abs(direction_rad)) ** 2)
        else:
            membership_azi = 0.0

        return membership_speed, membership_distance, membership_azi


class WorkModeMembership:
    """工作模式和参数隶属度计算"""
    
    @staticmethod
    def calculate(prt: float, freq: float, pw: float,
                 work_mode: Union[str, int]) -> Tuple[float, float, float, float]:
        """
        计算参数与工作模式的隶属度

        Args:
            prt: 脉冲重复周期 (单位: μs，标准输入单位)
            freq: 载频 (单位: MHz，标准输入单位)
            pw: 脉宽 (单位: μs，标准输入单位)
            work_mode: 工作模式（字符串或数字编码）

        Returns:
            (PRF隶属度, 频率隶属度, 脉宽隶属度, 工作模式隶属度)
        """
        # PRF隶属度计算（按照MATLAB代码逻辑）
        # 输入为脉冲重复周期(μs)，转换为PRF(kHz)
        prf_khz = 1000.0 / prt  # PRT(μs) -> PRF(kHz)

        # 按照MATLAB代码逻辑：if prf <= 0.1, membership_prf = 0; else membership_prf = 1 - exp(-5 * (prf - 0.1)^2)
        if prf_khz <= 0.1:
            membership_prf = 0.0
        else:
            membership_prf = 1 - np.exp(-5 * (prf_khz - 0.1) ** 2)
        
        # 频率隶属度计算（按照MATLAB代码逻辑）
        # 输入为MHz，转换为GHz进行计算
        freq_ghz = freq / 1000.0

        # 按照MATLAB代码逻辑的频率分段
        if freq_ghz > 8:
            membership_freq = 1.0
        elif freq_ghz > 2 and freq_ghz <= 8:
            membership_freq = 0.6
        elif freq_ghz > 0.3 and freq_ghz <= 2:
            membership_freq = 0.3
        elif freq_ghz > 0.03 and freq_ghz <= 0.3:
            membership_freq = 0.1
        else:
            membership_freq = 0.0
        
        # 脉宽隶属度计算（按照MATLAB代码逻辑）
        # 输入为μs，直接使用
        pw_us = pw

        # 按照MATLAB代码逻辑：membership_pw = 1 / (1 + (0.1 * pw)^2)
        membership_pw = 1 / (1 + (0.1 * pw_us) ** 2)
        
        # 工作模式隶属度计算
        if isinstance(work_mode, str):
            membership_mode = WorkModeMembership._get_mode_membership_by_name(work_mode)
        else:
            membership_mode = WorkModeMembership._get_mode_membership_by_code(work_mode)
        
        return membership_prf, membership_freq, membership_pw, membership_mode
    
    @staticmethod
    def _get_mode_membership_by_name(work_mode: str) -> float:
        """根据工作模式名称计算隶属度（按照MATLAB代码逻辑）"""
        work_mode = work_mode.lower()

        if any(keyword in work_mode for keyword in ["制导模式", "制导"]):
            return 1.0
        elif any(keyword in work_mode for keyword in ["跟踪模式", "跟踪"]):
            return 0.8  # 按照MATLAB代码，跟踪模式是0.8
        elif any(keyword in work_mode for keyword in ["成像模式", "成像"]):
            return 0.5  # 按照MATLAB代码，成像模式是0.5
        elif any(keyword in work_mode for keyword in ["搜索模式", "搜索", "预警"]):
            return 0.2  # 按照MATLAB代码，搜索模式是0.2
        elif any(keyword in work_mode for keyword in ["静默模式", "静默"]):
            return 0.0
        else:
            return 0.0
    
    @staticmethod
    def _get_mode_membership_by_code(work_mode: int) -> float:
        """根据工作模式编码计算隶属度（按照MATLAB代码逻辑）"""
        mode_mapping = {
            0: 0.0,   # 静默模式 - 无威胁
            1: 0.2,   # 搜索模式 - 按照MATLAB代码是0.2
            2: 0.8,   # 跟踪模式 - 按照MATLAB代码是0.8
            3: 0.5,   # 成像模式 - 按照MATLAB代码是0.5
            4: 1.0    # 制导模式 - 威胁极高
        }
        return mode_mapping.get(work_mode, 0.0)


# 测试函数
if __name__ == "__main__":
    # 测试雷达类型隶属度
    print("=== 雷达类型隶属度测试 ===")
    test_cases = [
        ("火控雷达", "战斗机"),
        ("搜索雷达", "勤务舰船"),
        ("导航雷达", "预警机")
    ]
    
    for radar_type, platform_type in test_cases:
        x1, x2 = TypeMembership.calculate(radar_type, platform_type)
        print(f"{radar_type} + {platform_type}: 雷达={x1:.3f}, 平台={x2:.3f}")
    
    # 测试运动隶属度
    print("\n=== 运动隶属度测试 ===")
    x3, x4, x5 = MovementMembership.calculate(200, 50, 60)  # 200m/s, 50km, 60度
    print(f"速度={x3:.3f}, 距离={x4:.3f}, 方向={x5:.3f}")

    # 测试工作模式隶属度
    print("\n=== 工作模式隶属度测试 ===")
    x6, x7, x8, x9 = WorkModeMembership.calculate(125, 10500, 1.0, "制导")  # 125μs PRT, 10500MHz, 1.0μs脉宽
    print(f"PRF={x6:.3f}, 频率={x7:.3f}, 脉宽={x8:.3f}, 模式={x9:.3f}")
