@echo off
echo Downloading ONNX Runtime 1.7.0 for VS2015...

REM 清理现有版本
if exist "third_party\onnxruntime" (
    echo Removing existing ONNX Runtime...
    rmdir /s /q "third_party\onnxruntime"
)

REM 创建目录
if not exist "third_party" mkdir third_party
cd third_party

REM 下载ONNX Runtime 1.7.0
set VERSION=1.7.0
set URL=https://github.com/microsoft/onnxruntime/releases/download/v%VERSION%/onnxruntime-win-x64-%VERSION%.zip
set ZIP_FILE=onnxruntime-win-x64-%VERSION%.zip

echo Downloading ONNX Runtime %VERSION%...
echo URL: %URL%

powershell -Command "Invoke-WebRequest -Uri '%URL%' -OutFile '%ZIP_FILE%'"

if exist "%ZIP_FILE%" (
    echo Extracting...
    powershell -Command "Expand-Archive -Path '%ZIP_FILE%' -DestinationPath '.'"
    
    REM 重命名目录
    ren "onnxruntime-win-x64-%VERSION%" "onnxruntime"
    
    echo.
    echo ✅ ONNX Runtime 1.7.0 setup completed!
    echo.
    echo Verification:
    if exist "onnxruntime\include\onnxruntime_cxx_api.h" (
        echo ✅ C++ API headers found
    ) else (
        echo ❌ C++ API headers missing
    )
    
    if exist "onnxruntime\lib\onnxruntime.lib" (
        echo ✅ Library files found
    ) else (
        echo ❌ Library files missing
    )
    
    if exist "onnxruntime\lib\onnxruntime.dll" (
        echo ✅ DLL files found
    ) else (
        echo ❌ DLL files missing
    )
    
) else (
    echo ❌ Download failed!
    echo Please check your internet connection.
)

cd ..
echo.
echo Ready to compile with ONNX Runtime 1.7.0!
pause
