"""
智能干扰决策模块
基于威胁评估和雷达参数制定最优干扰策略
"""

from typing import Dict, Optional, Tuple, Any
import time
import random

from .interfaces import IJammingDecisionModule
from .data_structures import (
    ThreatAssessmentData, RadarEmissionData, SimplifiedRadarData, JammingDecisionData,
    FeedbackData, JammingStrategy, CombSpectrumParams, IntermittentSamplingParams,
    BroadbandNoiseParams, SmartNoiseParams, DeceptionParams, NOISE_BANDWIDTH_TABLE
)


class IntelligentDecisionModule(IJammingDecisionModule):
    """
    智能干扰决策模块 - 纯强化学习版本

    基于威胁评估结果和雷达参数，使用强化学习算法制定最优干扰策略
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化智能干扰决策模块（纯强化学习）

        Args:
            config: 配置参数字典
        """
        if config is None:
            config = {}

        self.strategy = JammingStrategy.LEARNING_BASED  # 固定为强化学习策略

        # 决策历史
        self.decision_history = []
        self.feedback_history = []

        # 强化学习参数（可配置）- 增强探索版本
        self.learning_rate = config.get('learning_rate', 0.15)
        self.exploration_rate = config.get('exploration_rate', 0.5)  # 提高初始探索率
        self.discount_factor = config.get('discount_factor', 0.95)
        self.epsilon_decay = config.get('epsilon_decay', 0.9995)  # 更慢的衰减
        self.min_epsilon = config.get('min_epsilon', 0.15)  # 提高最小探索率到15%
        self.verbose = config.get('verbose', False)  # 添加静默模式控制

        # Q表和经验库
        self.q_table = {}
        self.experience_table = {}
        self.state_action_history = []

        # 学习统计
        self.training_episodes = 0
        self.cumulative_reward = 0.0
        self.best_performance = 0.0
        self.learning_curve = []

        # Q表收敛监控
        self.q_table_size_history = []  # Q表大小历史
        self.convergence_window = 100   # 收敛检测窗口
        self.is_converged = False       # 是否已收敛

        # 性能统计
        self.decision_stats = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'average_confidence': 0.0,
            'exploration_decisions': 0,
            'exploitation_decisions': 0
        }

        # 基础干扰类型
        self.base_jamming_types = [
            "梳状谱",
            "间歇采样转发",
            "宽带阻塞噪声",
            "灵巧噪声",
            "拖引"
        ]

        # 组合干扰策略 - 支持双重组合干扰（最多2个）
        self.jamming_combinations = [
            # 单一干扰
            ["梳状谱"],
            ["间歇采样转发"],
            ["宽带阻塞噪声"],
            ["灵巧噪声"],
            ["拖引"],

            # 双重组合 - 所有可能的两两组合
            ["梳状谱", "间歇采样转发"],
            ["梳状谱", "宽带阻塞噪声"],
            ["梳状谱", "灵巧噪声"],
            ["梳状谱", "拖引"],
            ["间歇采样转发", "宽带阻塞噪声"],
            ["间歇采样转发", "灵巧噪声"],
            ["间歇采样转发", "拖引"],
            ["宽带阻塞噪声", "灵巧噪声"],
            ["宽带阻塞噪声", "拖引"],
            ["灵巧噪声", "拖引"]
        ]

        self.power_levels = [0.3, 0.5, 0.7, 0.9, 1.0]  # 离散化的功率等级

        # 干扰类型编号映射 (按照用户要求)
        self.jamming_type_mapping = {
            "梳状谱": 0,
            "间歇采样转发": 1,
            "宽带阻塞噪声": 2,
            "灵巧噪声": 3,
            "拖引": 4
        }

        # 反向映射
        self.jamming_id_mapping = {v: k for k, v in self.jamming_type_mapping.items()}

        # 雷达自适应历史管理
        from .radar_adaptation_history import RadarAdaptationHistory
        self.radar_history = RadarAdaptationHistory(verbose=self.verbose)

        if self.verbose:
            print(f"   智能干扰决策模块初始化完成 - 纯强化学习策略（支持组合干扰）")
            print(f"   学习率: {self.learning_rate}, 探索率: {self.exploration_rate}")
            print(f"   动作空间: {len(self.jamming_combinations)} 种干扰组合 × {len(self.power_levels)} 个强度等级")
            print(f"   组合干扰类型: 单一({len([c for c in self.jamming_combinations if len(c)==1])}) + 双重({len([c for c in self.jamming_combinations if len(c)==2])}) (最多2个干扰)")
    
    def make_decision(self, threat_data: ThreatAssessmentData,
                     radar_data: SimplifiedRadarData,
                     context: Optional[Dict] = None) -> JammingDecisionData:
        """
        制定干扰决策

        Args:
            threat_data: 威胁评估数据
            radar_data: 简化的雷达数据（只包含载频、脉宽、PRF、工作模式、目标距离、目标速度）
            context: 上下文信息

        Returns:
            干扰决策数据
        """
        decision_start_time = time.time()
        
        # 使用强化学习决策方法
        decision = self._reinforcement_learning_decision(threat_data, radar_data, context)
        
        # 记录决策时间
        decision.timestamp = decision_start_time
        
        # 更新统计信息
        self._update_decision_stats(decision)
        
        # 记录决策历史
        decision_record = {
            'threat_data': threat_data,
            'radar_data': radar_data,
            'context': context,
            'decision': decision,
            'timestamp': decision_start_time
        }
        self.decision_history.append(decision_record)
        
        return decision
    
    def _reinforcement_learning_decision(self, threat_data: ThreatAssessmentData,
                                        radar_data: SimplifiedRadarData,
                                        context: Optional[Dict]) -> JammingDecisionData:
        """
        强化学习决策策略

        使用Q-learning算法选择最优干扰动作，考虑雷达自适应历史
        """
        # 获取雷达自适应上下文
        radar_adaptation_context = self.radar_history.get_radar_adaptation_context()

        # 构建状态特征（包含雷达自适应信息）
        state = self._build_state_features_with_adaptation(threat_data, radar_data, radar_adaptation_context)

        # 增强的ε-贪婪策略：好奇心驱动的探索
        base_exploration_prob = self.exploration_rate

        # 检查当前状态是否为新状态
        current_state = self._build_state_features_with_adaptation(
            threat_data, radar_data, radar_adaptation_context
        )

        # 好奇心奖励：如果是新状态，增加探索概率
        if current_state not in self.q_table:
            curiosity_bonus = 0.2  # 新状态额外20%探索概率
            exploration_prob = min(0.8, base_exploration_prob + curiosity_bonus)
            if self.verbose:
                print(f"   发现新状态，好奇心驱动探索: {exploration_prob:.3f}")
        else:
            # 已知状态，检查该状态的动作是否充分探索
            state_actions = self.q_table.get(current_state, {})
            if len(state_actions) < 3:  # 如果该状态的动作数量少于3个
                exploration_prob = min(0.6, base_exploration_prob + 0.1)  # 增加10%探索概率
            else:
                exploration_prob = base_exploration_prob

        should_explore = random.random() < exploration_prob

        if should_explore:
            # 探索：根据威胁等级智能随机选择动作
            action = self._smart_random_action_with_adaptation(threat_data, radar_data, radar_adaptation_context)
            self.decision_stats['exploration_decisions'] += 1
            decision_reason = f"强化学习探索 (ε={self.exploration_rate:.3f})"
        else:
            # 利用：选择最优动作，考虑雷达自适应
            action = self._select_best_action_with_adaptation(state, radar_adaptation_context)
            self.decision_stats['exploitation_decisions'] += 1
            decision_reason = f"强化学习利用 (Q值最优)"

        # 如果有雷达自适应历史，添加反制策略
        if radar_adaptation_context['has_adaptation_history']:
            action = self._apply_countermeasure_strategy(action, radar_adaptation_context)
            decision_reason += " + 雷达自适应反制"

        # 记录决策原因供奖励计算使用
        self.last_decision_reason = decision_reason

        # 将动作转换为决策
        decision = self._action_to_decision(action, threat_data, radar_data, decision_reason)

        # 记录状态-动作对用于学习
        self._record_state_action(state, action, threat_data, radar_data)

        # 更新训练统计
        self.training_episodes += 1

        return decision
    

    
    def update_decision_model(self, feedback: FeedbackData) -> bool:
        """
        更新决策模型

        Args:
            feedback: 反馈数据

        Returns:
            更新是否成功
        """
        try:
            # 记录反馈
            self.feedback_history.append(feedback)

            # 更新强化学习模型
            self._update_q_table(feedback)

            # 调整探索率
            self._adjust_exploration_rate(feedback)

            # 更新决策规则
            self._update_decision_rules(feedback)

            if self.verbose:
                print(f"决策模型更新 - 奖励: {feedback.learning_reward:.3f}")
            return True

        except Exception as e:
            if self.verbose:
                print(f"决策模型更新失败: {e}")
            return False

    def evaluate_and_learn(self, decision_data,
                          radar_response: Dict, threat_data: ThreatAssessmentData) -> float:
        """
        内部干扰效果评估和强化学习更新

        Args:
            decision_data: 干扰决策数据（字典格式）
            radar_response: 雷达响应数据
            threat_data: 威胁评估数据

        Returns:
            计算的奖励值
        """
        # 1. 内部干扰效果评估
        effectiveness = self._evaluate_jamming_effectiveness_internal(
            decision_data, radar_response, threat_data
        )

        # 2. 基于效果计算奖励
        reward = self._calculate_internal_reward(decision_data, effectiveness, radar_response)

        # 2.5. 添加成功奖励机制
        reward = self._apply_success_bonus(reward, effectiveness, decision_data)

        # 3. 创建反馈数据并更新模型
        feedback = FeedbackData(
            decision_quality=effectiveness['overall_effectiveness'],
            execution_quality=0.8,
            system_performance=effectiveness['overall_effectiveness'],
            learning_reward=reward,
            experience_value=reward,
            model_update_needed=True,
            power_adjustment=0.0,
            frequency_adjustment=0.0,
            type_change_suggestion=""
        )

        # 4. 更新决策模型
        self.update_decision_model(feedback)

        return reward

    def _evaluate_jamming_effectiveness_internal(self, decision_data: Dict,
                                               radar_response: Dict, threat_data: ThreatAssessmentData) -> Dict:
        """
        强化学习模块内部的干扰效果评估
        直接基于雷达参数进行评估
        """
        if not decision_data.get('should_jam', False):
            return {
                'overall_effectiveness': 0.0,
                'threat_reduction': 0.0,
                'parameter_match_score': 0.0,
                'resistance_factor': 1.0
            }

        # 获取雷达参数
        radar_params = radar_response.get('radar_params', {})
        peak_power = radar_params.get('peak_power', 1e6)
        frequency = radar_params.get('frequency', 10e9)
        pulse_width = radar_params.get('pulse_width', 1e-6)
        prf = radar_params.get('prf', 1000)
        operating_mode = radar_params.get('operating_mode', '搜索')

        # 计算信号带宽
        signal_bandwidth = 1.0 / pulse_width

        # 获取干扰参数
        jamming_combination = decision_data.get('jamming_params', {}).get('combination', [])
        jamming_power = decision_data.get('jamming_power', 0.0)

        # 1. 分析雷达特征
        radar_characteristics = self._analyze_radar_characteristics_internal(
            peak_power, frequency, pulse_width, prf, signal_bandwidth, operating_mode
        )

        # 2. 计算干扰-雷达匹配度
        match_scores = self._calculate_jamming_radar_match_internal(
            jamming_combination, radar_characteristics
        )

        # 3. 计算雷达抗干扰能力
        resistance_factor = self._calculate_radar_resistance_internal(radar_characteristics)

        # 4. 计算综合效果
        if match_scores:
            avg_match_score = sum(match_scores.values()) / len(match_scores)
        else:
            avg_match_score = 0.0

        # 基础效果 = 匹配度 × 干扰功率 × 抗干扰修正
        base_effectiveness = avg_match_score * jamming_power * (2.0 - resistance_factor)
        overall_effectiveness = min(1.0, max(0.0, base_effectiveness))

        # 5. 计算威胁降低程度
        threat_reduction = self._calculate_threat_reduction_internal(
            overall_effectiveness, operating_mode, jamming_combination,
            peak_power, frequency, threat_data.threat_level
        )

        return {
            'overall_effectiveness': overall_effectiveness,
            'threat_reduction': threat_reduction,
            'parameter_match_score': avg_match_score,
            'resistance_factor': resistance_factor
        }

    def _analyze_radar_characteristics_internal(self, peak_power: float, frequency: float,
                                              pulse_width: float, prt: float,
                                              signal_bandwidth: float, operating_mode: str) -> Dict:
        """内部雷达特征分析 - 使用标准单位格式"""

        # 功率特征
        power_level = 'high' if peak_power > 1e6 else 'medium' if peak_power > 1e5 else 'low'

        # 频率特征 - frequency 现在是MHz
        freq_mhz = frequency
        if freq_mhz > 8000:  # >8GHz
            freq_band = 'X'
        elif freq_mhz > 4000:  # >4GHz
            freq_band = 'C'
        elif freq_mhz > 2000:  # >2GHz
            freq_band = 'S'
        else:
            freq_band = 'L'

        # 脉冲特征 - pulse_width 现在是μs
        pw_us = pulse_width
        if pw_us < 1:
            pulse_type = 'short'
        elif pw_us < 5:
            pulse_type = 'medium'
        else:
            pulse_type = 'long'

        # PRT特征 - prt 现在是μs，计算PRF
        prf_hz = 1e6 / prt  # μs -> Hz
        if prf_hz > 5000:
            prt_type = 'high'
        elif prf_hz > 1000:
            prt_type = 'medium'
        else:
            prt_type = 'low'

        # 带宽特征
        if signal_bandwidth > 10e6:
            bandwidth_type = 'wide'
        elif signal_bandwidth > 1e6:
            bandwidth_type = 'medium'
        else:
            bandwidth_type = 'narrow'

        # 模式优先级
        mode_priority = {
            '制导': 'critical',
            '跟踪': 'high',
            '搜索': 'medium',
            '预警': 'low',
            '成像': 'high'
        }.get(operating_mode, 'medium')

        return {
            'power_level': power_level,
            'freq_band': freq_band,
            'pulse_type': pulse_type,
            'prf_type': prt_type,
            'bandwidth_type': bandwidth_type,
            'mode_priority': mode_priority
        }

    def _calculate_jamming_radar_match_internal(self, jamming_combination: list,
                                              radar_characteristics: Dict) -> Dict:
        """内部干扰-雷达匹配度计算"""

        # 干扰效果矩阵（简化版）
        effectiveness_matrix = {
            '梳状谱': {
                'power_level': {'high': 0.6, 'medium': 0.7, 'low': 0.8},
                'freq_band': {'X': 0.8, 'C': 0.7, 'S': 0.6, 'L': 0.5},
                'pulse_type': {'short': 0.5, 'medium': 0.7, 'long': 0.8},
                'bandwidth_type': {'wide': 0.4, 'medium': 0.7, 'narrow': 0.9},
                'mode_priority': {'critical': 0.8, 'high': 0.7, 'medium': 0.6, 'low': 0.5}
            },
            '间歇采样转发': {
                'power_level': {'high': 0.8, 'medium': 0.7, 'low': 0.6},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.9, 'medium': 0.8, 'long': 0.6},
                'bandwidth_type': {'wide': 0.6, 'medium': 0.8, 'narrow': 0.9},
                'mode_priority': {'critical': 0.9, 'high': 0.8, 'medium': 0.7, 'low': 0.6}
            },
            '宽带阻塞噪声': {
                'power_level': {'high': 0.5, 'medium': 0.6, 'low': 0.8},
                'freq_band': {'X': 0.7, 'C': 0.7, 'S': 0.7, 'L': 0.7},
                'pulse_type': {'short': 0.7, 'medium': 0.7, 'long': 0.7},
                'bandwidth_type': {'wide': 0.9, 'medium': 0.7, 'narrow': 0.5},
                'mode_priority': {'critical': 0.6, 'high': 0.7, 'medium': 0.8, 'low': 0.9}
            },
            '灵巧噪声': {
                'power_level': {'high': 0.7, 'medium': 0.8, 'low': 0.9},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.8, 'medium': 0.8, 'long': 0.7},
                'bandwidth_type': {'wide': 0.7, 'medium': 0.8, 'narrow': 0.9},
                'mode_priority': {'critical': 0.9, 'high': 0.8, 'medium': 0.7, 'low': 0.6}
            },
            '拖引': {
                'power_level': {'high': 0.9, 'medium': 0.8, 'low': 0.7},
                'freq_band': {'X': 0.9, 'C': 0.8, 'S': 0.7, 'L': 0.6},
                'pulse_type': {'short': 0.8, 'medium': 0.9, 'long': 0.8},
                'bandwidth_type': {'wide': 0.8, 'medium': 0.8, 'narrow': 0.8},
                'mode_priority': {'critical': 0.95, 'high': 0.9, 'medium': 0.8, 'low': 0.7}
            }
        }

        match_scores = {}
        for jamming_type in jamming_combination:
            if jamming_type in effectiveness_matrix:
                factors = effectiveness_matrix[jamming_type]
                match_score = 1.0

                for char_type, char_value in radar_characteristics.items():
                    if char_type in factors and char_value in factors[char_type]:
                        match_score *= factors[char_type][char_value]

                match_scores[jamming_type] = match_score
            else:
                match_scores[jamming_type] = 0.5

        return match_scores

    def _calculate_radar_resistance_internal(self, radar_characteristics: Dict) -> float:
        """内部雷达抗干扰能力计算"""

        # 抗干扰因子
        power_resistance = {
            'high': 1.2, 'medium': 1.0, 'low': 0.8
        }.get(radar_characteristics['power_level'], 1.0)

        frequency_resistance = {
            'X': 1.1, 'C': 1.0, 'S': 0.9, 'L': 0.8
        }.get(radar_characteristics['freq_band'], 1.0)

        pulse_resistance = {
            'short': 1.3, 'medium': 1.0, 'long': 0.7
        }.get(radar_characteristics['pulse_type'], 1.0)

        bandwidth_resistance = {
            'wide': 1.4, 'medium': 1.0, 'narrow': 0.6
        }.get(radar_characteristics['bandwidth_type'], 1.0)

        # 加权平均
        resistance_factor = (
            power_resistance * 0.3 +
            frequency_resistance * 0.2 +
            pulse_resistance * 0.2 +
            bandwidth_resistance * 0.3
        )

        return resistance_factor

    def _calculate_threat_reduction_internal(self, effectiveness_score: float, operating_mode: str,
                                           jamming_combination: list, peak_power: float,
                                           frequency: float, threat_level: int) -> float:
        """内部威胁降低程度计算"""

        # 基础威胁降低
        base_threat_reduction = effectiveness_score

        # 模式系数
        mode_coefficients = {
            '制导': 1.0, '跟踪': 0.9, '搜索': 0.7, '预警': 0.6, '成像': 0.8
        }
        mode_coefficient = mode_coefficients.get(operating_mode, 0.7)

        # 雷达威胁程度
        power_threat = min(1.5, peak_power / 1e6)
        freq_threat = min(1.2, frequency / 10e9)
        radar_threat_level = (power_threat + freq_threat) / 2.0

        # 干扰类型威胁降低贡献
        jamming_factors = {
            '梳状谱': 0.6, '间歇采样转发': 0.8, '宽带阻塞噪声': 0.5,
            '灵巧噪声': 0.9, '拖引': 0.95
        }

        jamming_factor = 0.0
        for jamming_type in jamming_combination:
            jamming_factor += jamming_factors.get(jamming_type, 0.5)

        if jamming_combination:
            jamming_factor = jamming_factor / len(jamming_combination)

        # 综合威胁降低
        threat_reduction = (
            base_threat_reduction *
            mode_coefficient *
            radar_threat_level *
            (0.5 + 0.5 * jamming_factor)
        )

        return min(1.0, max(0.0, threat_reduction))

    def _calculate_internal_reward(self, decision_data,
                                 effectiveness: Dict, radar_response: Dict) -> float:
        """内部奖励计算"""

        # 处理字典和对象两种类型
        should_jam = decision_data.get('should_jam', False) if isinstance(decision_data, dict) else decision_data.should_jam

        if not should_jam:
            return 0.6  # 提高不干扰的基础奖励

        # 基于效果的奖励（增加权重）
        base_reward = effectiveness['overall_effectiveness'] * 1.1

        # 威胁降低奖励（增加权重）
        threat_reward = effectiveness['threat_reduction'] * 0.8

        # 参数匹配奖励（增加权重）
        match_reward = effectiveness['parameter_match_score'] * 0.3

        # 雷达自适应惩罚（减少惩罚）
        adaptation_penalty = 0.0
        if radar_response.get('adapted', False):
            adaptation_penalty = 0.05  # 减少惩罚

        # 组合干扰奖励（新增）
        combination_bonus = 0.0
        if hasattr(decision_data, 'jamming_params') and decision_data.jamming_params:
            combination = decision_data.jamming_params.get('combination', [])
            if len(combination) >= 2:
                combination_bonus = 0.1 * len(combination)  # 组合干扰额外奖励

        # 综合奖励
        total_reward = base_reward + threat_reward + match_reward + combination_bonus - adaptation_penalty

        return max(0.0, min(1.0, total_reward))

    def _apply_success_bonus(self, base_reward: float, effectiveness: Dict, decision_data: Dict) -> float:
        """应用成功奖励机制"""
        bonus = 0.0

        # 高效果奖励
        if effectiveness['overall_effectiveness'] > 0.8:
            bonus += 0.1  # 高效果额外奖励
        elif effectiveness['overall_effectiveness'] > 0.6:
            bonus += 0.05  # 中等效果奖励

        # 威胁降低奖励
        if effectiveness['threat_reduction'] > 0.7:
            bonus += 0.08

        # 连续成功奖励
        if hasattr(self, 'recent_rewards'):
            if len(self.recent_rewards) >= 3 and all(r > 0.6 for r in self.recent_rewards[-3:]):
                bonus += 0.05  # 连续成功奖励
        else:
            self.recent_rewards = []

        # 记录最近奖励
        self.recent_rewards.append(base_reward)
        if len(self.recent_rewards) > 10:
            self.recent_rewards.pop(0)

        # 组合干扰成功奖励
        if decision_data.get('should_jam', False):
            combination = decision_data.get('jamming_params', {}).get('combination', [])
            if len(combination) >= 2 and effectiveness['overall_effectiveness'] > 0.5:
                bonus += 0.03 * len(combination)  # 成功组合干扰奖励

        return min(1.0, base_reward + bonus)

    def get_decision_confidence(self) -> float:
        """
        获取决策置信度
        
        Returns:
            置信度值 [0,1]
        """
        if not self.decision_history:
            return 0.5
        
        # 基于最近决策的平均置信度
        recent_decisions = self.decision_history[-10:]
        confidences = [d['decision'].confidence for d in recent_decisions]
        
        return sum(confidences) / len(confidences)
    

    
    def _generate_jamming_params(self, jamming_type: str, radar_data: SimplifiedRadarData,
                               threat_data: ThreatAssessmentData) -> Tuple[Dict, Optional[object]]:
        """生成具体的干扰参数"""
        base_params = {
            'target_frequency': radar_data.frequency,
            'bandwidth': 1e6,
            'modulation': 'noise'
        }

        specific_params = None

        if jamming_type == "梳状谱":
            specific_params = self._generate_comb_spectrum_params(radar_data, threat_data)
        elif jamming_type == "间歇采样转发":
            specific_params = self._generate_intermittent_sampling_params(radar_data, threat_data)
        elif jamming_type == "宽带阻塞噪声":
            specific_params = self._generate_broadband_noise_params(radar_data, threat_data)
        elif jamming_type == "灵巧噪声":
            specific_params = self._generate_smart_noise_params(radar_data, threat_data)
        elif jamming_type == "拖引":
            specific_params = self._generate_deception_params(radar_data, threat_data)

        return base_params, specific_params

    def _generate_comb_spectrum_params(self, radar_data: SimplifiedRadarData,
                                     threat_data: ThreatAssessmentData) -> CombSpectrumParams:
        """生成梳状谱参数"""
        # 根据威胁等级调整梳状谱数量
        spectrum_count = min(8, max(1, 6 - threat_data.threat_level))

        # 基于雷达PRT调整频偏 - 使用标准单位μs
        prt_us = radar_data.prf  # 现在prf字段存储的是PRT(μs)
        prf_khz = 1000.0 / prt_us  # 计算实际PRF(kHz)
        frequency_offset = prf_khz * 0.1  # 10% PRF作为基础频偏

        # 基于脉宽调整闪烁周期 - 使用标准单位μs
        pulse_width_us = radar_data.pulse_width  # 现在pulse_width字段存储的是μs
        flicker_period = pulse_width_us * 2.0
        flicker_hold_time = pulse_width_us * 0.8

        return CombSpectrumParams(
            spectrum_count=spectrum_count,
            frequency_offset_1=frequency_offset,
            flicker_period_1=flicker_period,
            flicker_hold_time_1=flicker_hold_time
        )

    def _generate_intermittent_sampling_params(self, radar_data: SimplifiedRadarData,
                                             threat_data: ThreatAssessmentData) -> IntermittentSamplingParams:
        """生成间歇采样转发参数"""
        # 基于PRT调整重复间隔 - 使用标准单位μs
        prt_us = radar_data.prf  # 现在prf字段存储的是PRT(μs)
        repeat_interval = prt_us * 0.8

        # 基于脉宽调整采样参数 - 使用标准单位μs
        pulse_width_us = radar_data.pulse_width  # 现在pulse_width字段存储的是μs
        sampling_period = pulse_width_us * 1.5
        sampling_width = pulse_width_us * 0.3

        # 基于目标距离调整覆盖范围
        coverage_range = radar_data.target_range * 1000 * 1.2  # 120%目标距离

        return IntermittentSamplingParams(
            repeat_interval=repeat_interval,
            sampling_switch=1,
            sampling_period=sampling_period,
            sampling_width=sampling_width,
            coverage_range=coverage_range,
            pulse_sampling_length=pulse_width_us
        )

    def _generate_broadband_noise_params(self, radar_data: SimplifiedRadarData,
                                       threat_data: ThreatAssessmentData) -> BroadbandNoiseParams:
        """生成宽带阻塞噪声参数"""
        # 根据威胁等级选择带宽
        if threat_data.threat_level <= 2:  # 高威胁
            bandwidth_selection = 15  # 500 MHz
        elif threat_data.threat_level <= 3:  # 中威胁
            bandwidth_selection = 11  # 100 MHz
        else:  # 低威胁
            bandwidth_selection = 8   # 10 MHz

        return BroadbandNoiseParams(bandwidth_selection=bandwidth_selection)

    def _generate_smart_noise_params(self, radar_data: SimplifiedRadarData,
                                   threat_data: ThreatAssessmentData) -> SmartNoiseParams:
        """生成灵巧噪声参数"""
        # 根据雷达工作模式选择噪声源
        if radar_data.operating_mode == 1:  # 搜索
            noise_source = 2  # 多普勒闪烁
            doppler_flicker_mode = 2  # 随机闪烁
        elif radar_data.operating_mode == 2:  # 跟踪
            noise_source = 3  # 多普勒噪声
            doppler_flicker_mode = 1  # 固定闪烁
        else:
            noise_source = 1  # 高斯噪声
            doppler_flicker_mode = 1

        # 基于PRT调整闪烁参数 - 使用标准单位μs
        prt_us = radar_data.prf  # 现在prf字段存储的是PRT(μs)
        flicker_hold_time = prt_us * 0.3
        flicker_disappear_time = prt_us * 0.1

        return SmartNoiseParams(
            bandwidth_selection=7,  # 1 MHz
            noise_source=noise_source,
            doppler_flicker_mode=doppler_flicker_mode,
            flicker_hold_time=flicker_hold_time,
            flicker_disappear_time=flicker_disappear_time,
            doppler_noise_bandwidth=100.0,
            doppler_noise_jump_period=50.0
        )

    def _generate_deception_params(self, radar_data: SimplifiedRadarData,
                                 threat_data: ThreatAssessmentData) -> DeceptionParams:
        """生成拖引参数"""
        # 基于目标速度调整拖引速度
        base_velocity = radar_data.target_velocity
        velocity_drag_speed = base_velocity * 1.2
        velocity_drag_acceleration = base_velocity * 0.1

        # 基于目标距离调整距离拖引
        base_range_rate = 100.0  # 默认距离变化率 m/s
        range_drag_speed = base_range_rate
        range_drag_acceleration = base_range_rate * 0.1

        # 基于威胁等级调整时间参数
        if threat_data.threat_level <= 2:  # 高威胁，快速拖引
            capture_time = 0.5
            drag_time = 2.0
            hold_time = 1.0
            disappear_time = 0.5
        else:  # 低威胁，缓慢拖引
            capture_time = 1.0
            drag_time = 4.0
            hold_time = 2.0
            disappear_time = 1.0

        return DeceptionParams(
            velocity_drag_speed=velocity_drag_speed,
            velocity_drag_acceleration=velocity_drag_acceleration,
            range_drag_speed=range_drag_speed,
            range_drag_acceleration=range_drag_acceleration,
            capture_time=capture_time,
            drag_time=drag_time,
            hold_time=hold_time,
            disappear_time=disappear_time
        )
    

    
    def _build_state_features(self, threat_data: ThreatAssessmentData,
                            radar_data: SimplifiedRadarData) -> Tuple:
        """构建状态特征 - 扩展版（增加状态空间丰富度）"""

        # 1. 威胁等级（1-5）- 主要特征
        threat_level = threat_data.threat_level

        # 2. 频率分段（扩展为4档）- 使用标准单位MHz
        frequency = radar_data.frequency
        if frequency < 2000:      # L波段及以下
            freq_band = 1
        elif frequency < 6000:    # S/C波段
            freq_band = 2
        elif frequency < 12000:   # X波段
            freq_band = 3
        else:                     # Ku波段及以上
            freq_band = 4

        # 3. 工作模式（保持5档）
        mode_code = radar_data.operating_mode
        work_mode = min(5, max(1, mode_code + 1))  # 0-4 -> 1-5

        # 4. 脉宽分段（新增，3档）
        pulse_width = radar_data.pulse_width  # 已经是μs单位，无需转换
        if pulse_width < 2.0:     # 短脉宽
            pw_band = 1
        elif pulse_width < 10.0:  # 中脉宽
            pw_band = 2
        else:                     # 长脉宽
            pw_band = 3

        # 5. 距离分段（新增，4档）
        distance = getattr(radar_data, 'target_range', 100.0)  # 使用正确的字段名
        if distance < 50:         # 近距离
            dist_band = 1
        elif distance < 100:      # 中近距离
            dist_band = 2
        elif distance < 200:      # 中远距离
            dist_band = 3
        else:                     # 远距离
            dist_band = 4

        # 6. 速度分段（新增，3档）
        speed = getattr(radar_data, 'target_velocity', 100.0)  # 使用正确的字段名
        if speed < 50:            # 低速
            speed_band = 1
        elif speed < 200:         # 中速
            speed_band = 2
        else:                     # 高速
            speed_band = 3

        # 状态空间：5 × 4 × 5 × 3 × 4 × 3 = 3600个状态
        return (threat_level, freq_band, work_mode, pw_band, dist_band, speed_band)

    def _build_state_features_with_adaptation(self, threat_data: ThreatAssessmentData,
                                            radar_data: SimplifiedRadarData,
                                            adaptation_context: Dict) -> Tuple:
        """构建包含雷达自适应信息的状态特征 - 扩展版"""
        base_features = self._build_state_features(threat_data, radar_data)

        # 7. 自适应类型（新增，4档）
        if not adaptation_context['has_adaptation_history']:
            adapt_type = 1  # 无自适应
        else:
            # 根据自适应类型分类
            adapt_history = adaptation_context.get('adaptation_history', [])
            if any('frequency' in str(h).lower() for h in adapt_history):
                adapt_type = 2  # 频率自适应
            elif any('power' in str(h).lower() for h in adapt_history):
                adapt_type = 3  # 功率自适应
            else:
                adapt_type = 4  # 其他自适应

        # 8. 自适应强度（新增，3档）
        adapt_strength = adaptation_context.get('adaptation_frequency', 0)
        if adapt_strength == 0:
            adapt_level = 1  # 无自适应
        elif adapt_strength < 3:
            adapt_level = 2  # 轻度自适应
        else:
            adapt_level = 3  # 重度自适应

        # 最终状态：5 × 4 × 5 × 3 × 4 × 3 × 4 × 3 = 43200个状态
        extended_features = base_features + (adapt_type, adapt_level)

        return extended_features
    
    def _random_action(self) -> Dict:
        """随机选择动作（探索）- 支持组合干扰"""
        # 80%的概率选择干扰，20%的概率不干扰
        should_jam = random.random() < 0.8

        if should_jam:
            jamming_combination = random.choice(self.jamming_combinations)
            jamming_type = " + ".join(jamming_combination)
            jamming_power = random.choice(self.power_levels)
        else:
            jamming_combination = []
            jamming_type = None
            jamming_power = 0.0

        return {
            'should_jam': should_jam,
            'jamming_combination': jamming_combination,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power
        }

    def _smart_random_action_with_adaptation(self, threat_data: ThreatAssessmentData, radar_data: Dict, adaptation_context: Dict) -> Dict:
        """智能随机动作选择 - 根据威胁等级和工作模式调整探索策略"""
        threat_level = threat_data.threat_level

        # 获取雷达工作模式
        work_mode = getattr(radar_data, 'work_mode', None)

        # 根据威胁等级和工作模式调整"不干扰"的概率
        if threat_level <= 2:  # 高威胁：95%概率干扰
            no_jam_probability = 0.05
        elif threat_level == 3:  # 中等威胁：90%概率干扰
            no_jam_probability = 0.10
        elif threat_level == 4:  # 低威胁：80%概率干扰
            no_jam_probability = 0.20
        else:  # 威胁等级5：极低威胁
            # 特殊处理：静默模式的极低威胁应该主要不干扰
            if work_mode == 0:  # 静默模式
                no_jam_probability = 0.85  # 85%概率不干扰
            else:
                no_jam_probability = 0.60  # 其他模式60%概率不干扰

        should_jam = random.random() > no_jam_probability

        if should_jam:
            # 考虑雷达自适应的干扰选择
            if adaptation_context['has_adaptation_history']:
                recommended_countermeasures = adaptation_context.get('recommended_countermeasures', [])
                if recommended_countermeasures and random.random() < 0.7:
                    countermeasure = random.choice(recommended_countermeasures)
                    return self._countermeasure_to_action(countermeasure)

            # 根据威胁等级选择合适的干扰组合
            if threat_level <= 2:  # 高威胁：允许复杂组合
                jamming_combination = random.choice(self.jamming_combinations)
            elif threat_level == 3:  # 中等威胁：允许双重组合
                # 过滤出单一和双重组合
                suitable_combinations = [combo for combo in self.jamming_combinations if len(combo) <= 2]
                jamming_combination = random.choice(suitable_combinations)
            elif threat_level == 4:  # 低威胁：主要使用单一干扰
                # 80%概率使用单一干扰，20%概率使用双重组合
                if random.random() < 0.8:
                    single_combinations = [combo for combo in self.jamming_combinations if len(combo) == 1]
                    jamming_combination = random.choice(single_combinations)
                else:
                    double_combinations = [combo for combo in self.jamming_combinations if len(combo) == 2]
                    jamming_combination = random.choice(double_combinations) if double_combinations else random.choice(self.jamming_combinations)
            else:  # 威胁等级5：如果要干扰，只使用最简单的
                single_combinations = [combo for combo in self.jamming_combinations if len(combo) == 1]
                jamming_combination = random.choice(single_combinations) if single_combinations else ["灵巧噪声"]

            jamming_type = " + ".join(jamming_combination)
            jamming_power = random.choice(self.power_levels)
        else:
            jamming_combination = []
            jamming_type = None
            jamming_power = 0.0

        return {
            'should_jam': should_jam,
            'jamming_combination': jamming_combination,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power
        }

    def _random_action_with_adaptation(self, adaptation_context: Dict) -> Dict:
        """考虑雷达自适应的随机动作选择（保留原方法用于兼容性）"""
        if not adaptation_context['has_adaptation_history']:
            return self._random_action()

        # 基于推荐的反制措施调整随机选择
        recommended_countermeasures = adaptation_context.get('recommended_countermeasures', [])

        if recommended_countermeasures:
            # 70%概率使用推荐的反制策略
            if random.random() < 0.7:
                countermeasure = random.choice(recommended_countermeasures)
                return self._countermeasure_to_action(countermeasure)

        # 否则使用普通随机选择
        return self._random_action()

    def _select_best_action_with_adaptation(self, state: Tuple, adaptation_context: Dict) -> Dict:
        """考虑雷达自适应的最优动作选择"""
        # 首先尝试标准的最优动作选择
        base_action = self._select_best_action(state)

        if not adaptation_context['has_adaptation_history']:
            return base_action

        # 检查是否需要根据雷达自适应调整策略
        predicted_adaptations = adaptation_context.get('predicted_adaptations', [])

        for prediction in predicted_adaptations:
            if prediction['probability'] > 0.6:  # 高概率预测
                # 根据预测调整动作
                adjusted_action = self._adjust_action_for_prediction(base_action, prediction)
                if adjusted_action != base_action:
                    if self.verbose:
                        print(f"基于雷达自适应预测调整策略: {prediction['type']}")
                    return adjusted_action

        return base_action

    def _apply_countermeasure_strategy(self, action: Dict, adaptation_context: Dict) -> Dict:
        """应用反制策略"""
        recommended_countermeasures = adaptation_context.get('recommended_countermeasures', [])

        if not recommended_countermeasures:
            return action

        # 选择优先级最高的反制措施
        high_priority_countermeasures = [cm for cm in recommended_countermeasures if cm['priority'] == 'high']

        if high_priority_countermeasures:
            countermeasure = max(high_priority_countermeasures, key=lambda x: x['confidence'])
            return self._countermeasure_to_action(countermeasure)

        return action

    def _countermeasure_to_action(self, countermeasure: Dict) -> Dict:
        """将反制措施转换为动作"""
        strategy = countermeasure['strategy']

        if strategy == 'preemptive_wideband_coverage':
            # 预防性宽带覆盖
            return {
                'should_jam': True,
                'jamming_combination': ['宽带阻塞噪声', '灵巧噪声'],
                'jamming_type': '宽带阻塞噪声 + 灵巧噪声',
                'jamming_power': 0.9
            }
        elif strategy == 'multi_point_tracking_preparation':
            # 多频点跟踪准备
            return {
                'should_jam': True,
                'jamming_combination': ['梳状谱', '间歇采样转发'],
                'jamming_type': '梳状谱 + 间歇采样转发',
                'jamming_power': 0.7
            }
        elif strategy == 'fast_frequency_tracking':
            # 快速频率跟踪
            return {
                'should_jam': True,
                'jamming_combination': ['灵巧噪声'],
                'jamming_type': '灵巧噪声',
                'jamming_power': 0.8
            }
        elif strategy == 'adaptive_power_jamming':
            # 自适应功率干扰
            return {
                'should_jam': True,
                'jamming_combination': ['宽带阻塞噪声'],
                'jamming_type': '宽带阻塞噪声',
                'jamming_power': 1.0
            }
        elif strategy == 'multi_mode_jamming':
            # 多模式干扰
            return {
                'should_jam': True,
                'jamming_combination': ['梳状谱', '间歇采样转发', '灵巧噪声'],
                'jamming_type': '梳状谱 + 间歇采样转发 + 灵巧噪声',
                'jamming_power': 0.8
            }
        else:
            # 默认动作
            return self._random_action()

    def _adjust_action_for_prediction(self, base_action: Dict, prediction: Dict) -> Dict:
        """根据雷达自适应预测调整动作"""
        pred_type = prediction['type']

        if 'frequency_agility' in pred_type:
            if 'large' in pred_type:
                # 预期大幅频率跳变，使用宽带覆盖
                return {
                    'should_jam': True,
                    'jamming_combination': ['宽带阻塞噪声', '灵巧噪声'],
                    'jamming_type': '宽带阻塞噪声 + 灵巧噪声',
                    'jamming_power': 0.9
                }
            elif 'medium' in pred_type or 'escalated' in pred_type:
                # 预期中等频率跳变，使用多频点跟踪
                return {
                    'should_jam': True,
                    'jamming_combination': ['梳状谱', '间歇采样转发'],
                    'jamming_type': '梳状谱 + 间歇采样转发',
                    'jamming_power': 0.7
                }
        elif pred_type == 'continued_power_control':
            # 预期功率控制，增强干扰强度
            enhanced_action = base_action.copy()
            enhanced_action['jamming_power'] = min(1.0, enhanced_action.get('jamming_power', 0.5) * 1.3)
            return enhanced_action
        elif pred_type == 'mode_switching':
            # 预期模式切换，使用多模式干扰
            return {
                'should_jam': True,
                'jamming_combination': ['梳状谱', '间歇采样转发', '灵巧噪声'],
                'jamming_type': '梳状谱 + 间歇采样转发 + 灵巧噪声',
                'jamming_power': 0.8
            }

        return base_action

    def record_radar_adaptation(self, original_params, adapted_params, jamming_types, snr_degradation, adaptation_info):
        """记录雷达自适应变化"""
        self.radar_history.record_adaptation(
            original_params, adapted_params, jamming_types, snr_degradation, adaptation_info
        )

    def analyze_radar_state_changes(self, radar_observable_state):
        """分析雷达状态变化并推断雷达行为（智能决策模块的核心分析功能）"""

        # 计算各参数的变化量
        freq_change_hz = radar_observable_state['current_frequency'] - radar_observable_state['original_frequency']
        freq_change_mhz = freq_change_hz / 1e6  # 转换为MHz

        power_change_w = radar_observable_state['current_power'] - radar_observable_state['original_power']
        import numpy as np
        power_change_db = 10 * np.log10(radar_observable_state['current_power'] / radar_observable_state['original_power']) if radar_observable_state['original_power'] > 0 else 0

        prf_change = radar_observable_state['current_prf'] - radar_observable_state['original_prf']
        prf_change_percent = abs(prf_change) / radar_observable_state['original_prf'] * 100 if radar_observable_state['original_prf'] > 0 else 0

        pulse_width_change = radar_observable_state['current_pulse_width'] - radar_observable_state['original_pulse_width']
        pulse_width_change_percent = abs(pulse_width_change) / radar_observable_state['original_pulse_width'] * 100 if radar_observable_state['original_pulse_width'] > 0 else 0

        # 基于载频变化推断频率捷变行为
        frequency_agility_analysis = self._analyze_frequency_agility(freq_change_mhz)

        # 基于功率变化推断功率调整策略
        power_adjustment_analysis = self._analyze_power_adjustment(power_change_db)

        # 基于PRF变化推断波形调整
        prf_adjustment_analysis = self._analyze_prf_adjustment(prf_change_percent)

        # 基于脉宽变化推断脉冲压缩策略
        pulse_adjustment_analysis = self._analyze_pulse_adjustment(pulse_width_change_percent)

        # 综合分析雷达对抗策略
        countermeasure_strategy = self._infer_countermeasure_strategy(
            frequency_agility_analysis, power_adjustment_analysis,
            prf_adjustment_analysis, pulse_adjustment_analysis
        )

        analysis_result = {
            'frequency_change_mhz': freq_change_mhz,
            'power_change_db': power_change_db,
            'prf_change_percent': prf_change_percent,
            'pulse_width_change_percent': pulse_width_change_percent,
            'frequency_agility': frequency_agility_analysis,
            'power_adjustment': power_adjustment_analysis,
            'prf_adjustment': prf_adjustment_analysis,
            'pulse_adjustment': pulse_adjustment_analysis,
            'countermeasure_strategy': countermeasure_strategy,
            'analysis_confidence': countermeasure_strategy['confidence']
        }

        print(f"   智能决策模块分析结果:")
        print(f"     载频变化: {freq_change_mhz:.1f} MHz → {frequency_agility_analysis['type']}")
        print(f"     功率变化: {power_change_db:.1f} dB → {power_adjustment_analysis['type']}")
        print(f"     PRF变化: {prf_change_percent:.1f}% → {prf_adjustment_analysis['type']}")
        print(f"     推断对抗策略: {countermeasure_strategy['strategy']}")
        print(f"     分析置信度: {countermeasure_strategy['confidence']:.2f}")

        return analysis_result

    def _analyze_frequency_agility(self, freq_change_mhz):
        """分析频率捷变类型"""
        abs_change = abs(freq_change_mhz)

        if abs_change > 500:
            return {'type': '大幅频率跳变', 'level': 'high', 'confidence': 0.95}
        elif abs_change > 200:
            return {'type': '中等频率捷变', 'level': 'medium', 'confidence': 0.90}
        elif abs_change > 50:
            return {'type': '小幅频率调整', 'level': 'low', 'confidence': 0.85}
        elif abs_change > 10:
            return {'type': '微调频率', 'level': 'minimal', 'confidence': 0.75}
        else:
            return {'type': '无频率变化', 'level': 'none', 'confidence': 0.80}

    def _analyze_power_adjustment(self, power_change_db):
        """分析功率调整策略"""
        if power_change_db > 10:
            return {'type': '大幅功率提升', 'level': 'high', 'confidence': 0.90}
        elif power_change_db > 5:
            return {'type': '中等功率提升', 'level': 'medium', 'confidence': 0.85}
        elif power_change_db > 2:
            return {'type': '小幅功率提升', 'level': 'low', 'confidence': 0.80}
        elif power_change_db < -5:
            return {'type': '功率降低', 'level': 'reduction', 'confidence': 0.75}
        else:
            return {'type': '功率无明显变化', 'level': 'none', 'confidence': 0.70}

    def _analyze_prf_adjustment(self, prf_change_percent):
        """分析PRF调整策略"""
        if prf_change_percent > 50:
            return {'type': '大幅PRF调整', 'level': 'high', 'confidence': 0.85}
        elif prf_change_percent > 20:
            return {'type': '中等PRF调整', 'level': 'medium', 'confidence': 0.80}
        elif prf_change_percent > 10:
            return {'type': '小幅PRF调整', 'level': 'low', 'confidence': 0.75}
        else:
            return {'type': 'PRF无明显变化', 'level': 'none', 'confidence': 0.70}

    def _analyze_pulse_adjustment(self, pulse_change_percent):
        """分析脉宽调整策略"""
        if pulse_change_percent > 50:
            return {'type': '大幅脉宽调整', 'level': 'high', 'confidence': 0.80}
        elif pulse_change_percent > 20:
            return {'type': '中等脉宽调整', 'level': 'medium', 'confidence': 0.75}
        elif pulse_change_percent > 10:
            return {'type': '小幅脉宽调整', 'level': 'low', 'confidence': 0.70}
        else:
            return {'type': '脉宽无明显变化', 'level': 'none', 'confidence': 0.65}

    def _infer_countermeasure_strategy(self, freq_analysis, power_analysis, prf_analysis, pulse_analysis):
        """推断雷达综合对抗策略"""

        # 计算综合对抗强度
        intensity_score = 0
        if freq_analysis['level'] == 'high': intensity_score += 4
        elif freq_analysis['level'] == 'medium': intensity_score += 3
        elif freq_analysis['level'] == 'low': intensity_score += 2
        elif freq_analysis['level'] == 'minimal': intensity_score += 1

        if power_analysis['level'] == 'high': intensity_score += 3
        elif power_analysis['level'] == 'medium': intensity_score += 2
        elif power_analysis['level'] == 'low': intensity_score += 1

        if prf_analysis['level'] in ['high', 'medium']: intensity_score += 2
        elif prf_analysis['level'] == 'low': intensity_score += 1

        if pulse_analysis['level'] in ['high', 'medium']: intensity_score += 1

        # 推断对抗策略
        if intensity_score >= 8:
            strategy = '全面抗干扰模式'
            confidence = 0.95
        elif intensity_score >= 6:
            strategy = '综合对抗模式'
            confidence = 0.90
        elif intensity_score >= 4:
            strategy = '主动频率对抗'
            confidence = 0.85
        elif intensity_score >= 2:
            strategy = '轻微参数调整'
            confidence = 0.75
        else:
            strategy = '无明显对抗措施'
            confidence = 0.70

        return {
            'strategy': strategy,
            'intensity_score': intensity_score,
            'confidence': confidence
        }

    def advance_episode(self):
        """推进到下一轮次"""
        self.radar_history.advance_episode()

    def get_radar_adaptation_context(self):
        """获取雷达自适应上下文，用于显示上一轮的雷达自适应历史"""
        return self.radar_history.get_radar_adaptation_context()
    
    def _select_best_action(self, state: Tuple) -> Dict:
        """选择最优动作（利用）- 支持组合干扰"""
        # 从Q表中选择Q值最高的动作
        if state in self.q_table and self.q_table[state]:
            best_action_key = max(self.q_table[state], key=self.q_table[state].get)
            return self._key_to_action(best_action_key)
        else:
            # 如果没有经验，使用启发式默认动作
            return self._get_heuristic_action(state)
    
    def _action_to_decision(self, action: Dict, threat_data: ThreatAssessmentData,
                          radar_data: SimplifiedRadarData, decision_reason: str) -> JammingDecisionData:
        """将强化学习动作转换为决策 - 支持组合干扰"""
        # 计算决策置信度
        confidence = self._calculate_rl_confidence(action, threat_data)

        # 计算优先级
        priority = self._calculate_rl_priority(action, threat_data)

        # 计算持续时间
        duration = self._calculate_rl_duration(action, threat_data)

        # 获取组合干扰信息
        jamming_combination = action.get('jamming_combination', [])

        # 生成组合干扰参数
        jamming_params, combined_specific_params = self._generate_combination_jamming_params(
            jamming_combination, radar_data, threat_data
        )

        # 添加组合信息到参数中
        jamming_params['combination'] = jamming_combination
        jamming_params['combination_size'] = len(jamming_combination)

        # 创建决策数据
        decision = JammingDecisionData(
            should_jam=action['should_jam'],
            jamming_type=action['jamming_type'],
            jamming_power=action['jamming_power'],
            jamming_frequency=radar_data.frequency,
            jamming_params=jamming_params,
            priority=priority,
            duration=duration,
            decision_reason=decision_reason,
            confidence=confidence
        )

        # 设置组合干扰的具体参数
        if jamming_combination:
            self._set_combination_specific_params(decision, jamming_combination, combined_specific_params)

        # 注意：标准输出格式由主系统统一处理，这里不再直接打印

        return decision
    
    def _record_state_action(self, state: Tuple, action: Dict,
                           threat_data: ThreatAssessmentData, radar_data: SimplifiedRadarData):
        """记录状态-动作对用于强化学习"""
        # 记录到经验表
        if state not in self.experience_table:
            self.experience_table[state] = {
                'actions': [],
                'rewards': [],
                'q_values': {},
                'visit_count': 0,
                'best_action': action
            }

        self.experience_table[state]['actions'].append(action)
        self.experience_table[state]['visit_count'] += 1

        # 记录到状态-动作历史
        self.state_action_history.append({
            'state': state,
            'action': action,
            'threat_data': threat_data,
            'radar_data': radar_data,
            'timestamp': time.time()
        })
    
    def _update_q_table(self, feedback: FeedbackData):
        """更新Q表（改进的Q-learning算法）"""
        reward = feedback.learning_reward
        self.cumulative_reward += reward

        # 更新最近的状态-动作对
        if self.state_action_history:
            last_sa = self.state_action_history[-1]
            state = last_sa['state']
            action_key = self._action_to_key(last_sa['action'])

            # 初始化Q值
            if state not in self.q_table:
                self.q_table[state] = {}
            if action_key not in self.q_table[state]:
                self.q_table[state][action_key] = 0.0

            # Q-learning更新公式: Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
            current_q = self.q_table[state][action_key]

            # 如果有下一个状态，使用完整的Q-learning公式
            if len(self.state_action_history) > 1:
                next_state = self.state_action_history[-2]['state']  # 前一个状态作为下一状态
                max_next_q = max(self.q_table.get(next_state, {}).values()) if next_state in self.q_table else 0
                target_q = reward + self.discount_factor * max_next_q
            else:
                # 没有下一状态，直接使用奖励
                target_q = reward

            new_q = current_q + self.learning_rate * (target_q - current_q)
            self.q_table[state][action_key] = new_q

            # 更新经验表中的最佳动作
            if state not in self.experience_table:
                self.experience_table[state] = {
                    'actions': [],
                    'rewards': [],
                    'q_values': {},
                    'visit_count': 0,
                    'best_action': last_sa['action']
                }

            self.experience_table[state]['rewards'].append(reward)
            self.experience_table[state]['q_values'][action_key] = new_q

            # 更新最佳动作
            best_action_key = max(self.q_table[state], key=self.q_table[state].get)
            self.experience_table[state]['best_action'] = self._key_to_action(best_action_key)

            # 更新性能统计
            if reward > self.best_performance:
                self.best_performance = reward

            # 记录学习曲线
            self.learning_curve.append(reward)

            if self.verbose:
                print(f"Q值更新: 状态{state} 动作{action_key[:20]}... Q: {current_q:.3f} → {new_q:.3f} (奖励: {reward:.3f})")

        # 更新Q表收敛监控
        self._update_convergence_monitoring()
    
    def _adjust_exploration_rate(self, feedback: FeedbackData):
        """动态调整探索率（ε-贪婪策略）- 增强探索版"""

        # 计算Q表增长率，如果还在发现新状态，保持较高探索率
        q_table_growth_rate = self._calculate_q_table_growth_rate()

        # 如果Q表还在快速增长，说明还有很多未探索的状态
        if q_table_growth_rate > 0.5:  # 每轮平均增长0.5个新状态
            # 保持较高探索率以发现更多状态
            self.exploration_rate = max(0.25, self.exploration_rate)
            if self.verbose:
                print(f"Q表快速增长({q_table_growth_rate:.2f}/轮)，保持高探索率")
        else:
            # 基于性能调整探索率（更保守的调整）
            if feedback.system_performance > 0.8:  # 提高阈值，减少过早收敛
                # 性能很好，适度减少探索
                self.exploration_rate = max(self.min_epsilon, self.exploration_rate * 0.9995)  # 更慢的衰减
            elif feedback.system_performance < 0.3:  # 性能很差，增加探索
                # 性能差，增加探索
                self.exploration_rate = min(0.7, self.exploration_rate * 1.1)
            else:
                # 中等性能，非常缓慢衰减
                self.exploration_rate = max(self.min_epsilon, self.exploration_rate * 0.9998)

        # 基于训练轮数的自然衰减（更温和）
        if self.training_episodes % 200 == 0:  # 进一步增加衰减间隔
            self.exploration_rate = max(self.min_epsilon, self.exploration_rate * 0.995)  # 更温和的衰减

        # 延长早期训练阶段，保持较高探索率
        if self.training_episodes < 500:  # 延长到500轮
            self.exploration_rate = max(0.3, self.exploration_rate)  # 前500轮保持至少30%探索率

        # 周期性探索增强：每1000轮增加一次探索
        if self.training_episodes % 1000 == 0 and self.training_episodes > 0:
            self.exploration_rate = min(0.4, self.exploration_rate * 1.5)
            if self.verbose:
                print(f"周期性探索增强: 探索率提升到 {self.exploration_rate:.3f}")

        if self.verbose:
            print(f"探索率调整: {self.exploration_rate:.3f} (性能: {feedback.system_performance:.3f}, Q表增长: {q_table_growth_rate:.2f})")

    def _update_convergence_monitoring(self):
        """更新Q表收敛监控"""
        current_size = len(self.q_table)
        self.q_table_size_history.append(current_size)

        # 保持历史记录在窗口大小内
        if len(self.q_table_size_history) > self.convergence_window:
            self.q_table_size_history.pop(0)

        # 检测收敛：如果最近100轮Q表大小变化很小，认为已收敛
        if len(self.q_table_size_history) >= self.convergence_window:
            recent_sizes = self.q_table_size_history[-50:]  # 最近50轮
            size_variance = max(recent_sizes) - min(recent_sizes)

            # 更严格的收敛条件：Q表大小变化很小且探索率很低且训练轮数足够多
            if size_variance <= 2 and self.exploration_rate <= self.min_epsilon and self.training_episodes > 1000:
                self.is_converged = True
                if self.verbose and not hasattr(self, '_convergence_logged'):
                    print(f"Q表已收敛: 大小={current_size}, 探索率={self.exploration_rate:.3f}, 训练轮数={self.training_episodes}")
                    self._convergence_logged = True

    def _calculate_q_table_growth_rate(self) -> float:
        """计算Q表增长率（最近20轮的平均增长）"""
        if len(self.q_table_size_history) < 20:
            return 0.0

        recent_20 = self.q_table_size_history[-20:]
        if len(recent_20) < 2:
            return 0.0

        # 计算平均增长率
        growth_rate = (recent_20[-1] - recent_20[0]) / len(recent_20)
        return max(0.0, growth_rate)

    def update_exploration_rate(self, new_rate: float):
        """更新探索率（外部调用接口）"""
        self.exploration_rate = max(self.min_epsilon, min(1.0, new_rate))
        if self.verbose:
            print(f"探索率已更新为: {self.exploration_rate:.3f}")
    
    def _action_to_key(self, action: Dict) -> str:
        """将动作转换为字符串键 - 支持组合干扰"""
        jamming_combination = action.get('jamming_combination', [])
        if jamming_combination:
            # 使用组合名称作为键的一部分
            combination_str = "+".join(sorted(jamming_combination))
        else:
            combination_str = 'None'
        return f"{action['should_jam']}_{combination_str}_{action['jamming_power']:.2f}"

    def _key_to_action(self, action_key: str) -> Dict:
        """将字符串键转换为动作 - 支持组合干扰"""
        parts = action_key.split('_', 2)
        should_jam = parts[0] == 'True'
        combination_str = parts[1] if len(parts) > 1 else 'None'
        jamming_power = float(parts[2]) if len(parts) > 2 else 0.0

        # 从组合字符串解析组合
        if should_jam and combination_str != 'None':
            jamming_combination = combination_str.split('+')
            jamming_type = " + ".join(jamming_combination)
        else:
            jamming_combination = []
            jamming_type = None

        return {
            'should_jam': should_jam,
            'jamming_combination': jamming_combination,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power
        }

    def _get_heuristic_action(self, state: Tuple) -> Dict:
        """基于启发式规则获取默认动作 - 支持组合干扰"""
        # 处理新的扩展状态特征
        if len(state) == 8:  # 包含雷达自适应信息的状态
            threat_level, freq_band, work_mode, pw_band, dist_band, speed_band, adapt_type, adapt_level = state
        else:  # 基础状态（6维）
            threat_level, freq_band, work_mode, pw_band, dist_band, speed_band = state
            adapt_type = 1  # 无自适应
            adapt_level = 1

        # 基于雷达自适应历史调整策略
        if adapt_type > 1:  # 有雷达自适应历史
            return self._get_adaptive_heuristic_action(threat_level, freq_band, adapt_type, adapt_level)

        # 基于威胁等级和其他特征选择组合干扰动作
        if threat_level <= 1:  # 极高威胁（制导雷达）- 使用强力组合
            should_jam = True
            jamming_power = 0.9
            if freq_band >= 3:  # X波段及以上，适合精确干扰
                jamming_combination = ["间歇采样转发", "灵巧噪声"]
            else:  # 低频段，使用宽带+拖引组合
                jamming_combination = ["宽带阻塞噪声", "拖引"]
            jamming_type = " + ".join(jamming_combination)
        elif threat_level <= 2:  # 高威胁（成像雷达）- 使用双重组合
            should_jam = True
            jamming_power = 0.8
            jamming_combination = ["梳状谱", "灵巧噪声"]
            jamming_type = " + ".join(jamming_combination)
        elif threat_level <= 3:  # 中等威胁（搜索转跟踪）- 使用双重组合
            should_jam = True
            jamming_power = 0.6
            jamming_combination = ["拖引", "灵巧噪声"]
            jamming_type = " + ".join(jamming_combination)
        elif threat_level <= 4:  # 低威胁（搜索雷达）- 使用单一干扰
            should_jam = True
            jamming_power = 0.4
            jamming_combination = ["宽带阻塞噪声"]
            jamming_type = " + ".join(jamming_combination)
        else:  # 极低威胁（预警雷达）- 通常不干扰
            should_jam = False
            jamming_power = 0.0
            jamming_combination = []
            jamming_type = None

        return {
            'should_jam': should_jam,
            'jamming_combination': jamming_combination,
            'jamming_type': jamming_type,
            'jamming_power': jamming_power
        }

    def _get_adaptive_heuristic_action(self, threat_level: int, freq_ghz: int,
                                     adaptation_type_code: int, adaptation_frequency: int) -> Dict:
        """基于雷达自适应历史的启发式动作"""

        # 极低威胁（威胁等级5）通常不干扰，即使有自适应历史
        if threat_level >= 5:
            return {
                'should_jam': False,
                'jamming_combination': [],
                'jamming_type': None,
                'jamming_power': 0.0
            }

        # 根据雷达自适应类型选择反制策略
        if adaptation_type_code == 1:  # large_frequency_agility
            return {
                'should_jam': True,
                'jamming_combination': ['宽带阻塞噪声', '灵巧噪声'],
                'jamming_type': '宽带阻塞噪声 + 灵巧噪声',
                'jamming_power': 0.9
            }
        elif adaptation_type_code == 2:  # medium_frequency_agility
            return {
                'should_jam': True,
                'jamming_combination': ['梳状谱', '间歇采样转发'],
                'jamming_type': '梳状谱 + 间歇采样转发',
                'jamming_power': 0.7
            }
        elif adaptation_type_code == 3:  # small_frequency_agility
            return {
                'should_jam': True,
                'jamming_combination': ['灵巧噪声'],
                'jamming_type': '灵巧噪声',
                'jamming_power': 0.8
            }
        elif adaptation_type_code == 4:  # power_control
            print(f"   应对功率控制")
            return {
                'should_jam': True,
                'jamming_combination': ['宽带阻塞噪声'],
                'jamming_type': '宽带阻塞噪声',
                'jamming_power': 1.0
            }
        elif adaptation_type_code == 5:  # prf_agility
            print(f"   应对PRF捷变")
            return {
                'should_jam': True,
                'jamming_combination': ['间歇采样转发', '梳状谱'],
                'jamming_type': '间歇采样转发 + 梳状谱',
                'jamming_power': 0.8
            }
        elif adaptation_type_code == 6:  # waveform_agility
            print(f"   应对波形捷变")
            return {
                'should_jam': True,
                'jamming_combination': ['灵巧噪声', '拖引'],
                'jamming_type': '灵巧噪声 + 拖引',
                'jamming_power': 0.8
            }
        elif adaptation_type_code == 7:  # mode_switching
            print(f"   应对模式切换")
            return {
                'should_jam': True,
                'jamming_combination': ['梳状谱', '间歇采样转发', '灵巧噪声'],
                'jamming_type': '梳状谱 + 间歇采样转发 + 灵巧噪声',
                'jamming_power': 0.8
            }
        elif adaptation_type_code == 8:  # beam_steering
            print(f"   应对波束指向变化")
            return {
                'should_jam': True,
                'jamming_combination': ['宽带阻塞噪声', '梳状谱'],
                'jamming_type': '宽带阻塞噪声 + 梳状谱',
                'jamming_power': 0.7
            }
        else:
            # 默认策略
            return {
                'should_jam': True,
                'jamming_combination': ['灵巧噪声'],
                'jamming_type': '灵巧噪声',
                'jamming_power': 0.6
            }

    def _calculate_rl_confidence(self, action: Dict, threat_data: ThreatAssessmentData) -> float:
        """计算强化学习决策的置信度 - 支持组合干扰"""
        # 基于Q值和访问次数计算置信度
        # 使用简化的状态特征（不依赖雷达数据）
        state = (
            threat_data.threat_level,
            10,  # 默认频率 10GHz
            5,   # 默认PRF 5kHz
            int(threat_data.threat_value * 10) / 10.0
        )
        action_key = self._action_to_key(action)

        base_confidence = 0.3  # 基础置信度

        if state in self.q_table and action_key in self.q_table[state]:
            q_value = self.q_table[state][action_key]
            visit_count = self.experience_table.get(state, {}).get('visit_count', 1)

            # 置信度基于Q值和经验
            q_confidence = min(0.5, abs(q_value) + 0.1 * min(visit_count / 10, 1.0))
            base_confidence = max(base_confidence, q_confidence)

        # 组合干扰的置信度调整
        jamming_combination = action.get('jamming_combination', [])
        if jamming_combination:
            combination_confidence = self._calculate_combination_confidence(jamming_combination, threat_data)
            base_confidence = max(base_confidence, combination_confidence)

        return max(0.1, min(1.0, base_confidence))

    def _calculate_combination_confidence(self, jamming_combination: list, threat_data: ThreatAssessmentData) -> float:
        """计算组合干扰的置信度"""
        if not jamming_combination:
            return 0.3  # 不干扰的基础置信度

        base_confidence = 0.4
        combination_size = len(jamming_combination)
        threat_level = threat_data.threat_level

        # 基于组合复杂度的置信度调整
        if combination_size == 1:
            complexity_bonus = 0.1
        elif combination_size == 2:
            complexity_bonus = 0.2
        elif combination_size >= 3:
            complexity_bonus = 0.3
        else:
            complexity_bonus = 0.0

        # 基于威胁匹配度的置信度调整
        if threat_level <= 2 and combination_size >= 2:  # 高威胁用复杂组合
            threat_match_bonus = 0.3
        elif threat_level == 3 and combination_size == 2:  # 中威胁用双重组合
            threat_match_bonus = 0.2
        elif threat_level >= 4 and combination_size == 1:  # 低威胁用单一干扰
            threat_match_bonus = 0.2
        else:
            threat_match_bonus = 0.0

        # 特定组合的置信度奖励
        synergy_bonus = self._calculate_combination_synergy_bonus(jamming_combination, threat_data)

        final_confidence = min(1.0, base_confidence + complexity_bonus + threat_match_bonus + synergy_bonus)
        return max(0.1, final_confidence)

    def _calculate_combination_synergy_bonus(self, jamming_combination: list, threat_data: ThreatAssessmentData) -> float:
        """计算组合协同效应置信度奖励"""
        synergy_bonus = 0.0

        # 检查是否有协同效应好的组合
        if "梳状谱" in jamming_combination and "间歇采样转发" in jamming_combination:
            synergy_bonus += 0.15  # 梳状谱+间歇采样对成像雷达效果好

        if "拖引" in jamming_combination and "灵巧噪声" in jamming_combination:
            synergy_bonus += 0.15  # 拖引+灵巧噪声对制导雷达效果好

        if "宽带阻塞噪声" in jamming_combination and len(jamming_combination) >= 2:
            synergy_bonus += 0.1   # 宽带噪声作为基础干扰的协同效应

        return min(0.3, synergy_bonus)

    def _calculate_rl_priority(self, action: Dict, threat_data: ThreatAssessmentData) -> int:
        """计算强化学习决策的优先级"""
        base_priority = 6 - threat_data.threat_level  # 威胁等级越高，优先级越高

        # 根据干扰功率调整优先级
        if action['jamming_power'] > 0.8:
            base_priority = min(5, base_priority + 1)

        return max(1, base_priority)

    def _calculate_rl_duration(self, action: Dict, threat_data: ThreatAssessmentData) -> float:
        """计算强化学习决策的持续时间"""
        base_duration = 5.0

        # 根据威胁等级调整持续时间
        threat_factor = (6 - threat_data.threat_level) * 1.5

        # 根据干扰功率调整持续时间
        power_factor = action['jamming_power'] * 2.0

        duration = base_duration + threat_factor + power_factor
        return min(15.0, max(2.0, duration))
    
    def _update_decision_rules(self, feedback: FeedbackData):
        """基于反馈更新决策规则"""
        # 这里可以实现基于反馈的规则调整
        # 例如：根据效果调整威胁阈值、功率范围等

        # 简单的自适应规则调整示例
        if hasattr(self, 'decision_rules'):
            if feedback.system_performance > 0.8:
                # 性能好，可以适当降低干扰强度
                self.decision_rules['power_adjustment_factor'] = 0.9
            elif feedback.system_performance < 0.3:
                # 性能差，需要增强干扰
                self.decision_rules['power_adjustment_factor'] = 1.1
            else:
                self.decision_rules['power_adjustment_factor'] = 1.0
        else:
            self.decision_rules = {
                'power_adjustment_factor': 1.0,
                'threat_sensitivity': 1.0
            }
    
    def _update_decision_stats(self, decision: JammingDecisionData):
        """更新决策统计"""
        self.decision_stats['total_decisions'] += 1
        
        # 更新平均置信度
        total = self.decision_stats['total_decisions']
        current_avg = self.decision_stats['average_confidence']
        self.decision_stats['average_confidence'] = (
            (current_avg * (total - 1) + decision.confidence) / total
        )
    
    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        return {
            'training_episodes': self.training_episodes,
            'cumulative_reward': self.cumulative_reward,
            'best_performance': self.best_performance,
            'current_exploration_rate': self.exploration_rate,
            'q_table_size': len(self.q_table),
            'experience_states': len(self.experience_table),
            'exploration_ratio': self.decision_stats['exploration_decisions'] / max(1, self.decision_stats['total_decisions']),
            'average_reward': self.cumulative_reward / max(1, self.training_episodes),
            'learning_curve': self.learning_curve[-10:] if self.learning_curve else [],
            'is_converged': self.is_converged,
            'q_table_growth_rate': self._calculate_q_table_growth_rate()
        }

    def reset_learning(self):
        """重置学习状态"""
        self.q_table.clear()
        self.experience_table.clear()
        self.state_action_history.clear()
        self.training_episodes = 0
        self.cumulative_reward = 0.0
        self.best_performance = 0.0
        self.learning_curve.clear()
        self.exploration_rate = 0.3

        # 重置决策统计
        self.decision_stats = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'average_confidence': 0.0,
            'exploration_decisions': 0,
            'exploitation_decisions': 0
        }

        print("强化学习状态已重置")

    def save_learning_model(self, filepath: str) -> bool:
        """保存学习模型"""
        try:
            import pickle
            model_data = {
                'q_table': self.q_table,
                'experience_table': self.experience_table,
                'learning_stats': self.get_learning_statistics(),
                'parameters': {
                    'learning_rate': self.learning_rate,
                    'exploration_rate': self.exploration_rate,
                    'discount_factor': self.discount_factor
                }
            }

            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)

            print(f"学习模型已保存到: {filepath}")
            return True
        except Exception as e:
            print(f"保存学习模型失败: {e}")
            return False

    def load_learning_model(self, filepath: str) -> bool:
        """加载学习模型"""
        try:
            import pickle

            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)

            self.q_table = model_data.get('q_table', {})
            self.experience_table = model_data.get('experience_table', {})

            # 恢复参数
            params = model_data.get('parameters', {})
            self.learning_rate = params.get('learning_rate', self.learning_rate)
            self.exploration_rate = params.get('exploration_rate', self.exploration_rate)
            self.discount_factor = params.get('discount_factor', self.discount_factor)

            print(f"学习模型已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载学习模型失败: {e}")
            return False

    def _generate_combination_jamming_params(self, jamming_combination: list,
                                           radar_data: SimplifiedRadarData,
                                           threat_data: ThreatAssessmentData) -> Tuple[Dict, Dict]:
        """生成组合干扰参数"""
        base_params = {
            'target_frequency': radar_data.frequency,
            'bandwidth': 1e6,
            'modulation': 'combination',
            'combination': jamming_combination,
            'combination_size': len(jamming_combination)
        }

        # 为每种干扰类型生成具体参数
        combined_specific_params = {}

        for jamming_type in jamming_combination:
            if jamming_type == "梳状谱":
                combined_specific_params['comb_spectrum'] = self._generate_comb_spectrum_params(radar_data, threat_data)
            elif jamming_type == "间歇采样转发":
                combined_specific_params['intermittent_sampling'] = self._generate_intermittent_sampling_params(radar_data, threat_data)
            elif jamming_type == "宽带阻塞噪声":
                combined_specific_params['broadband_noise'] = self._generate_broadband_noise_params(radar_data, threat_data)
            elif jamming_type == "灵巧噪声":
                combined_specific_params['smart_noise'] = self._generate_smart_noise_params(radar_data, threat_data)
            elif jamming_type == "拖引":
                combined_specific_params['deception'] = self._generate_deception_params(radar_data, threat_data)

        # 计算组合干扰的协同效应参数
        synergy_factor = self._calculate_synergy_factor(jamming_combination, threat_data)
        base_params['synergy_factor'] = synergy_factor

        return base_params, combined_specific_params

    def _set_combination_specific_params(self, decision: JammingDecisionData,
                                       jamming_combination: list,
                                       combined_specific_params: Dict):
        """设置组合干扰的具体参数"""
        # 为决策对象设置每种干扰类型的参数
        for jamming_type in jamming_combination:
            if jamming_type == "梳状谱" and 'comb_spectrum' in combined_specific_params:
                decision.comb_spectrum_params = combined_specific_params['comb_spectrum']
            elif jamming_type == "间歇采样转发" and 'intermittent_sampling' in combined_specific_params:
                decision.intermittent_sampling_params = combined_specific_params['intermittent_sampling']
            elif jamming_type == "宽带阻塞噪声" and 'broadband_noise' in combined_specific_params:
                decision.broadband_noise_params = combined_specific_params['broadband_noise']
            elif jamming_type == "灵巧噪声" and 'smart_noise' in combined_specific_params:
                decision.smart_noise_params = combined_specific_params['smart_noise']
            elif jamming_type == "拖引" and 'deception' in combined_specific_params:
                decision.deception_params = combined_specific_params['deception']

    def _calculate_synergy_factor(self, jamming_combination: list, threat_data: ThreatAssessmentData) -> float:
        """计算组合干扰的协同效应因子"""
        if len(jamming_combination) <= 1:
            return 1.0  # 单一干扰无协同效应

        synergy_factor = 1.0

        # 经典协同组合
        if "梳状谱" in jamming_combination and "间歇采样转发" in jamming_combination:
            synergy_factor += 0.3  # 梳状谱+间歇采样对成像雷达效果好

        if "拖引" in jamming_combination and "灵巧噪声" in jamming_combination:
            synergy_factor += 0.25  # 拖引+灵巧噪声对制导雷达效果好

        if "宽带阻塞噪声" in jamming_combination and len(jamming_combination) >= 2:
            synergy_factor += 0.2   # 宽带噪声作为基础干扰的协同效应

        # 三重组合的额外协同奖励
        if len(jamming_combination) >= 3:
            if "宽带阻塞噪声" in jamming_combination and "梳状谱" in jamming_combination and "间歇采样转发" in jamming_combination:
                synergy_factor += 0.4  # 全频谱覆盖组合
            elif "拖引" in jamming_combination and "灵巧噪声" in jamming_combination and "梳状谱" in jamming_combination:
                synergy_factor += 0.35  # 运动参数欺骗组合

        # 避免过度复杂化惩罚
        if len(jamming_combination) > 3:
            synergy_factor *= 0.9  # 过于复杂的组合可能效果不佳

        return min(2.0, max(1.0, synergy_factor))

    def format_standard_output(self, decision: JammingDecisionData, threat_data: ThreatAssessmentData) -> str:
        """
        格式化标准输出：威胁等级 干扰个数 干扰1编号 干扰1参数 干扰2编号 干扰2参数 ...
        """
        # 获取威胁等级
        threat_level = threat_data.threat_level

        # 获取干扰组合
        jamming_combination = decision.jamming_params.get('combination', [])
        jamming_count = len(jamming_combination)

        # 构建输出字符串
        output_parts = [str(threat_level), str(jamming_count)]

        # 为每个干扰类型添加编号和参数（不输出类型名称）
        for jamming_type in jamming_combination:
            # 获取干扰编号
            jamming_id = self.jamming_type_mapping.get(jamming_type, -1)

            # 只添加干扰编号
            output_parts.append(str(jamming_id))

            # 生成该干扰类型的详细参数
            params = self._generate_detailed_jamming_params(jamming_type, decision, threat_data)
            output_parts.extend(params)

        return " ".join(output_parts)

    def _generate_detailed_jamming_params(self, jamming_type: str, decision: JammingDecisionData, threat_data: ThreatAssessmentData) -> list:
        """为特定干扰类型生成详细参数列表"""
        if jamming_type == "梳状谱":
            # 梳状谱参数：梳状谱个数(1-8), 频偏1(kHz), 闪烁周期1(us), 闪烁保持时间1(us)
            spectrum_count = min(8, max(1, 6 - threat_data.threat_level))
            freq_offset_1 = 100.0  # kHz
            flicker_period_1 = 2.0  # us
            flicker_hold_time_1 = 1.5  # us

            params = [str(spectrum_count), str(freq_offset_1), str(flicker_period_1), str(flicker_hold_time_1)]

            # 如果有多个梳状谱，添加更多参数
            for i in range(2, spectrum_count + 1):
                freq_offset = 100.0 + (i-1) * 50.0  # kHz
                flicker_period = 2.0 + (i-1) * 0.5  # us
                flicker_hold_time = 1.5 + (i-1) * 0.3  # us
                params.extend([str(freq_offset), str(flicker_period), str(flicker_hold_time)])

            return params

        elif jamming_type == "间歇采样转发":
            # 间歇采样转发参数：重复转发时间间隔(us), 间歇采样开关(0/1), 间歇采样周期(us),
            # 间歇采样宽度(us), 干扰覆盖距离范围(m), 脉冲采样长度(us)
            repeat_interval = 125.0  # us
            sampling_switch = 1  # 1: on
            sampling_period = 1.5  # us
            sampling_width = 0.8  # us
            coverage_range = 60000.0  # m
            pulse_sampling_length = 1.2  # us

            return [str(repeat_interval), str(sampling_switch), str(sampling_period),
                   str(sampling_width), str(coverage_range), str(pulse_sampling_length)]

        elif jamming_type == "宽带阻塞噪声":
            # 宽带阻塞噪声参数：噪声带宽选择(0-20)
            if threat_data.threat_level <= 2:
                bandwidth_selection = 15  # 500 MHz
            elif threat_data.threat_level <= 3:
                bandwidth_selection = 11  # 100 MHz
            else:
                bandwidth_selection = 8   # 10 MHz

            return [str(bandwidth_selection)]

        elif jamming_type == "灵巧噪声":
            # 灵巧噪声参数：噪声带宽选择(0-20), 噪声源选择(1-3), 多普勒闪烁模式(1-2),
            # 闪烁保持时间(us), 闪烁消失时间(us), 多普勒噪声带宽(kHz), 多普勒噪声跳变周期(kHz)
            bandwidth_selection = min(20, max(0, 10 + (3 - threat_data.threat_level) * 2))
            noise_source = 1  # 1: 高斯噪声
            doppler_flicker_mode = 1  # 1: 固定闪烁
            flicker_hold_time = 2.0  # us
            flicker_disappear_time = 1.0  # us
            doppler_noise_bandwidth = 50.0  # kHz
            doppler_noise_jump_period = 10.0  # kHz

            return [str(bandwidth_selection), str(noise_source), str(doppler_flicker_mode),
                   str(flicker_hold_time), str(flicker_disappear_time),
                   str(doppler_noise_bandwidth), str(doppler_noise_jump_period)]

        elif jamming_type == "拖引":
            # 拖引参数：速拖速度(m/s), 速拖加速度(m/s²), 距拖速度(m/s), 距拖加速度(m/s²),
            # 捕获时间(s), 拖引时间(s), 保持时间(s), 消失时间(s)
            velocity_drag_speed = 300.0 + threat_data.threat_level * 50.0  # m/s
            velocity_drag_acceleration = 50.0 - threat_data.threat_level * 10.0  # m/s²
            range_drag_speed = 200.0 + threat_data.threat_level * 30.0  # m/s
            range_drag_acceleration = 30.0 - threat_data.threat_level * 5.0  # m/s²
            capture_time = 1.0 + threat_data.threat_level * 0.2  # s
            drag_time = 2.0 + threat_data.threat_level * 0.5  # s
            hold_time = 1.5 + threat_data.threat_level * 0.3  # s
            disappear_time = 0.5 + threat_data.threat_level * 0.1  # s

            return [str(velocity_drag_speed), str(velocity_drag_acceleration),
                   str(range_drag_speed), str(range_drag_acceleration),
                   str(capture_time), str(drag_time), str(hold_time), str(disappear_time)]

        else:
            return ["0"]  # 默认参数

    def _get_mode_code(self, mode_name: str) -> int:
        """将模式名称转换为数字代码"""
        mode_codes = {"预警": 0, "搜索": 1, "跟踪": 2, "制导": 3}
        return mode_codes.get(mode_name, 1)  # 默认返回搜索模式
