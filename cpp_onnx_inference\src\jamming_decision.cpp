#include "jamming_decision.h"
#include <string.h>

// 干扰决策函数
RadarErrorCode make_jamming_decision(const RadarParams* radar_params,
                                   const ThreatAssessment* threat_assessment,
                                   JammingDecision* decision) {
    if (!radar_params || !threat_assessment || !decision) {
        return RADAR_ERROR_INVALID_PARAM;
    }

    // 清空决策结果
    memset(decision, 0, sizeof(JammingDecision));

    // 复制威胁评估结果
    decision->threat_value = threat_assessment->threat_value;
    decision->threat_level = threat_assessment->threat_level;
    decision->confidence = threat_assessment->confidence;

    // 根据威胁等级选择干扰策略
    int jamming_type = select_jamming_strategy(threat_assessment->threat_value,
                                             threat_assessment->threat_level);

    if (jamming_type != JAMMING_TYPE_NONE) {
        decision->jamming_count = 1;
        decision->jamming_types[0] = jamming_type;

        // 计算干扰参数
        calculate_jamming_parameters(radar_params, jamming_type,
                                   decision->jamming_params[0]);
    }

    decision->status = 0;
    strcpy(decision->error_message, "Success");

    return RADAR_SUCCESS;
}

// 选择干扰策略
int select_jamming_strategy(double threat_value, int threat_level) {
    if (threat_level >= THREAT_LEVEL_HIGH) {
        return JAMMING_TYPE_DECEPTION;
    } else if (threat_level >= THREAT_LEVEL_MEDIUM) {
        return JAMMING_TYPE_NOISE;
    } else if (threat_level >= THREAT_LEVEL_LOW) {
        return JAMMING_TYPE_BARRAGE;
    }

    return JAMMING_TYPE_NONE;
}

// 计算干扰参数
void calculate_jamming_parameters(const RadarParams* radar_params,
                                int jamming_type,
                                double* params) {
    if (!radar_params || !params) {
        return;
    }

    // 根据干扰类型计算参数
    switch (jamming_type) {
        case JAMMING_TYPE_NOISE:
            params[0] = radar_params->frequency;
            params[1] = radar_params->power * 0.1;
            break;

        case JAMMING_TYPE_DECEPTION:
            params[0] = radar_params->frequency + 50;
            params[1] = radar_params->power * 0.05;
            params[2] = radar_params->prt * 0.9;
            break;

        case JAMMING_TYPE_BARRAGE:
            params[0] = radar_params->frequency - 100;
            params[1] = radar_params->frequency + 100;
            params[2] = radar_params->power * 0.2;
            break;

        default:
            // 默认参数
            params[0] = radar_params->frequency;
            params[1] = radar_params->power * 0.01;
            break;
    }
}