#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX转换依赖包下载脚本
下载所有ONNX转换和推理阶段需要的Python包到本地目录
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def create_directory(path):
    """创建目录"""
    Path(path).mkdir(parents=True, exist_ok=True)
    print(f"创建目录: {path}")

def download_packages(requirements_file, download_dir, index_url=None):
    """
    下载依赖包到指定目录
    
    Args:
        requirements_file: requirements.txt文件路径
        download_dir: 下载目录
        index_url: PyPI镜像源URL
    """
    print(f"开始下载ONNX依赖包...")
    print(f"需求文件: {requirements_file}")
    print(f"下载目录: {download_dir}")
    
    # 创建下载目录
    create_directory(download_dir)
    
    # 构建pip下载命令
    cmd = [
        sys.executable, "-m", "pip", "download",
        "-r", requirements_file,
        "-d", download_dir,
        "--no-deps"  # 不下载依赖的依赖
    ]
    
    # 添加镜像源
    if index_url:
        cmd.extend(["-i", index_url])
        print(f"使用镜像源: {index_url}")
    
    try:
        # 执行下载
        print("执行下载命令...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("下载成功!")
        print(result.stdout)
        
        # 统计下载的包数量
        downloaded_files = list(Path(download_dir).glob("*.whl")) + list(Path(download_dir).glob("*.tar.gz"))
        print(f"共下载 {len(downloaded_files)} 个包文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"下载失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def download_onnx_tools(download_dir):
    """下载ONNX相关工具"""
    print("下载ONNX专用工具...")
    
    # ONNX核心包
    onnx_packages = [
        "onnx>=1.10.0",
        "onnxruntime>=1.8.0", 
        "onnxruntime-gpu>=1.8.0",
        "onnx-simplifier>=0.3.6",
        "onnxoptimizer>=0.2.6",
        "onnx-tools>=0.1.0",
        "netron>=5.0.0"
    ]
    
    for package in onnx_packages:
        cmd = [
            sys.executable, "-m", "pip", "download",
            package,
            "-d", download_dir
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            print(f"下载成功: {package}")
        except subprocess.CalledProcessError as e:
            print(f"下载失败: {package} - {e}")

def create_install_script(download_dir):
    """创建安装脚本"""
    script_content = f"""#!/bin/bash
# ONNX依赖包安装脚本

echo "开始安装ONNX依赖包..."

# 设置包目录
PACKAGE_DIR="{download_dir}"

# 安装核心ONNX包
pip install --find-links $PACKAGE_DIR --no-index onnx
pip install --find-links $PACKAGE_DIR --no-index onnxruntime
pip install --find-links $PACKAGE_DIR --no-index torch
pip install --find-links $PACKAGE_DIR --no-index numpy

# 安装ONNX工具
pip install --find-links $PACKAGE_DIR --no-index onnx-simplifier
pip install --find-links $PACKAGE_DIR --no-index onnxoptimizer
pip install --find-links $PACKAGE_DIR --no-index netron

# 安装其他依赖
pip install --find-links $PACKAGE_DIR --no-index -r onnx_requirements.txt

echo "ONNX依赖包安装完成!"
"""
    
    script_path = os.path.join(download_dir, "install_onnx_packages.sh")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    print(f"创建安装脚本: {script_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载ONNX转换依赖包')
    parser.add_argument('--requirements', '-r', type=str, 
                       default='onnx_requirements.txt',
                       help='requirements文件路径')
    parser.add_argument('--download-dir', '-d', type=str,
                       default='onnx_packages',
                       help='下载目录')
    parser.add_argument('--index-url', '-i', type=str,
                       default='https://pypi.tuna.tsinghua.edu.cn/simple/',
                       help='PyPI镜像源URL')
    parser.add_argument('--include-tools', action='store_true',
                       help='包含ONNX专用工具')
    parser.add_argument('--create-script', action='store_true',
                       help='创建安装脚本')
    
    args = parser.parse_args()
    
    # 检查requirements文件
    if not os.path.exists(args.requirements):
        print(f"错误: requirements文件不存在: {args.requirements}")
        return 1
    
    # 下载包
    success = download_packages(args.requirements, args.download_dir, args.index_url)
    
    if success and args.include_tools:
        # 下载ONNX专用工具
        download_onnx_tools(args.download_dir)
    
    if success and args.create_script:
        # 创建安装脚本
        create_install_script(args.download_dir)
    
    if success:
        print(f"\nONNX依赖包下载完成!")
        print(f"包文件位置: {os.path.abspath(args.download_dir)}")
        print(f"\n使用方法:")
        print(f"1. 离线安装: pip install --find-links {args.download_dir} --no-index <package_name>")
        print(f"2. 批量安装: pip install --find-links {args.download_dir} --no-index -r {args.requirements}")
        if args.create_script:
            print(f"3. 脚本安装: bash {args.download_dir}/install_onnx_packages.sh")
        return 0
    else:
        print("下载失败!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
